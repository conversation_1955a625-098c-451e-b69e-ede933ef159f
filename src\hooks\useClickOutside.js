import { useEffect, useRef } from 'react'

function useClickOutside(callback) {
  const ref = useRef()

  useEffect(() => {
    function handleClickOutside(event) {
      if (ref.current && !ref.current.contains(event.target)) {
        callback()
      }
    }

    document.addEventListener('mousedown', handleClickOutside, true)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true)
    }
  }, [callback])

  return ref
}

export default useClickOutside
