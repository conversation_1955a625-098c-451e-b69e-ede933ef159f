const generateTreeData = (key, schema = []) => {
  return schema.filter(Boolean).map((item) => {
    // console.log('schema item---------------', item)
    const parentKey =
      item.type?.startsWith('array') ? `${key}.${item.name}.[*]` : `${key}.${item.name}`
    // 处理每个节点
    const treeNode = {
      title: item.name,
      key: parentKey,
      type: item.type
    }

    // 处理子节点
    if (item.schema && item.schema.length > 0) {
      treeNode.children = item.schema.filter(Boolean).map((child) => {
        // console.log('child---------------', child)
        // 每个子节点
        const childKey =
          child.type?.startsWith('array')
            ? `${parentKey}.${child.name}.[*]`
            : `${parentKey}.${child.name}`
        const childNode = {
          title: `${child.name}`,
          key: child<PERSON>ey,
          type: child.type
        }

        // 如果是对象类型，递归生成子节点
        if (
          (child.type === 'object' || child.type === 'array<object>') &&
          child.schema &&
          child.schema.length > 0
        ) {
          childNode.children = generateTreeData(childKey, child.schema)
        }

        return childNode
      })
    }

    return treeNode
  })
}

export default generateTreeData
