function useInputDataValidate(treeData = []) {
  const isExisitInData = (value, data) => {
    for (const node of data) {
      if (node.key === value) return true
      if (node.children) {
        const found = isExisitInData(value, node.children)
        if (found) return true
      }
    }
    return false
  }
  const validateInputData = (rule, value, notRequired) => {
    if (value) {
      if (value.ref === 'ref') {
        // return Promise.reject('引用或输入不可为空')
        // 判断在不在treeData中
        if (!value.path) {
          if (notRequired) {
            return Promise.resolve()
          } else {
            return Promise.reject('引用或输入不可为空')
          }
        } else {
          const isExist = isExisitInData(value.path, treeData)
          if (!isExist) {
            return Promise.reject('引用变量不存在')
          }
        }
      } else {
        if (!value.path) {
          if (notRequired) {
            return Promise.resolve()
          } else {
            return Promise.reject('引用或输入不可为空')
          }
        }
      }
    } else {
      // notRequired 无值，表示必填
      if (notRequired) {
        return Promise.resolve()
      } else {
        return Promise.reject('引用或输入不可为空')
      }
    }
    return Promise.resolve()
  }

  const validateChoiceInputData = (rule, value) => {
    if (value) {
      const isExist = isExisitInData(value, treeData)
      if (!isExist) {
        return Promise.reject('引用变量不存在')
      }
    } else {
      return Promise.reject('参数不可为空')
    }
    return Promise.resolve()
  }
  return { validateInputData, validateChoiceInputData }
}
export default useInputDataValidate
