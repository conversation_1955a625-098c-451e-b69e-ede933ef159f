import styles from './style.module.scss'

import {
  CheckCircleOutlined,
  DownOutlined,
  InfoCircleOutlined,
  LoadingOutlined,
  UpOutlined
} from '@ant-design/icons'
import { useEffect, useState } from 'react'
import { useCallback } from 'react'
import MarkdownIt from 'markdown-it'
import { Spin, Tag, message } from 'antd'
import eventBus from '@/utils/eventBus'

import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import CopyIcon from '@/assets/svgs/copy.svg?react'
import { CopyToClipboard } from 'react-copy-to-clipboard'

const md = new MarkdownIt({ breaks: true })

function DebugData({ id }) {
  const [collapsed, setCollaped] = useState(false)
  const [outputCollapsed, setOutputCollapsed] = useState(false)
  const [originOutputCollapsed, setOriginOutputCollapsed] = useState(true)
  const [chatList, setChatList] = useState([])
  // 正在运行
  const [nodeStatus, setNodeStatus] = useState(false)

  const [time, setTime] = useState(0)

  const [timeFirst, setTimeFirst] = useState(0)

  useEffect(() => {
    // 订阅事件
    eventBus.on('DEBUG_DATA', handler)
    // 清理订阅
    return () => {
      eventBus.off('DEBUG_DATA', handler)
    }
  }, [])

  const handler = (payload = {}) => {
    if (payload.id === id) {
      setNodeStatus(payload.debugData?.nodeStatus)
      setTime(payload.debugData?.time / 1000)
      setTimeFirst(payload.debugData?.timeFirst / 1000)
      consumeData(payload.debugData)
    }
  }

  const consumeData = (data) => {
    if (data?.logId) {
      setChatList((list) => {
        const index = list.findIndex((item) => item.logId === data.logId)
        if (index === -1) {
          return list.concat([data])
        } else {
          return list.map((item) => {
            return {
              ...item,
              text: item.logId === data.logId ? `${item.text || ''}${data.text || ''}` : item.text
            }
          })
        }
      })
    } else {
      setChatList([])
    }
  }
  const toggleCollapsed = useCallback(
    (e) => {
      e.stopPropagation()
      setCollaped((c) => !c)
    },
    [collapsed]
  )

  const toggleOutputCollapsed = useCallback(
    (e) => {
      e.stopPropagation()
      setOutputCollapsed((c) => !c)
    },
    [outputCollapsed]
  )

  const toggleOriginOutputCollapsed = useCallback(
    (e) => {
      e.stopPropagation()
      setOriginOutputCollapsed((c) => !c)
    },
    [originOutputCollapsed]
  )

  const onInfoClick = (e) => {
    e.stopPropagation()
  }

  const handleWheel = useCallback((e) => {
    e.stopPropagation()
  }, [])

  return (
    <>
      {chatList && chatList.length > 0 && (
        <div className={`${styles.debug} nodrag`} onClick={onInfoClick}>
          <div className={styles.debugTitle}>
            {nodeStatus === true ? (
              <div className={styles.debugTitleLeft}>
                <CheckCircleOutlined className="text-[--flow-success-color] mr-1" />
                <span className="mr-1">运行成功</span>
                {timeFirst ? (
                  <Tag color="blue" bordered={false}>
                    首响：{timeFirst}s
                  </Tag>
                ) : null}
                {time ? (
                  <Tag color="success" bordered={false}>
                    尾响：{time}s
                  </Tag>
                ) : null}
              </div>
            ) : null}
            {nodeStatus === false ? (
              <div className={styles.debugTitleLeft}>
                <Spin
                  style={{ marginRight: 4 }}
                  indicator={
                    <LoadingOutlined
                      style={{
                        fontSize: 16
                      }}
                      spin
                    />
                  }
                />
                <span>运行中</span>
              </div>
            ) : null}
            {nodeStatus === 'error' ? (
              <div className={styles.debugTitleLeft}>
                <InfoCircleOutlined className="text-[rgba(255,161,84,1)] mr-1" />
                <span>运行失败</span>
              </div>
            ) : null}
            <div className={`${styles.debugTitleRight} nodrag`} onClick={toggleCollapsed}>
              {/* <span>展开结果</span> */}
              <IconArrow
                style={{ fontSize: 20, transform: collapsed ? 'rotate(0deg)' : 'rotate(180deg)' }}
              ></IconArrow>
            </div>
          </div>

          {!collapsed ? (
            <>
              {chatList.length > 1 && (
                <div className="text-[--flow-desc-color] mt-1">开启了{chatList.length}路子流程</div>
              )}
               <div className={styles.label}>
                <IconArrow
                  style={{
                    fontSize: 16,
                    cursor: 'pointer',
                    transform: originOutputCollapsed ? 'rotate(-90deg)' : 'rotate(0deg)'
                  }}
                  onClick={toggleOriginOutputCollapsed}
                />
                &nbsp;<span>原始生成</span>{' '}
                {chatList.length === 1 && (
                  <CopyToClipboard
                    text={chatList[0].text || ''}
                    onCopy={() => message.success('拷贝成功')}
                  >
                    <CopyIcon onClick={(e) => e.stopPropagation()}  style={{cursor: 'pointer'}}/>
                  </CopyToClipboard>
                )}
              </div>
              {!originOutputCollapsed ? (
                <div className={`${styles.debugContent} nodrag`} onWheelCapture={handleWheel}>
                  {chatList.map((item) => {
                    return (
                      <div
                        className={styles.debugCell}
                        key={item.logId}
                        dangerouslySetInnerHTML={{ __html: item.text || '' }}
                      ></div>
                    )
                  })}
                </div>
              ) : null}
              <div className={styles.label}>
                <IconArrow
                  style={{
                    fontSize: 16,
                    cursor: 'pointer',
                    transform: outputCollapsed ? 'rotate(-90deg)' : 'rotate(0deg)'
                  }}
                  onClick={toggleOutputCollapsed}
                />
                &nbsp;<span>节点输出</span>{' '}
                {chatList.length === 1 && (
                  <CopyToClipboard
                    text={(chatList[0].text || '')}
                    onCopy={() => message.success('拷贝成功')}
                   
                  >
                    <CopyIcon onClick={(e) => e.stopPropagation()}  style={{cursor: 'pointer'}}/>
                  </CopyToClipboard>
                )}
              </div>
              {!outputCollapsed ? (
                <div className={`${styles.debugContent} nodrag`} onWheelCapture={handleWheel}>
                  {chatList.map((item) => {
                    return (
                      <div
                        className={styles.debugCell}
                        key={item.logId}
                        dangerouslySetInnerHTML={{ __html: md.render(item.text || '') }}
                      ></div>
                    )
                  })}
                </div>
              ) : null}
             
            </>
          ) : null}
        </div>
      )}
    </>
  )
}
export default DebugData
