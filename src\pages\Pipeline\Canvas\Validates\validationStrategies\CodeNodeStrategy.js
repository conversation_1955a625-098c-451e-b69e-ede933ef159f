import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import Schema from 'async-validator'
import { getTreeData } from '@/hooks/getTreeData'
import { validateNameRecursive } from './validateNameRecursive'
import useInputDataValidate from '@/hooks/useInputDataValidate'

const validateNodeWithRules = async (node, rules, nodes, edges) => {
  const treeData = getTreeData(node, nodes, edges)
  const { validateInputData } = useInputDataValidate(treeData)

  const inputKeyValidate = (node) => {
    // 校验该变量是否被 提示词所使用

    const validateInputKey = (rule, value) => {
      // 官方大模型能力，没有提示词，不需要校验该项
      
      return Promise.resolve()
    }
    return validateInputKey
  }

  const validateInputKey = inputKeyValidate(node)

  let errors = []
  for (const field in rules) {
    // 针对 field  是否是output等特殊类型
    if (field === 'output') {
      // 这里为方便处理，且考虑项目中实际，只对name进行校验
      for (const [index, data] of node.data?.[field]?.entries()) {
        const dataErrors = await validateNameRecursive(data, index, node.data?.[field])
        errors.push(...dataErrors)
      }
    } else {
      const rule =
        field === 'input' ? rules[field](validateInputData, validateInputKey) : rules[field]
      const validator = new Schema({ [field]: rule })
      try {
        await validator.validate({ [field]: node.data?.[field] })
      } catch ({ errors: validationErrors }) {
        if (validationErrors) {
          errors.push(...validationErrors)
        }
      }
    }
  }
  return errors
}

export const CodeNodeStrategy = async (node, nodes, edges) => {
  // 大模型节点需要检查是否为官方（模版），官方的不需要校验 某些字段（prompt， systemPrompt没有）
  const rule = getRules(NODETYPES.CODE)
  const errors = await validateNodeWithRules(node, rule, nodes, edges)
  return errors
}
