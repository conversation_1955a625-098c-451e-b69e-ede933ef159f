import { Input } from 'antd'
import { useEffect, useState } from 'react'

function with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(WrappedComponent) {
  return function BlurInputWrapper({ onChange, onBlur, value, ...rest }) {
    const [val, setVal] = useState(value)
    // 监听外部 value 的变化，更新内部状态
    useEffect(() => {
      setVal(value)
    }, [value])

    const handleChange = (e) => {
      setVal(e.target.value) // 更新内部状态
    }

    const handleBlur = (e) => {
      if (onChange) {
        onChange(e) // 只在 blur 时触发外部 onChange
      }
    }

    return <WrappedComponent {...rest} value={val} onBlur={handleBlur} onChange={handleChange} />
  }
}

// 使用高阶组件包装 Input 和 Input.TextArea
const BlurInput = withBlurHandler(Input)
const BlurTextArea = withBlurHandler(Input.TextArea)

export { BlurInput, BlurTextArea }
