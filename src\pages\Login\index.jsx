import { useNavigate } from 'react-router-dom'
import { Button, Checkbox, Form, Input, Flex, message } from 'antd'
import md5 from 'md5-js'
import ajax from '@/utils/http'

// import React from 'react';

export default function Login() {
  const navigate = useNavigate()
  const onFinish = (values) => {
    console.log('Success:', values)
    const url = '/auth/userLogin'
    const data = {
      authType: 2,
      password: md5(values.password),
      userName: values.username
    }
    ajax({
      url,
      data,
      method: 'post',
      baseURL: '/api/v1'
    }).then((res) => {
      if (res.data.code === 0) {
        message.success('登录成功')
        navigate('/')
      } else {
        message.error('账号或密码错误')
      }
    })
  }
  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }
  return (
    <div className="pt-10">
      <h1 className="text-center font-bold text-[20px] mb-10">交互定制开发平台</h1>
      <Flex justify="center" align="center">
        <Form
          name="basic"
          labelCol={{
            span: 8
          }}
          wrapperCol={{
            span: 16
          }}
          style={{
            maxWidth: 600
          }}
          initialValues={{
            remember: true
          }}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item
            label="账号"
            name="username"
            rules={[
              {
                required: true,
                message: '请输入账号!'
              }
            ]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[
              {
                required: true,
                message: '请输入密码!'
              }
            ]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item
            wrapperCol={{
              offset: 8,
              span: 16
            }}
          >
            <Button type="primary" htmlType="submit">
              登录
            </Button>
          </Form.Item>
        </Form>
      </Flex>
    </div>
  )
}
