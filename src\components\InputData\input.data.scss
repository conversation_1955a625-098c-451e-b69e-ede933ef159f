.x-input-data {
  position: relative;
  display: flex;
  align-items: stretch;
  min-height: 32px;
  height: 100%;

  .x-input-data-tag {
    position: absolute;
    z-index: 999;
    display: flex;
    flex-wrap: wrap;
    top: 50%;
    transform: translateY(-50%);
    &-inner {
      display: flex;
      align-items: center;
    }
    &-inner-content {
      max-width: 130px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
    }
  }

  .ant-input:hover {
    border-color: #d9d9d9 !important;
    border-right-width: 1px !important;
  }

  .ant-input-outlined {
    padding: 1px 11px;
  }
}

.x-input-data-popover {
  .ant-cascader-menus {
    display: flex;
    flex-direction: row-reverse;
    right: 0;
  }

  .ant-cascader-menu {
    /*transform: translateX(100%);*/
  }

  .ant-popover-content {
    .ant-popover-inner {
      padding: 0;
    }
  }
}

.x-input-dropdown-overlay {
  max-height: 100px;
  overflow: auto;
}
