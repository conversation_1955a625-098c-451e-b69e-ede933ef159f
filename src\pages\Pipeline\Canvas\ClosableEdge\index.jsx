import React from 'react'
import { BaseEdge, EdgeLabelRenderer, getBezierPath, useReactFlow } from '@xyflow/react'
import styles from './style.module.scss'
import { useState } from 'react'
import useStore from '@/store.js'

export default function CustomEdge({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd
}) {
  const { setEdges } = useReactFlow()
  const [isHovered, setIsHovered] = useState(false)
  const { debouncedSetDirty } = useStore()

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition
  })

  const onEdgeClick = () => {
    setEdges((edges) => edges.filter((edge) => edge.id !== id))
    debouncedSetDirty()
  }

  return (
    <g onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      {isHovered && (
        <EdgeLabelRenderer>
          <div
            className={`${styles['button-edge__label']} nodrag nopan`}
            style={{
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`
            }}
          >
            <button className={styles['button-edge__button']} onClick={onEdgeClick}>
              ×
            </button>
          </div>
        </EdgeLabelRenderer>
      )}
    </g>
  )
}
