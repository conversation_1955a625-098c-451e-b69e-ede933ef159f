import { DATA_TYPES_MAP, NODETYPES, ENTRY_SOURCE_ID } from '@/utils/constant'
import { Space } from 'antd'

function KeyProp(props) {
  const { value = {} } = props
  const Component = DATA_TYPES_MAP[value?.type || 'string'].component
  return (
    <Space size={1} align="center" style={{ minWidth: 150 }}>
      <Component className={'text-[var(--flow-desc-color)]'} style={{ marginTop: 2 }} />
      <span>{value.name}</span>
      <span style={{ color: '#ff4d4f' }}>{value.required ? '*' : ''}</span>
    </Space>
  )
}
export default KeyProp
