import React from 'react'
import { Empty } from 'antd'
import emptyBaseImage from '@/assets/images/empty-base.png'
import styles from './style.module.scss'
import { APP_ENV } from '@/utils/constant'

const CustomEmpty = ({ description = '暂无数据', ...props }) => {
  // 当APP_ENV为auto时，完全使用antd默认样式
  if (APP_ENV === 'auto') {
    return <Empty description={description} {...props} />
  }

  // 当APP_ENV为base时，使用自定义样式和图片
  return (
    <div className={styles.customEmpty}>
      <Empty
        image={<img src={emptyBaseImage} alt="empty" style={{ width: 285, height: 203 }} />}
        description={<span className={styles.description}>{description}</span>}
        {...props}
      />
    </div>
  )
}

export default CustomEmpty
