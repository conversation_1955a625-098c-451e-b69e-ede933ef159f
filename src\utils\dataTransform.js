import { MarkerType, Position, getOutgoers } from '@xyflow/react'
import {
  EDGETYPES,
  NODETYPES,
  CONCURRENT_DIMENSION,
  ENTRY_SOURCE_ID,
  EXIT_SOURCE_ID
} from './constant'

import { NodeAdapterFactory } from './adapter/NodeAdapterFactory'

const deduplicateById = (arr) => {
  const map = new Map()
  return arr.filter((item) => {
    if (!map.has(item.id)) {
      map.set(item.id, true)
      return true
    }
    return false
  })
}

function deduplicateEdges(arr) {
  const idSet = new Set()
  const sourceTargetSet = new Set()

  return arr.filter((item) => {
    const idExists = idSet.has(item.id)
    const sourceTargetKey = `${item.source}-${item.sourceHandle}-${item.target}-${item.targetHandle}`
    const sourceTargetExists = sourceTargetSet.has(sourceTargetKey)

    if (!idExists) idSet.add(item.id)
    if (!sourceTargetExists) sourceTargetSet.add(sourceTargetKey)

    return !idExists && !sourceTargetExists
  })
}

/**
 * 画布显示的数据到后端存储数据的转换.
 * @param {*} nodes
 * @param {*} edges
 * @returns
 */
export const transformFlowNodeData = (nodes, edges, models) => {
  // 后端需要的数据结构
  const workflowNodes = nodes.map((nd) => {
    // 找出该Switch节点的Outgoers节点
    const { id, type, position, data } = nd
    let param = {}
    // 通过适配器转换数据
    const adapter = NodeAdapterFactory.getAdapter(type)
    if (adapter) {
      param = adapter.fromFrontToBack(nd, nodes, edges)
    }
    return param
  })

  // 过滤选择器出口节点
  const workflowEdges = edges.map((ed) => {
    return {
      ...ed
    }
  })

  return { nodes: deduplicateById(workflowNodes), edges: deduplicateById(workflowEdges) }
}

/**
 * 后端存储数据-->画布显示
 * @param {*} nodes
 * @param {*} edges
 * @param {*} isHorizontal
 * @returns
 */
export const transformNodeDataToFlow = (schemaData, isHorizontal = true) => {
  let originEdges = schemaData?.edges || []
  // 生成 initialEdges
  let originNodes = schemaData?.nodes || []
  // 判断有没有入口节点和结束节点
  const entryIndex = originNodes.findIndex((nd) => nd.nodeType === NODETYPES.ENTRY)
  if (entryIndex === -1) {
    originNodes.push({
      id: ENTRY_SOURCE_ID,
      nodeType: NODETYPES.ENTRY,
      position: { x: 0, y: 0 },
      description: '工作流的起始节点，用于设定启动工作流需要的信息',
      title: '开始',
      input: { text: { desc: { format: 'plain', schema: { type: 'string' } } } }
    })
  }
  const exitIndex = originNodes.findIndex((nd) => nd.nodeType === NODETYPES.EXIT)
  if (exitIndex === -1) {
    originNodes.push({
      id: EXIT_SOURCE_ID,
      nodeType: NODETYPES.EXIT,
      position: { x: 900, y: 0 },
      dist: false,
      description: '工作流的最终节点，用于返回工作流运行后的结果信息',
      title: '结束',
      input: { nlp: {} }
    })
  }

  const initialNodes = originNodes.map((nd) => {
    let param = {}
    const adapter = NodeAdapterFactory.getAdapter(nd.nodeType)

    if (adapter) {
      param = adapter.fromBackToFront(nd)
    }
    return param
  })

  const initialEdges = originEdges.map((ed) => {
    return {
      ...ed,
      type: EDGETYPES.CLOSABLE,
      markerEnd: {
        type: MarkerType.ArrowClosed
      },
      targetHandle: 'target'
    }
  })

  return { initialNodes, initialEdges: deduplicateEdges(initialEdges) }
}
