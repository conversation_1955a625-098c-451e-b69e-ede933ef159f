.monacoEditorWrapper {
  position: absolute;
  z-index: 10;
  width: calc(100% - 440px);
  height: calc(100% - 77px);
  left: 10px;
  top: 68px;
  background: linear-gradient(to bottom, #efedf8 0%, #ffffff 10%, #ffffff 100%);

  border: 1px solid rgba(17, 17, 17, 0.08);
  border-radius: 8px;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  //   left: 0;
  .monacoEditorTitle {
    height: 40px;
    min-height: 40px;
    // line-height: 40px;
    border-top: 1px solid rgba(17, 17, 17, 0.08);
    border-bottom: 1px solid #eaecf0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
  }
  .monacoEditor {
    flex: 1;
  }
}
