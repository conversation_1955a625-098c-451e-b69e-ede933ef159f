import IconClose from 'assets/svgs/close.svg?react'
import styles from './style.module.scss'
import { nodesMap } from '@/utils/constant'
import getNodeIconComponent from '@/utils/getNodeIconComponent'

function Header(props) {
  const { onClose, node } = props
  const Component = getNodeIconComponent(
    node.type,
    `${node.data.abilityOriginId || node.data.abilityCategory}`
  )

  return (
    <>
      <div className={styles['drawer-header']}>
        <div className={styles['header-top']}>
          <div className={styles['top-left']}>
            <div className={styles['icon']}>
              <Component />
            </div>
            <span>{node.data.name}节点调试</span>
          </div>
          <div className={styles['top-right']}>
            <div className={styles['button-left']}></div>
            <div className={styles['button-close']} onClick={onClose}>
              <div className={styles['icon-opt']}>
                <IconClose />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles['divider-big']}></div>
    </>
  )
}
export default Header
