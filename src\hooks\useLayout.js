import dagre from 'dagre'
import { useCallback, useRef } from 'react'
import { NODETYPES } from '../utils/constant'

export const getLayoutedElements = (nodes, edges, isH) => {
  const dagreGraph = new dagre.graphlib.Graph()
  dagreGraph.setDefaultEdgeLabel(() => ({}))
  console.log(nodes, edges)
  const direction = isH ? 'LR' : 'TB'
  dagreGraph.setGraph({ rankdir: direction })

  nodes.forEach((node) => {
    // concurrent节点内部节点不需要重新布局
    if (node.parentId && nodes.findIndex((nd) => nd.id === node.parentId) !== -1) {
      return
    }
    const { width, height } = node.measured
    dagreGraph.setNode(node.id, { width: width * 1.1, height: height * 1.1 })
  })

  edges.forEach((edge) => {
    const sourceNode = nodes.find((nd) => nd.id === edge.source)
    const targetNode = nodes.find((nd) => nd.id === edge.target)
    if (!sourceNode.parentId && targetNode.parentId) {
      // sourceNode 在外，targetNode位于concurrent内
      dagreGraph.setEdge(edge.source, targetNode.parentId)
    } else if (sourceNode.parentId && !targetNode.parentId) {
      // sourceNode 在内，targetNode位于concurrent外
      dagreGraph.setEdge(sourceNode.parentId, edge.target)
    } else {
      dagreGraph.setEdge(edge.source, edge.target)
    }
  })

  dagre.layout(dagreGraph)

  const newNodes = nodes.map((node) => {
    let newNode
    if (node.parentId && nodes.findIndex((nd) => nd.id === node.parentId) !== -1) {
      newNode = {
        ...node,
        position: node.position
      }
    } else {
      const nodeWithPosition = dagreGraph.node(node.id)
      const { width, height } = node.measured
      newNode = {
        ...node,
        position: {
          x: nodeWithPosition.x - width / 2,
          y: nodeWithPosition.y - height / 2
        }
      }
    }

    return newNode
  })

  return { nodes: newNodes, edges }
}

function useLayout(nodes, setNodes, edges, setEdges) {
  const setLayout = useCallback(
    (val) => {
      const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(nodes, edges, true)
      setNodes([...layoutedNodes])
      setEdges([...layoutedEdges])
    },
    [nodes, setNodes, edges, setEdges]
  )

  return [
    {
      setLayout
    }
  ]
}


export default useLayout
