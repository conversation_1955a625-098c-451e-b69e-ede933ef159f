.title {
  font-size: 14px;
  font-weight: 600;
  color: var(--flow-title-color2);
  line-height: 16px;
}

.choiceCard {
  border: 1px solid #eaecf0;
  margin-bottom: 16px;
  padding: 16px 16px 36px 16px;
  border-radius: 6px;
  position: relative;
  
}
.elseCard {
  border: 1px solid #eaecf0;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 6px;
}

.tag {
  height: 22px;
  background: #eff1f9;
  border-radius: 6px;
  line-height: 22px;
  padding: 0 8px;
  margin-left: 10px;
  font-size: 12px;
}

.branchLine {
  position: absolute;
  z-index: 1;
  height: 100%;
  // border: 1px solid red;
  width: 100%;
  pointer-events: none;
  .branchLineInner {
    position: absolute;
    top: 15px;
    bottom: 15px;
    width: 100%;
    // border: 1px solid blue;
    height: calc(100% - 35px);
    .branchLineInnerTop {
      position: absolute;
      width: 60%;
      left: 40%;
      top: 0;
      height: calc(50% - 10px);
      border-left: 1px solid #dcdcdf;
      border-top: 1px solid #dcdcdf;
      border-top-left-radius: 4px;
    }
    .branchLineInnerBottom {
      position: absolute;
      width: 60%;
      left: 40%;
      bottom: 0;
      height: calc(50% - 10px);
      border-left: 1px solid #dcdcdf;
      border-bottom: 1px solid #dcdcdf;
      border-bottom-left-radius: 4px;
    }
  }
}

.ruleLogic {
  position: absolute;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
}

.holder {
  color: var(--flow-desc-color);
  margin-right: 10px;
  cursor: move;
}
