import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import Schema from 'async-validator'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { getTreeData } from '@/hooks/getTreeData'

const validateNodeWithRules = async (node, rules, nodes, edges) => {
  const treeData = getTreeData(node, nodes, edges)
  const { validateChoiceInputData } = useInputDataValidate(treeData)
  let errors = []
  for (const field in rules) {
    // 针对 field  是否是output等特殊类型
    const rule = field === 'choice' ? rules[field](validateChoiceInputData) : rules[field]
    const validator = new Schema({ [field]: rule })
    try {
      await validator.validate({ [field]: node.data?.[field] })
    } catch ({ errors: validationErrors }) {
      if (validationErrors) {
        errors.push(...validationErrors)
      }
    }
  }
  return errors
}

export const ChoiceNodeStrategy = async (node, nodes, edges) => {
  const rule = getRules(NODETYPES.CHOICE)
  const errors = await validateNodeWithRules(node, rule, nodes, edges)
  return errors
}
