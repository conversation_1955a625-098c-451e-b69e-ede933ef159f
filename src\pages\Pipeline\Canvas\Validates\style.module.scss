.pannel {
  top: -8px !important;
  bottom: unset !important;
  left: unset !important;
  &.pannel-origin {
    right: 100px !important;
  }
  &.pannel-adapt {
    right: 420px !important;
  }
  
}

.container {
  width: 412px;
  //   height: 265px;
  // background: #ffffff;
  background: linear-gradient(to bottom, #f6f5fb 0%, #ffffff 25%, #ffffff 100%);

  border: 1px solid rgba(17, 17, 17, 0.08);
  border-radius: 6px;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  padding: 0 8px;
  .header {
    border-bottom: 1px solid #eaecf0;
    height: 52px;
    line-height: 52px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    .header-left {
      font-size: 16px;
      font-weight: 600;
      color: var(--flow-title-color);
    }
    .header-right {
      color: var(--flow-content-color);
      font-size: 24px;
      cursor: pointer;
    }
  }
  .content {
    padding: 16px 8px;
    overflow: auto;
    max-height: 220px;
    .node {
      padding: 8px 12px;
      border: 1px solid rgba(255, 255, 255, 0.5);
      border-radius: 4px;
      box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      cursor: pointer;
      .node-icon {
      }
      .node-info {
        margin-left: 6px;
        .node-info-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--flow-title-color2);
          line-height: 20px;
        }
        .node-info-validates {
          font-size: 12px;
          font-weight: 400;
          color: #ed4b3f;
          line-height: 20px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 310px;
        }
      }
    }

    .node + .node {
      margin-top: 6px;
    }
  }
}
