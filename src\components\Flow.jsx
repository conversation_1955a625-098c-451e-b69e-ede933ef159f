import React, { useState } from 'react'
import { createRoot } from 'react-dom/client'
import '../index.css'
import '@xyflow/react/dist/style.css'
import '@/assets/css/reactflow.scss'

import PipelineList from '@/pages/PipelineList'
import WorkflowWrapper from '@/pages/Pipeline/WorkflowWrapper.jsx'


// Create a root element and cache it
let root
const container = document.createElement('div')
container.style.height = '100%'
// Show function to render the component and append it to the DOM
const mount = (el, callback) => {
  let ele
  if (el) {
    el.replaceChildren()
    ele = el
  } else {
    ele = document.body
  }
  if (!root) {
    ele.appendChild(container)
    root = createRoot(container)
  }
  root.render(<PipelineList callback={callback} />)
}

// Hide function to remove the component from the DOM
const unmount = () => {
  if (root) {
    root.unmount()
    container.remove()
    root = null
  }
}

let rootC
const containerC = document.createElement('div')
containerC.style.height = '100%'
const mountC = (el, pipeline, callback) => {
  let ele
  if (el) {
    el.replaceChildren()
    ele = el
  } else {
    ele = document.body
  }
  if (!rootC) {
    ele.appendChild(containerC)
    rootC = createRoot(containerC)
  }
  rootC.render(<WorkflowWrapper workflowId={pipeline} callback={callback} />)
}

const unmountC = () => {
  if (rootC) {
    rootC.unmount()
    containerC.remove()
    rootC = null
  }
}

export { mount, unmount, mountC, unmountC }
