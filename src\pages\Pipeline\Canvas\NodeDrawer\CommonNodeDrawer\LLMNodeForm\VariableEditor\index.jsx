import React, { useEffect, useRef, useState } from 'react'
import { Popover, Tree, TreeSelect } from 'antd'
import { EditorView, ViewPlugin, placeholder as pl, Decoration, keymap } from '@codemirror/view'

import { EditorState, RangeSetBuilder, StateEffect } from '@codemirror/state'
import { defaultKeymap, insertNewlineAndIndent } from '@codemirror/commands'

import styles from './style.module.scss'
import { useLayoutEffect } from 'react'
import { DownOutlined, SettingOutlined } from '@ant-design/icons'
import { DATA_TYPES_MAP } from '@/utils/constant'

// 扁平化树结构
const flattenTree = (nodes, result = []) => {
  for (const node of nodes) {
    result.push({ key: node.key, title: node.title })
    if (node.children) {
      flattenTree(node.children, result)
    }
  }
  return result
}

const VariableEditor = ({ value = '', onChange, onBlur, variables, placeholder }) => {
  const editorRef = useRef(null)
  const anchorRef = useRef(null)
  const [popoverOpen, setPopoverOpen] = useState(false)
  const editorViewRef = useRef(null)
  const flattenedTree = useRef(flattenTree(variables))
  const lastCursorPos = useRef(null)
  const popoverRef = useRef()

  const [selectedKeys, setSelectedKeys] = useState([])

  const currentIndex = flattenedTree.current.findIndex((node) => node.key === selectedKeys[0])

  const handleVariableInsert = (keys, info) => {
    setSelectedKeys(keys)
    const view = editorViewRef.current
    if (!view) return

    const state = view.state
    const pos = state.selection.main.head
    const doc = state.doc.toString()

    let insertText = `{${keys[0]}}`
    let targetFrom = pos
    let targetTo = pos
    let foundInBraces = false

    // 检查光标是否在 {...} 内部
    const regex = /\{[^}]*\}/g
    let match
    while ((match = regex.exec(doc)) !== null) {
      const [full] = match
      const start = match.index
      const end = start + full.length
      if (pos > start && pos < end) {
        targetFrom = start
        targetTo = end
        foundInBraces = true
        break
      }
    }

    // 如果不在 {...} 中，但光标前是 `{`，只插入 `${key}}`，不要加多一个 `{`
    if (!foundInBraces && doc[pos - 1] === '{') {
      targetFrom = pos
      insertText = `${keys[0]}}` // ❗️前面已经有 {，只补后半段
    }

    const transaction = state.update({
      changes: {
        from: targetFrom,
        to: targetTo,
        insert: insertText
      },
      selection: { anchor: targetFrom + insertText.length }
    })

    view.dispatch(transaction)
    view.focus()
    // onChange?.(view.state.doc.toString())
    setPopoverOpen(false)
  }

  const onEditorBlur = (e) => {
    const related = e.relatedTarget
    // console.log(related)

    // 如果焦点转移到了 Popover 内部（例如变量选项），则不处理 blur
    if (related && related.closest('.my-variable-popover')) {
      return
    }
    const view = editorViewRef.current
    if (view) {
      onChange?.(view.state.doc.toString())
    }
    // setTimeout(()=>{setPopoverOpen(false)})
  }

  /**
   * 校验模板插值路径是否合法
   * @param schema - schema数组
   * @param expression - 插值表达式，如 `{abc[0].sub.field1}`
   * @returns boolean 是否有效路径
   */
  const validatePath = (schema, rawPath) => {
    // if (!expression.startsWith('{') || !expression.endsWith('}')) return false

    // 去除花括号，转为路径数组
    // const rawPath = expression.slice(1, -1)
    const segments = rawPath.replace(/\[(\d+)\]/g, '[$1]').split('.')

    // 递归匹配
    function match(nodes, index) {
      if (index >= segments.length) return true

      const currentKey = segments[index]

      for (const node of nodes) {
        const { title, type, children } = node

        // 匹配数组字段，如  abc[0]
        if (/\[\d+\]$/.test(currentKey)) {
          const name = currentKey.replace(/\[\d+\]$/, '')
          if (title === name && type === 'array<object>' && children) {
            return match(children, index + 1)
          }
        }

        // 匹配普通字段
        if (title === currentKey) {
          if ((type === 'object' || type === 'array<object>') && children) {
            return match(children, index + 1)
          }
          // 如果不是object类型，且已经是最后一个字段
          return index === segments.length - 1
        }
      }

      return false
    }

    return match(schema, 0)
  }

  const variablePlugin = ViewPlugin.fromClass(
    class {
      constructor(view) {
        this.view = view
      }

      update(update) {
        if (update.docChanged || update.selectionSet) {
          const pos = update.state.selection.main.head
          const doc = update.state.doc.toString()

          console.log('update variablePlugin', pos)

          // 只有当光标位置真正变化时才更新
          if (lastCursorPos.current !== pos) {
            lastCursorPos.current = pos
            // 手动更新 Popover 位置
            if (popoverRef.current) {
              setTimeout(() => {
                popoverRef.current.forceAlign()
              }, 10)
            }
            // 强制 Popover 重新定位
          }

          // 1. 正则查找所有的 {xxx}
          const regex = /\{(.*?)\}/g
          let match
          let inInterpolation = false

          while ((match = regex.exec(doc)) !== null) {
            const start = match.index
            const end = start + match[0].length

            if (pos > start && pos < end) {
              // 光标在插值表达式内
              inInterpolation = true
              setTimeout(() => {
                const coords = this.view.coordsAtPos(pos)
                const editorRect = this.view.dom.getBoundingClientRect()

                if (coords) {
                  anchorRef.current.style.position = 'absolute'
                  anchorRef.current.style.left = `${coords.left - editorRect.left - 10}px`
                  anchorRef.current.style.top = `${coords.top - editorRect.top}px`
                  anchorRef.current.dataset.start = start
                  anchorRef.current.dataset.end = end
                  setPopoverOpen(true)
                }
              }, 0)

              break
            }
          }

          if (!inInterpolation) {
            // 原来的 { 检测保留
            const prev = update.state.sliceDoc(pos - 1, pos)
            if (prev === '{') {
              setTimeout(() => {
                const coords = this.view.coordsAtPos(pos)
                const editorRect = this.view.dom.getBoundingClientRect()
                if (coords) {
                  anchorRef.current.style.position = 'absolute'
                  anchorRef.current.style.left = `${coords.left - editorRect.left - 10}px`
                  anchorRef.current.style.top = `${coords.top - editorRect.top}px`
                  anchorRef.current.dataset.start = pos
                  anchorRef.current.dataset.end = pos
                  setPopoverOpen(true)
                }
              }, 0)
            } else {
              setPopoverOpen(false)
            }
          }
        }
      }
    }
  )

  const updateListener = EditorView.updateListener.of((update) => {
    if (update.docChanged) {
      console.log('Change detected')
      const content = update.state.doc.toString()
      // onChange && onChange(content)
    }
  })


  const createInterpolationPlugin = (variables) => {
    return ViewPlugin.fromClass(
      class {
        constructor(view) {
          this.decorations = this.buildDecorations(view)
        }

        update(update) {
          if (update.docChanged || update.viewportChanged) {
            this.decorations = this.buildDecorations(update.view)
          }
        }

        buildDecorations(view) {
          const builder = new RangeSetBuilder()
          const doc = view.state.doc
          const text = doc.toString()
          const regex = /\{(.*?)\}/g
          let match

          while ((match = regex.exec(text)) !== null) {
            const [full, expr] = match
            const start = match.index
            const end = start + full.length

            const isValid = validatePath(variables, expr.trim())
            const deco = Decoration.mark({
              class: isValid
                ? 'cm-decoration-interpolation-valid'
                : 'cm-decoration-interpolation-invalid'
            })

            builder.add(start, end, deco)
          }

          return builder.finish()
        }

        destroy() {}
      },
      {
        decorations: (v) => v.decorations
      }
    )
  }

  const extensions = [
    pl(placeholder || '请输入内容...'),
    EditorView.editable.of(true),
    EditorView.lineWrapping, // 自动换行
    keymap.of([...defaultKeymap, { key: 'Enter', run: insertNewlineAndIndent }]), // 添加这行
    EditorState.languageData.of(() => {
      return [{ autocomplete: () => [] }]
    }),

    updateListener,
    variablePlugin,
    createInterpolationPlugin(variables) // 初始插件
  ]

  useEffect(() => {
    if (!editorRef.current) return

    const view = new EditorView({
      doc: value,
      parent: editorRef.current,
      extensions
    })

    editorViewRef.current = view

    return () => {
      view.destroy()
    }
  }, [])

  // 2. 在 useEffect 中处理 variables 变化
  useEffect(() => {
    const view = editorViewRef.current
    if (!view) return

    // 重新配置编辑器以更新插件
    view.dispatch({
      effects: StateEffect.reconfigure.of(extensions)
    })
  }, [JSON.stringify(variables)]) // 依赖 variables 的变化

  const treeRef = useRef(null)

  const afterOpenChange = (val) => {
    if (val && treeRef.current) {
      treeRef.current?.focus()
    }
  }

  useEffect(() => {
    if (popoverOpen) {
      
      setSelectedKeys([flattenedTree.current[0].key])
      // setSelectedKeys([])
    }
  }, [popoverOpen])

  useEffect(() => {
    const view = editorViewRef.current
    if (view && value !== view.state.doc.toString()) {
      view.dispatch({
        changes: { from: 0, to: view.state.doc.length, insert: value }
      })
    }
  }, [value])
  const titleRender = (nodeData) => {
    const Component = nodeData?.type
      ? DATA_TYPES_MAP[nodeData?.type]?.component
      : DATA_TYPES_MAP['string']?.component

    return (
      <div className="flex items-center flex-nowrap">
        {Component && <Component className="text-[var(--flow-desc-color)]" />}
        <span className="ml-1">{nodeData?.title}</span>
      </div>
    )
  }
  const handleKeyDownCapture = (e) => {
    if (!['ArrowUp', 'ArrowDown', 'Enter'].includes(e.key)) {
      e.stopPropagation()
    }
  }
  const handleKeyDown = (e) => {
    if (!['ArrowUp', 'ArrowDown', 'Enter'].includes(e.key)) return

    if (e.key === 'ArrowDown') {
      let nextKey
      if (currentIndex < flattenedTree.current.length - 1) {
        nextKey = flattenedTree.current[currentIndex + 1].key
      } else {
        nextKey = flattenedTree.current[0].key
      }
      setSelectedKeys([nextKey])
    } else if (e.key === 'ArrowUp') {
      let prevKey
      if (currentIndex > 0) {
        prevKey = flattenedTree.current[currentIndex - 1].key
      } else {
        prevKey = flattenedTree.current[flattenedTree.current.length - 1].key
      }

      setSelectedKeys([prevKey])
    } else if (e.key === 'Enter' && selectedKeys[0]) {
      // 模拟点击选中节点
      handleVariableInsert(selectedKeys)
    }
  }
  return (
    <div className={`${styles.editor} variable-ref-editor`}>
      <div ref={editorRef} onBlur={onEditorBlur} />
      {variables?.length > 0 && (
        <Popover
          ref={popoverRef}
          arrow={false}
          open={popoverOpen}
          placement="leftTop"
          afterOpenChange={afterOpenChange}
          content={
            <div
              style={{ minWidth: 200 }}
              ref={treeRef}
              className={`${styles.treeWrap} my-variable-popover`}
              tabIndex={-1}
              onKeyDown={handleKeyDown}
              onKeyDownCapture={handleKeyDownCapture}
            >
              <Tree
                tabIndex={0} // 必须设置可聚焦
                style={{ outline: 'none' }}
                selectedKeys={selectedKeys}
                virtual={false}
                selectable
                treeData={variables}
                onSelect={(keys, info) => {
                  handleVariableInsert([info.node.key])
                }}
                defaultExpandAll
               
                switcherIcon={<DownOutlined />}
                titleRender={titleRender}
              />
            </div>
          }
        >
          <span ref={anchorRef} style={{ position: 'absolute', zIndex: 10 }} />
        </Popover>
      )}
    </div>
  )
}

export default VariableEditor
