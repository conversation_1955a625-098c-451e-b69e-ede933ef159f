import React, { useState, useRef, useCallback, useEffect, DragEvent, memo } from 'react'
import {
  ReactFlow,
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  Background,
  useReactFlow,
  Position,
  getNodesBounds,
  MarkerType,
  useNodes,
  MiniMap,
  useNodesData,
  getOutgoers
} from '@xyflow/react'

// import useLayout from '@/hooks/useLayout'
import useNodesMeasured from '@/hooks/useNodesMeasured'

import { message } from 'antd'

import useStore from '@/store.js'

import { transformNodeDataToFlow } from '@/utils/dataTransform'
import { isValidConnection } from '@/utils/isValidConnection'
import { isLLMRelatedNode } from '@/utils/isLLMRelatedNode'

import styles from './style.module.scss'
import { NODETYPES, CONCURRENT_DIMENSION, EDGETYPES } from '@/utils/constant'
import ChoiceNode from './ChoiceNode'
import ConcurrentNode from './ConcurrentNode'
import NodeDrawer from './NodeDrawer'
import EntryNode from './EntryNode'
import ExitNode from './ExitNode'
import PassNode from './PassNode'
import FlowNode from './FlowNode'
import VariableSetNode from './VariableSetNode'
import VariableGetNode from './VariableGetNode'
import ClosableEdge from './ClosableEdge'

import eventBus from '@/utils/eventBus'

import Tool from './Tool'
import LLMNode from './LLMNode'
import { genNewNodeIdAndTitle, genNewAbilityId } from '@/utils/utils'
import { cloneDeep, throttle } from 'lodash'
import Validates from './Validates'
import ExperienceDrawer from './ExperienceDrawer'
import InsertApiModal from './InsertApiModal'
import useInIframe from '@/hooks/useInIframe'
import { getLayoutedElements } from '@/hooks/useLayout'

const MIN_DELTA = 1

const nodeTypes = {
  [NODETYPES.ENTRY]: EntryNode,
  [NODETYPES.EXIT]: ExitNode,
  [NODETYPES.CHOICE]: ChoiceNode,
  [NODETYPES.CONCURRENT]: ConcurrentNode,
  [NODETYPES.PASS]: PassNode,
  [NODETYPES.FLOW]: FlowNode,
  [NODETYPES.LLM]: LLMNode,

  [NODETYPES.SEMANTIC]: LLMNode,
  [NODETYPES.KNOWLEDGE]: LLMNode,
  [NODETYPES.DENIAL]: LLMNode,
  [NODETYPES.ARC]: LLMNode,
  [NODETYPES.INTENT_DOMAIN]: LLMNode,
  [NODETYPES.PK]: LLMNode,
  [NODETYPES.FUNCTION]: LLMNode,
  [NODETYPES.API]: LLMNode,
  [NODETYPES.CODE]: LLMNode,

  [NODETYPES.VARIABLE_SET]: VariableSetNode,
  [NODETYPES.VARIABLE_GET]: VariableGetNode,

}
const edgeTypes = {
  [EDGETYPES.CLOSABLE]: ClosableEdge
}

function Canvas({ initData, flowId, experienceVisible, onExperienceDrawerClose }) {
  const { debouncedSetDirty } = useStore()
  // console.log('---canvas----', initData)
  const reactFlowWrapper = useRef(null)
  // console.log('进入canvas', initData)
  const { initialNodes, initialEdges } = transformNodeDataToFlow(initData, true)
  // console.log('转换为画布上的节点和连线', initialNodes, initialEdges)

  const [nodes, setNodes] = useState(initialNodes)
  const [edges, setEdges] = useState(initialEdges)

  // const [{ setLayout }] = useLayout(nodes, setNodes, edges, setEdges)

  // const selectedNodes = nodes.filter((nd) => nd.selected)

  // 该处的selectedNode与reactflow中的nodes 中的selected还是不太相同
  const [selectedNode, setSelectedNode] = useState(null)
  // console.log('-----当前选中的节点是---', selectedNode)

  const [visible, setVisible] = useState(false)

  const reactflowInstance = useRef(null)

  const { screenToFlowPosition, updateNodeData, setCenter, getZoom } = useReactFlow()

  const { setIsDirty } = useStore()

  const [startPosition, setStartPosition] = useState(null)

  const isInIframe = useInIframe()

  const [insertApiModalOpen, setInsertApiModalOpen] = useState(false)

  const insertPosition = useRef(null)

  const onInsertApiModalCancel = () => {
    setInsertApiModalOpen(false)
  }

  useEffect(() => {
    // 订阅事件
    eventBus.on('INSERT_NODE', handler)
    eventBus.on('INSERT_API', insertApiHandler)
    // 清理订阅
    return () => {
      eventBus.off('INSERT_NODE', handler)
      eventBus.off('INSERT_API', insertApiHandler)
    }
  }, [selectedNode])

  const handler = (data) => {
    console.log('待插入节点信息:', data?.type)
    console.log('当前画布上选中的节点是', selectedNode)
    // TODO: 这里增加 并发 节点后，插入时，需要判断画布上有没有选中的并发节点，如果有，需要插入到并发节点下面，作为其子节点;
    if (selectedNode && selectedNode.type === NODETYPES.CONCURRENT) {
      if (data?.type === NODETYPES.CONCURRENT) {
        return message.error('并发器中不支持嵌套并发器')
      }
      const parentId = selectedNode.id
      let position = {
        x: 50,
        y: 50
      }
      insertNode(data.type, position, data?.data, parentId)
    } else {
      let position
      if (data?.position) {
        position = data?.position
      } else {
        position = {
          x: Math.floor(Math.random() * 500),
          y: Math.floor(Math.random() * 500)
        }
      }
      insertNode(data.type, screenToFlowPosition(position), data?.data)
    }

    setTimeout(() => {
      setIsDirty(true)
    })
  }

  const insertApiHandler = ({ position }) => {
    // alert('insertApiHandler')
    insertPosition.current = screenToFlowPosition(position)

    setInsertApiModalOpen(true)
  }

  // const calculateParentDimensions = (nodes, parentNode) => {
  //   const parentId = parentNode.id
  //   const childNodes = nodes.filter((node) => node.parentId === parentId)

  //   if (!childNodes.length) return { width: 320, height: 120, position: parentNode.position }

  //   const minX = Math.min(...childNodes.map((node) => node.position.x))
  //   const minY = Math.min(...childNodes.map((node) => node.position.y))
  //   const maxX = Math.max(
  //     ...childNodes.map((node) => node.position.x + (node.measured?.width || 300))
  //   )
  //   const maxY = Math.max(
  //     ...childNodes.map((node) => node.position.y + (node.measured?.height || 100))
  //   )

  //   const padding = 50
  //   const width = maxX - minX + padding * 2
  //   const height = maxY - minY + padding * 2
  //   const position = {
  //     x: parentNode.position.x + minX - padding,
  //     y: parentNode.position.y + minY - padding
  //   }

  //   return {
  //     width,
  //     height,
  //     position
  //   }
  // }

  const calculateMinMax = (childNodes) => {
    const minX = Math.floor(Math.min(...childNodes.map((node) => node.position.x)))
    const minY = Math.floor(Math.min(...childNodes.map((node) => node.position.y)))
    const maxX = Math.ceil(
      Math.max(...childNodes.map((node) => node.position.x + node.measured?.width))
    )
    const maxY = Math.ceil(
      Math.max(...childNodes.map((node) => node.position.y + node.measured?.height))
    )

    return { minX, minY, maxX, maxY }
  }

  const onNodesChange = useCallback(
    (changes) => {
      setNodes((prevNodes) => applyNodeChanges(changes, prevNodes))
    },
    [setNodes]
  )

  // const onNodesChange = useCallback(
  //   throttle((changes) => {
  //     console.log('++++++++++changes+++++++++++', changes)
  //     setNodes((prevNodes) => {
  //       console.log('---------------------prevNodes---------------', prevNodes)
  //       const currentNodes = applyNodeChanges(changes, prevNodes)
  //       let afterChangedNodes = cloneDeep(currentNodes)
  //       afterChangedNodes.forEach((node) => {
  //         if (node.type === NODETYPES.CONCURRENT) {
  //           const childNodes = afterChangedNodes.filter((n) => n.parentId === node.id)
  //           // 计算变化前的 minX, minY, maxX, maxY
  //           const {
  //             minX: prevMinX,
  //             minY: prevMinY,
  //             maxX: prevMaxX,
  //             maxY: prevMaxY
  //           } = calculateMinMax(prevNodes.filter((n) => n.parentId === node.id))
  //           // 计算变化后的 minX, minY, maxX, maxY
  //           const { minX, minY, maxX, maxY } = calculateMinMax(childNodes)

  //           // console.log('变化前:minX,minY,maxX,maxY', prevMinX, prevMinY, prevMaxX, prevMaxY)
  //           // console.log('变化后:minX,minY,maxX,maxY', minX, minY, maxX, maxY)

  //           // 父节点位置宽高由position和 width height决定，position由 deltaMinX 和 deltaMinY 决定变化, width 和 height 由bound决定
  //           const deltaX = Math.round(minX - prevMinX) // 确保为整数
  //           const deltaY = Math.round(minY - prevMinY)

  //           node.style = {
  //             width: maxX - minX + 2 * 50,
  //             height: maxY - minY + 2 * 50
  //           }
  //           if (Math.abs(deltaX) > MIN_DELTA || Math.abs(deltaY) > MIN_DELTA) {
  //             node.position = {
  //               x: node.position.x + deltaX,
  //               y: node.position.y + deltaY
  //             }

  //             childNodes.forEach((nd) => {
  //               let childNode = afterChangedNodes.find((n) => n.id === nd.id)
  //               childNode.position = {
  //                 x: childNode.position.x - deltaX,
  //                 y: childNode.position.y - deltaY
  //               }
  //             })
  //           }
  //         }
  //       })

  //       console.log('---------------------afterChangedNodes---------------', afterChangedNodes)
  //       return afterChangedNodes
  //     })
  //   }, 50),
  //   [setNodes]
  // )

  const onEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  )
  const onConnect = (params) => {
    console.log('连接的源节点和目标节点分别是：', params, params.source, params.target)
    const sourceNode = nodes.find((n) => n.id === params.source)
    const targetNode = nodes.find((n) => n.id === params.target)
    console.log('source node:', sourceNode)

    // 一、连线
    let newParams
    newParams = {
      ...params,
      type: EDGETYPES.CLOSABLE,
      markerEnd: {
        type: MarkerType.ArrowClosed
      }
    }
    setEdges((eds) => {
      console.log('origin eds', eds)
      return addEdge(newParams, eds)
    })

    setTimeout(() => {
      setIsDirty(true)
    })
  }

  const onDragOver = useCallback((event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onDrop = useCallback(
    (event) => {
      event.preventDefault()
      event.stopPropagation()

      const jsonObj = JSON.parse(event.dataTransfer.getData('application/reactflow') || '{}')

      const { delta, nodeType: type, data } = jsonObj
      const position = screenToFlowPosition({
        x: event.clientX - delta.x,
        y: event.clientY - delta.x
      })

      // 判断该位置在不在concurrent节点内部，如果在某个concurrent节点内部，应该插入该concurrent节点
      // TODO: 插入了找到的第一个concurrent节点，是否满足预期？有没有多个concurrent节点重叠在一起的情况？
      if (type === NODETYPES.API && !data.api_type) {
        insertPosition.current = position
        setInsertApiModalOpen(true)
        return
      }
      // TODO: API节点 弹出弹框，选择节点，然后再特定某位置执行插入
      const targetNode = nodes
        .filter((nd) => nd.type === NODETYPES.CONCURRENT)
        .find((nd) => {
          const condition =
            position.x > nd.position.x &&
            position.y > nd.position.y &&
            position.x < nd.position.x + nd.measured.width &&
            position.y < nd.position.y + nd.measured.height
          return condition
        })
      if (targetNode) {
        // 插入位置计算，
        const insertPos = {
          x: position.x - targetNode.position.x,
          y: position.y - targetNode.position.y
        }
        insertNode(type, insertPos, data, targetNode.id)
      } else {
        insertNode(type, position, data)
      }

      setTimeout(() => {
        setIsDirty(true)
      }, 0)
    },
    [setNodes, nodes, edges]
  )

  /**
   * 给定节点类型和position信息，在画布上插入节点
   * @param {*} type
   * @param {*} position
   * @param {*} data
   * @param {*} parentId parentId 有值，表示子节点插入
   */
  const insertNode = (type, position = { x: 0, y: 0 }, data = {}, parentId) => {
    if (!type) {
      return
    }

    console.log('待插入节点的信息', data)

    setNodes((nds) => {
      // 判断待添加类型节点在pipeline当前画布中已存在的个数
      // 计算待添加节点的id和title的方法
      const { id, name } = genNewNodeIdAndTitle(nds, type, data)

      let tobeAddedNode = {
        id,
        type,
        position,
        sourcePosition: Position.Right,
        targetPosition: Position.Left,
        data: {
          ...(data || {}),
          name: name
        }
      }
      if (type === NODETYPES.CONCURRENT) {
        tobeAddedNode = {
          ...tobeAddedNode,
          style: {
            width: CONCURRENT_DIMENSION.width,
            height: CONCURRENT_DIMENSION.height
          }
        }
      }
      if (parentId) {
        tobeAddedNode = {
          ...tobeAddedNode,
          parentId,
          extent: 'parent'
        }
      }
      // 大模型相关节点初始化abilityId
      if (
        isLLMRelatedNode(type) ||
        type === NODETYPES.FLOW ||
        type === NODETYPES.KNOWLEDGE ||
        type === NODETYPES.CODE ||
        type === NODETYPES.FUNCTION ||
        type === NODETYPES.VARIABLE_SET ||
        type === NODETYPES.VARIABLE_GET 
      ) {
        tobeAddedNode = {
          ...tobeAddedNode,
          data: {
            ...(tobeAddedNode?.data || {}),
            abilityId: genNewAbilityId(nds, type, data, type === NODETYPES.KNOWLEDGE),
            abilityCategory: genNewAbilityId(nds, type, data, type === NODETYPES.KNOWLEDGE)
          }
        }
      }

      if (type === NODETYPES.API) {
        tobeAddedNode = {
          ...tobeAddedNode,
          // 官方 official false自定义时，没有position, offical true时
          position: !!data.api_type ? position : insertPosition.current,
          data: {
            ...(tobeAddedNode?.data || {}),
            abilityId: genNewAbilityId(nds, type, data, true),
            abilityCategory: genNewAbilityId(nds, type, data, true)
          }
        }
      }

      const newNode = [tobeAddedNode]
      return nds.concat(newNode)
    })
  }

  // const onApiAdd = (item, index) => {
  //   console.log('onApiAdd-----', item, index)
  // }

  const onBeforeDelete = useCallback(({ nodes, edges }) => {
    console.log('onBeforeDelete', nodes, edges)
    // 删除的节点中包含 开始或者结束节点，则阻止本次操作
    if (nodes.some((nd) => nd.type === NODETYPES.ENTRY || nd.type === NODETYPES.EXIT)) {
      message.error('开始/结束节点不能删除')
      return false
    } else {
      return true
    }
  }, [])

  const onNodeClick = useCallback((_, node) => {
    // console.log('onNodeClick', node?.data.code)
    // 
    // 并发器节点不用弹出drawer
    
    setSelectedNode(node)
    if (node.type !== NODETYPES.CONCURRENT) {
      setVisible(true)
      onExperienceDrawerClose && onExperienceDrawerClose()
    }
  }, [])

  const onEdgeClick = useCallback((_, edge) => {
    setSelectedNode(null)
    setVisible(false)
  }, [])

  const onNodesDelete = (nodes) => {
    if (selectedNode) {
      const index = nodes.findIndex((nd) => nd.id === selectedNode.id)
      if (index !== -1) {
        // 重置选中节点
        setSelectedNode(null)
      }
      if (visible) {
        setVisible(false)
      }
    }

    // if (selectedNode && visible) {
    //   // 节点编辑弹框是开的，如果被删除的节点包含该节点，则将弹框关闭
    //   const index = nodes.findIndex((nd) => nd.id === selectedNode.id)
    //   if (index !== -1) {
    //     setVisible(false)
    //     // 重置选中节点
    //     setSelectedNode(null)
    //   }
    // }
    setTimeout(() => {
      setIsDirty(true)
    })
  }

  const onEdgesDelete = useCallback(
    (eds) => {
      console.log('delete edges is', eds)

      setTimeout(() => {
        setIsDirty(true)
      })
    },
    [edges]
  )

  const onNodeDragStart = useCallback(
    (event, node, nodes) => {
      console.log('onNodeDrag start---------', { x: event.clientX, y: event.clientY })
      setStartPosition({ x: event.clientX, y: event.clientY })
    },
    [nodes, edges]
  )

  const onNodeDrag = useCallback(
    (e, node, nodes) => {
      // console.log('onNodeDrag ---------', node, nodes)
    },
    [nodes, edges]
  )

  const onNodeDragStop = useCallback(
    (event, node) => {
      // 
      setIsDirty(true)
      setTimeout(() => setSelectedNode(node))

      // debouncedSetDirty()
      // setTimeout(() => setIsDirty(true))
    },
    [nodes, edges]
  )

  const onClose = useCallback(() => {
    setVisible(false)
  }, [visible])

  const onPaneClick = () => {
    setSelectedNode(null)
    setVisible(false)
  }

  const onIssueNodeClick = (node) => {
    // 将画布移动到以该节点为中央的位置

    const selectedNode = nodes.find((nd) => nd.id === node.id)
    const center = {
      x: selectedNode.position.x + selectedNode.measured.width / 2,
      y: selectedNode.position.y + selectedNode.measured.height / 2
    }
    setCenter(center.x, center.y, { duration: 300, zoom: getZoom() })
    setSelectedNode(selectedNode)
    setVisible(true)
    onExperienceDrawerClose && onExperienceDrawerClose()
  }

  const isValidConnectionCheck = useCallback(
    (connection) => {
      return isValidConnection(connection, nodes, edges)
    },
    [nodes, edges]
  )

  useNodesMeasured((measuredNodes) => {
    // nodes.forEach((n) =>
    //   console.log(`Node ${n.id}: ${n.width} x ${n.height}`)
    // );
    const isDisorder = measuredNodes.every((nd) => nd.position?.x === 0 && nd.position?.y === 0)
    if (isDisorder) {
      setTimeout(() => {
        const { nodes: layoutedNodes, edges: layoutedEdges } = getLayoutedElements(
          measuredNodes,
          reactflowInstance.current?.getEdges(),
          true
        )
        reactflowInstance?.current?.setNodes([...layoutedNodes])
        reactflowInstance?.current?.setEdges([...layoutedEdges])
        reactflowInstance?.current?.fitView()
        debouncedSetDirty(true)
      })
    }
  })

  const onInit = (instance) => {
    reactflowInstance.current = instance
  }

  return (
    <div className={styles['editor-canvas']} ref={reactFlowWrapper}>
      <ReactFlow
        deleteKeyCode={['Delete', 'Backspace']}
        onInit={onInit}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDragOver={onDragOver}
        onDrop={onDrop}
        onBeforeDelete={onBeforeDelete}
        onNodeClick={!isInIframe ? onNodeClick : undefined}
        onEdgeClick={onEdgeClick}
        onNodesDelete={onNodesDelete}
        onEdgesDelete={onEdgesDelete}
        onNodeDragStart={onNodeDragStart}
        onNodeDrag={onNodeDrag}
        onNodeDragStop={onNodeDragStop}
        fitView
        proOptions={{ hideAttribution: true }}
        onPaneClick={onPaneClick}
        nodeClickDistance={10}
        // nodeDragThreshold={1} // 设置拖拽阈值为 1 像素
        isValidConnection={throttle(isValidConnectionCheck, 500)}
        minZoom={isInIframe ? 0.1 : 0.2}
        nodesDraggable={!isInIframe} // 禁止节点拖动
        nodesConnectable={!isInIframe} // 禁止节点连接
        elementsSelectable={!isInIframe} // 禁止选择节点或边
        panOnDrag={!isInIframe} // 禁止拖动画布
        zoomOnScroll={!isInIframe} // 禁止滚动缩放
        zoomOnPinch={!isInIframe} // 禁止捏合缩放
        zoomOnDoubleClick={!isInIframe} // 禁止双击缩放
      >
        <Background style={{ background: '#ECF0F8' }} />

        {!isInIframe && <Tool visible={visible} />}
        <Validates onNodeClick={onIssueNodeClick} visible={visible} />
      </ReactFlow>
      {selectedNode && visible && (
        <NodeDrawer
          node={selectedNode}
          visible={true}
          onClose={onClose}
          setIsDirty={setIsDirty}
          flowId={flowId}
        ></NodeDrawer>
      )}
      <div style={{ display: experienceVisible ? 'block' : 'none' }}>
        <ExperienceDrawer visible={true} onClose={onExperienceDrawerClose} flowId={flowId} />
      </div>

      {/* 添加API节点的MODAL */}
      <InsertApiModal open={insertApiModalOpen} onCancel={onInsertApiModalCancel} />
    </div>
  )
}

export default memo(Canvas)
