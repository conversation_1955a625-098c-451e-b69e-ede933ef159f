import {
  Drawer,
  But<PERSON>,
  Space,
  Form,
  Switch,
  Select,
  Input,
  message,
  Card,
  Divider,
  Tag,
  Collapse,
  Cascader
} from 'antd'
import { memo, useCallback, useEffect } from 'react'
import Header from './Header'

import { getOutgoers, useEdges, useNodes, useReactFlow } from '@xyflow/react'
import { useTheme } from 'antd-style'
import { debounce } from 'lodash'
import { NODETYPES } from '@/utils/constant'

import EntryNodeForm from './EntryNodeForm'
import ExitNodeForm from './ExitNodeForm'

import LLMNodeForm from './LLMNodeForm'
import KnowledgeNodeForm from './KnowledgeNodeForm'
import APINodeForm from './APINodeForm'
import CodeNodeForm from './CodeNodeForm'
import PKNodeForm from './PKNodeForm'
import FunctionNodeForm from './FunctionNodeForm'
import SemanticNodeForm from './SemanticNodeForm'

import ChoiceNodeForm from './ChoiceNodeForm'
import PassNodeForm from './PassNodeForm'
import FlowNodeForm from './FlowNodeForm'

import VariableSetNodeForm from './VariableSetNodeForm'
import VariableGetNodeForm from './VariableGetNodeForm'

import { useState } from 'react'

export default memo((props) => {
  const { onClose, visible } = props

  const drawerStyles = {
    mask: {
      // backdropFilter: 'blur(10px)',
    },
    content: {
      // boxShadow: '-10px 0 10px #666',
    },
    header: {
      borderBottom: `1px solid #EAECF0`
    },
    body: {
      // fontSize: token.fontSizeLG,
    },
    footer: {
      // borderTop: `1px solid ${token.colorBorder}`,
    }
  }

  const getNodeForm = ({ node, ...rest }) => {
    const type = node.type
    

    switch (type) {
      case NODETYPES.LLM:

      case NODETYPES.DENIAL:
      case NODETYPES.ARC:
      case NODETYPES.INTENT_DOMAIN:
        return <LLMNodeForm node={node} {...rest}></LLMNodeForm>
      case NODETYPES.SEMANTIC:
        return <SemanticNodeForm node={node} {...rest}></SemanticNodeForm>
      case NODETYPES.PK:
        return <PKNodeForm node={node} {...rest}></PKNodeForm>
      case NODETYPES.FUNCTION:
        return <FunctionNodeForm node={node} {...rest}></FunctionNodeForm>
      case NODETYPES.KNOWLEDGE:
        return <KnowledgeNodeForm node={node} {...rest}></KnowledgeNodeForm>
      case NODETYPES.API:
        return <APINodeForm node={node} {...rest}></APINodeForm>
      case NODETYPES.CODE:
        return <CodeNodeForm node={node} {...rest}></CodeNodeForm>

      case NODETYPES.ENTRY:
        return <EntryNodeForm node={node} {...rest}></EntryNodeForm>
      case NODETYPES.EXIT:
        return <ExitNodeForm node={node} {...rest}></ExitNodeForm>

      case NODETYPES.CHOICE:
        return <ChoiceNodeForm node={node} {...rest}></ChoiceNodeForm>

      case NODETYPES.PASS:
        return <PassNodeForm node={node} {...rest}></PassNodeForm>
      case NODETYPES.FLOW:
        return <FlowNodeForm node={node} {...rest}></FlowNodeForm>

      case NODETYPES.VARIABLE_SET:
        return <VariableSetNodeForm node={node} {...rest}></VariableSetNodeForm>
      case NODETYPES.VARIABLE_GET:
        return <VariableGetNodeForm node={node} {...rest}></VariableGetNodeForm>

      default:
        return null
    }
  }

  return (
    <Drawer
      title={null}
      footer={null}
      placement="right"
      closable={false}
      onClose={onClose}
      open={visible}
      getContainer={false}
      destroyOnClose
      mask={false}
      rootStyle={{ position: 'absolute' }}
      width={420}
      styles={drawerStyles}
    >
      <div className="flex flex-col h-full">
        <Header {...props} />
        {getNodeForm({ ...props })}
      </div>
    </Drawer>
  )
})
