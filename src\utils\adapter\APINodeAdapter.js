import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'

import { MarkerType, Position, getOutgoers } from '@xyflow/react'

// 1、后端给定的 API 节点模板
const apiTemplate = {
  ability: {
    api_type: '',
    name: '',
    id: '',
    type: 'API',
    token: '',
    work_id: '',
    work_type: '',
    schema: ''
  },
  audit: true,
  description: '',
  flow_in: false,
  flow_out: false,
  input: {
    text: {}
  },
  inputAlias: [],
  nodeType: 'plugin',
  output: { nlp: { desc: { format: 'plain', schema: { type: 'string' } } } },
  title: '插件',
  type: 'task',
  dist: true
}

/**
 * 将大模型后端模板转成前端data
 * @param {*} template
 */
const convertAPITemplateFromBack2Front = function (template = apiTemplate) {
  // validated 前端字段，在这里过滤
  let {
    official,
    ability,
    input,
    inputAlias,
    nodeType,
    output,
    title,
    type,
    position,
    validated,
    ...rest
  } = template
  // TODO: output 没有数据，先取默认的数据显示
  const keys = Object.keys(output || {})
  if (keys.length === 1) {
    const k = keys[0]
    if (!(output || {})[k]?.desc) {
      // 没有值，搞个默认值
      output = { text: { desc: { format: 'plain', schema: { type: undefined } } } }
    }
  }
  // 
  let param = {
    ...rest,

    // !!ability?.config?.openapi 为true，表示是openapi，即自定义（非官方）

    input:
      !!ability?.config?.openapi || !!ability?.config?.auto_template
        ? inputAlias
        : handleInputFromBack2Canvas(input),

    // input: typeof inputAlias !== 'undefined' ? inputAlias : handleInputFromBack2Canvas(input),

    type: nodeType,
    output: handleOutputFromBack2Canvas(output),
    name: title,
    official
    // 确认 type  task? 前端不需要这个task，转换回来的时候写死
  }
  if (ability) {
    let aid = ability.id
    if (aid) {
      if (aid.startsWith('cbm_')) {
        aid = aid.substring(4)
      }
    }

    let acategory = ability.category
    if (acategory) {
      if (acategory.startsWith('cbm_')) {
        acategory = acategory.substring(4)
      }
    } else {
      acategory = aid
    }

    const api_type = ability?.api_type

    param = {
      ...param,
      abilityId: aid,
      abilityCategory: acategory,
      abilityName: ability.name,
      abilityType: ability.type,
      abilityOriginId: ability.originId,
      // 后端到前端，将前端domain字段存放 `${ability.domain ?? ''}_${ability.patchId ?? ''}_${ability.modelType ?? ''}`
      api_type,
      // coze, xingchen
      token: ability?.config?.[api_type]?.key,
      work_id: ability?.config?.[api_type]?.id,
      work_type: ability?.config?.[api_type]?.type,
      // openAPi字段
      schema: ability?.config?.[api_type]?.template

      // 汽车模板字段
    }
    if (api_type === 'openapi') {
      param = {
        ...param,
        schema: ability?.config?.[api_type]?.template
      }
    } else if (api_type === 'auto_template') {
      param = {
        ...param,
        url: ability?.config?.[api_type]?.url,
        method: ability?.config?.[api_type]?.method,
        auth_type: ability?.config?.[api_type]?.auth?.type,
        auth_key: ability?.config?.[api_type]?.auth?.key,

        auto_template: ability?.config?.[api_type]

        // {
        //   "auto_template": {
        //     "template": "{\"openapi\":\"3.1.0\",\"info\":{\"title\":\"agentBuilder工具集\",\"version\":\"1.0.0\",\"x-is-official\":false},\"servers\":[{\"description\":\"a server description\",\"url\":\"http://sophon-adapter.autocar-aqua-test.k8s.hf.iflytekauto.cn\"}],\"paths\":{\"/sophon-adapter/agent/api/v1/chat/fuL1KCmF3zokgp0zdOLYc8chqYRrXqFr\":{\"POST\":{\"description\":\"test\",\"operationId\":\"测试智能体-5rWL6K+V\",\"summary\":\"测试智能体\",\"requestBody\":{\"required\":true,\"content\":{\"application/json\":{\"schema\":{\"type\":\"object\",\"properties\":{\"cbm_agent\":{\"type\":\"string\",\"description\":\"智能体\"},\"test\":{\"type\":\"string\",\"description\":\"描述\"}}}}}}}}}}",
        //     "method": "POST",
        //     "auth": { "type": "bearer", "key": "j2CutsA7ELnFrWTAWfMfOhQU0kGtxVkL" },
        //     "customParamWebSchema": {
        //       "toolRequestOutput": [],
        //       "toolRequestInput": [
        //         {
        //           "defaultValue": "",
        //           "startDisabled": false,
        //           "name": "cbm_agent",
        //           "description": "智能体",
        //           "location": "body",
        //           "descriptionErrMsg": "",
        //           "id": "41ef4702-5b8e-47eb-867e-2882fc1ca1dd",
        //           "nameErrMsg": "",
        //           "type": "string",
        //           "open": true,
        //           "required": false
        //         },
        //         {
        //           "defaultValue": "",
        //           "startDisabled": false,
        //           "name": "test",
        //           "description": "描述",
        //           "location": "body",
        //           "descriptionErrMsg": "",
        //           "id": "14e046b8-4860-4d20-a2b2-6bdfe1f06998",
        //           "nameErrMsg": "",
        //           "type": "string",
        //           "open": true,
        //           "required": false
        //         }
        //       ]
        //     },
        //     "url": "http://sophon-adapter.autocar-aqua-test.k8s.hf.iflytekauto.cn/sophon-adapter/agent/api/v1/chat/fuL1KCmF3zokgp0zdOLYc8chqYRrXqFr"
        //   }
        // }

        
      }
    } else {
      param = {
        ...param,
        token: ability?.config?.[api_type]?.key,
        work_id: ability?.config?.[api_type]?.id,
        work_type: ability?.config?.[api_type]?.type
      }
    }
  }
  return param
}

/**
 * 将前端data转成后端协议结构
 * @param {*} data
 */
const convertAPITemplateFromFront2Back = function (data = {}, nodes) {
  const {
    official,
    abilityId,
    abilityName,
    abilityType,
    abilityOriginId,
    abilityCategory,

    token,
    work_id,
    work_type,
    api_type,
    schema,

    url,
    method,
    auth_type,
    auth_key,
    auto_template = {},

    input,
    output,
    name,
    // 前端字段，在这里去除
    validated,
    debug,
    ...rest
  } = data

  const handleInput = handleInputFromCanvas2Back(input, nodes)
  const handleOutput = handleOutputFromCanvas2Back(output)

  let config = {}
  if (api_type === 'openapi') {
    config.openapi = {
      template: schema
    }
  } else if (api_type === 'auto_template') {
    config['auto_template'] = {
      url,
      method,
      auth: {
        type: auth_type,
        key: auth_key
      },
      ...auto_template
    }
  } else {
    config[api_type] = {
      id: work_id,
      key: token,
      type: work_type
    }
  }

  let newData = {
    ...rest,
    official,
    title: name,
    type: 'task',
    output: handleOutput,
    input: handleInput,
    inputAlias: input,
    ability: {
      id: abilityId ? `cbm_${abilityId}` : '',
      originId: abilityOriginId,
      category: abilityCategory ? `cbm_${abilityCategory}` : abilityId ? `cbm_${abilityId}` : '',
      // ability.name 接收页面 的title
      name,
      type: abilityType || 'LLM',
      api_type,
      config
    }
  }

  return newData
}

export class APINodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    param = {
      ...param,
      data: convertAPITemplateFromBack2Front(nd)
    }
    return param
  }

  static fromFrontToBack(nd, nodes) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    const newData = convertAPITemplateFromFront2Back(data, nodes)
    param = {
      ...newData,
      ...param
    }
    return param
  }
}
