import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'
import { MarkerType, Position, getOutgoers } from '@xyflow/react'
import {
  EDGETYPES,
  NODETYPES,
  CONCURRENT_DIMENSION,
  ENTRY_SOURCE_ID,
  EXIT_SOURCE_ID
} from '@/utils/constant'



export class ConcurrentNodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    param = {
      ...param,
      style: {
        width: parseInt(nd.style?.width ?? CONCURRENT_DIMENSION.width),
        height: parseInt(nd.style?.height ?? CONCURRENT_DIMENSION.height)
      },
      data: {
        name: nd.title,
        description: nd.description
      }
    }
    return param
  }

  static fromFrontToBack(nd) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    param = {
      ...param,
      style: {
        width: parseInt(nd.measured?.width ?? CONCURRENT_DIMENSION.width),
        height: parseInt(nd.measured?.height ?? CONCURRENT_DIMENSION.height)
      },
      title: data?.name,
      description: data?.description,
      type: nd.type
    }
    return param
  }
}
