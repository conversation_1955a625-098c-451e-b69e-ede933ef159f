import { Button, message, Space } from 'antd'
import { getOutgoers } from '@xyflow/react'
import { CONNECTION_BLACK_LIST, NODETYPES } from '@/utils/constant'

const connectionErrorKey = 'connection_error_key'

const checkIsCycle = (connection, nodes, edges) => {
  const source = nodes.find((node) => node.id === connection.source)
  const target = nodes.find((node) => node.id === connection.target)

  const hasCycle = (node, visited = new Set()) => {
    if (visited.has(node.id)) return false

    visited.add(node.id)

    for (const outgoer of getOutgoers(node, nodes, edges)) {
      if (outgoer.id === connection.source) return true
      if (hasCycle(outgoer, visited)) return true
    }
  }

  if (target.id === connection.source) {
    message.error({
      key: connectionError<PERSON>ey,
      type: 'error',
      content: '节点不能连接自身'
    })
    return false
  }
  if (hasCycle(target)) {
    message.error({
      key: connection<PERSON>rro<PERSON><PERSON><PERSON>,
      type: 'error',
      content: '节点之间连接不能形成环状'
    })
    return false
  }
  return true
}

const checkBlackList = (connection, nodes, edges) => {
  const source = nodes.find((node) => node.id === connection.source)
  const target = nodes.find((node) => node.id === connection.target)

  const black = CONNECTION_BLACK_LIST.find(
    (item) => item.source === source.type && item.target === target.type
  )
  if (black) {
    message.error({
      key: connectionErrorKey,
      type: 'error',
      content: black.message
    })
    return false
  }
  return true
}

const checkConcurrent = (connection, nodes, edges) => {
  const source = nodes.find((node) => node.id === connection.source)
  const target = nodes.find((node) => node.id === connection.target)
  // 校验 conncurrent节点
  // 校验只能有一个入口
  if (!source.parentId && target.parentId) {
    for (const edge of edges) {
      const sourceNode = nodes.find((nd) => nd.id === edge.source)
      const targetNode = nodes.find((nd) => nd.id === edge.target)
      if (!sourceNode.parentId && targetNode.parentId && targetNode.parentId === target.parentId) {
        message.error({
          key: connectionErrorKey,
          type: 'error',
          content: '并发器节点只能有一个入口'
        })
        return false
      }
    }
  }

  // 校验只能有一个出口
  if (source.parentId && !target.parentId) {
    for (const edge of edges) {
      const sourceNode = nodes.find((nd) => nd.id === edge.source)
      const targetNode = nodes.find((nd) => nd.id === edge.target)
      if (sourceNode.parentId && !targetNode.parentId && sourceNode.parentId === source.parentId) {
        message.error({
          key: connectionErrorKey,
          type: 'error',
          content: '并发器节点只能有一个出口'
        })
        return false
      }
    }
  }
  return true
}

const checkCondition = (connection, nodes, edges) => {
  const source = nodes.find((node) => node.id === connection.source)
  const target = nodes.find((node) => node.id === connection.target)

  // console.log('------------checkConditon-------', source, target)
  // console.log('---connection---', connection)
  // console.log('---edges---', edges)
  // 判断 当前connection连线是否是分支器的最后一条待连的线，是的话，要判断之前连过的是否都连向了同一target，这次也连接向该target，这种情况要避免
  const conditionEdges = edges.filter((ed) => ed.source === connection.source)

  if (
    source.type === NODETYPES.CHOICE &&
    conditionEdges.length > 0 &&
    conditionEdges.length === source.data?.choice.length + 1 - 1
  ) {
    let sameTargetId = conditionEdges[0].target
    const allSame = conditionEdges.every((it) => it.target === sameTargetId)
    if (allSame && connection.target === sameTargetId) {
      message.error({
        key: connectionErrorKey,
        type: 'error',
        content: '分支器不能所有出口连接向同一目标节点'
      })
      return false
    }
  }

  return true
}

export const isValidConnection = (connection, nodes, edges) => {
  // console.log('isValidConnection-------------------------------', connection, nodes, edges)
  // we are using getNodes and getEdges helpers here
  // to make sure we create isValidConnection function only once
  // const nodes = getNodes()
  // const edges = getEdges()
  if (!checkBlackList(connection, nodes, edges)) {
    return false
  }
  if (!checkIsCycle(connection, nodes, edges)) {
    return false
  }
  if (!checkConcurrent(connection, nodes, edges)) {
    return false
  }

  if (!checkCondition(connection, nodes, edges)) {
    return false
  }

  return true
}
