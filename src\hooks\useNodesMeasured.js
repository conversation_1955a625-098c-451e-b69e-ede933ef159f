import { useEffect } from 'react'
import { useReactFlow } from '@xyflow/react'

/**
 * Hook to wait until all nodes have been measured (i.e., have width and height)
 * @param onAllMeasured callback when all nodes are measured
 * @param deps optional dependency array to control when to start checking
 */
export default function useNodesMeasured(onAllMeasured, deps = []) {
  const { getNodes } = useReactFlow()

  useEffect(() => {
    let animationFrameId

    const check = () => {
      const nodes = getNodes()
      const allMeasured = nodes.every((n) => n.measured)

      if (allMeasured) {
        onAllMeasured(nodes)
      } else {
        animationFrameId = requestAnimationFrame(check)
      }
    }

    check()

    return () => {
      cancelAnimationFrame(animationFrameId)
    }
  }, deps)
}
