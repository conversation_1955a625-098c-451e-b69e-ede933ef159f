import {
  But<PERSON>,
  Collapse,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Space,
  Switch
} from 'antd'

import React, { memo, useCallback, useEffect, useState } from 'react'
import {
  CheckOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  FullscreenOutlined
} from '@ant-design/icons'

import { useReactFlow, useNodesData, useNodeConnections } from '@xyflow/react'

import FormItemRenderer from '@/components/FormItemRender'
import useRefTreeData from '@/hooks/useRefTreeData'

import { getRules, outputRule } from '@/utils/rule'

import InputData from '@/components/InputData/input_data.jsx'
import useStore from '@/store.js'

import useRefUpdate from '@/hooks/useRefUpdate'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { BlurInput, BlurTextArea } from '@/components/BlurInput'
import NodeDebugDrawer from '../NodeDebugDrawer'
import eventBus from '@/utils/eventBus'
import withDividingLine from '@/components/WithDividingLine'

import IconArrow from 'assets/svgs/drop-arrow.svg?react'

const genLabelTitle = (title) => {
  return <span className="text-[var(--flow-title-color2)] text-[14px] font-semibold">{title}</span>
}

export default memo((props) => {
  const { node, onClose, visible } = props
  const [form] = Form.useForm()
  const { updateNodeData } = useReactFlow()
  const { debouncedSetDirty } = useStore()
  const nodeData = useNodesData(node.id)
  const { updateRefName } = useRefUpdate(node)

  const hasExpand = (nodeData?.data?.output || [])
    .filter(Boolean)
    .some((val) => (val.schema || []).length > 0)
  console.log(
    '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++nodeData',
    nodeData
  )
  const treeData = useRefTreeData(nodeData, form, visible)
  // console.log('---------------------------------------------------------------treeData', treeData)
  const { validateInputData } = useInputDataValidate(treeData)

  const ruleObj = getRules(node.type)

  // 官方的模版能力需要禁用某些
  const isOfficial = false

  const [nodeDebugVisible, setNodeDebugVisible] = useState(false)

  useEffect(() => {
    // 订阅事件
    eventBus.on('DEBUG_NODE', handler)
    // 清理订阅
    return () => {
      eventBus.off('DEBUG_NODE', handler)
    }
  }, [form])

  const onCloseDebug = useCallback(() => {
    setNodeDebugVisible(false)
  }, [])

  const handler = () => {
    form
      .validateFields()
      .then(() => {
        setNodeDebugVisible(true)
        updateNodeData(node.id, { debug: false })
      })
      .catch(() => {
        updateNodeData(node.id, { debug: false })
      })
  }

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log('\n\n----onValuesChange---', allValues)
    //
    updateNodeData(node.id, allValues)
    debouncedSetDirty()
  }

  const setFields = () => {
    console.log(
      '%c useEffect trigged code drawer ,node.data.code',
      'background: #ff0000; color: #ffffff;',
      node.data.code
    )
    console.log('nodeData.data.code', nodeData.data?.code)
    // 
    // console.log('useEffect trigged code drawer', node.type, node?.data)
    form.setFieldsValue({
      domain: nodeData.data?.domain,
      abilityId: nodeData.data?.abilityId,
      abilityName: nodeData.data?.abilityName,

      audit: !!nodeData.data?.audit,
      dist: !!nodeData.data?.dist,
      flow_in: !!nodeData.data?.flow_in,
      flow_out: !!nodeData.data?.flow_out,
      input: nodeData.data?.input,
      output: nodeData.data?.output
    })
  }

  const onNameChange = (path, value) => {
    console.log('onNameChange', path, value)
    updateRefName(path, value)
  }

  useEffect(() => {
    if (visible) {
      setFields()
      //  校验过了，再次打开，需要展示校验结果
      if (node.data?.validated) {
        setTimeout(() => {
          form.validateFields()
        })
      }
    }
  }, [nodeData, visible, form])

  useEffect(() => {
    if (nodeData.data.debug) {
      setTimeout(() => {
        handler()
      })
    }
  }, [nodeData.data.debug, form])

  let inputFormListAdd
  let outputFormListAdd

  const genExtra = (isInput) => {
    // const showAdd = isOfficial ? false : isInput ? true : false
    return (
      // <Space size={0} split={<Divider type="vertical" />}>
      <Space size={0} split={<Divider type="vertical" />}>
        {/* {isInput && (
          <Switch
            key="memory"
            checkedChildren={
              <span>
                记忆
                <CheckOutlined />
              </span>
            }
            unCheckedChildren={
              <span>
                记忆
                <CloseOutlined />
              </span>
            }
          />
        )} */}
        {!isInput && (
          <Form.Item name="dist" style={{ marginBottom: 0 }}>
            <Switch
              checkedChildren={
                <span>
                  下发
                  <CheckOutlined />
                </span>
              }
              unCheckedChildren={
                <span>
                  下发
                  <CloseOutlined />
                </span>
              }
            />
          </Form.Item>
        )}
        <Form.Item
          name={isInput ? 'flow_in' : 'flow_out'}
          initialValue={true}
          style={{ marginBottom: 0 }}
        >
          <Switch
            disabled={true}
            key="flow"
            checkedChildren={
              <span>
                流式
                <CheckOutlined />
              </span>
            }
            unCheckedChildren={
              <span>
                流式
                <CloseOutlined />
              </span>
            }
          />
        </Form.Item>

        {isInput && <Button
          type="dashed"
          onClick={() => {
            if (isInput) {
              inputFormListAdd({})
            } else {
              outputFormListAdd()
            }
          }}
          size="small"
        >
          +
        </Button>}
      </Space>
    )
  }

  const items = [
    {
      key: '3',
      label: genLabelTitle('输入'),
      children: (
        <Form.List name="input">
          {(subFields, { add, remove }) => {
            inputFormListAdd = add
            return (
              <div className="relative">
                {subFields.map((subField) => {
                  const key = form.getFieldValue(['input', subField.name, 'key'])

                  console.log('key is', key)
                  console.log('subField', subField)
                  return (
                    <>
                      <Space
                        key={subField.key + 'ext'}
                        style={{ display: 'flex', marginBottom: -4 }}
                        align="middle"
                        size={4}
                      >
                        <Form.Item
                          name={[subField.name, 'key']}
                          rules={[
                            ...outputRule.name,
                            ({ getFieldValue }) => ({
                              validator(_, value) {
                                if (value) {
                                  const currentIndex = subField.name

                                  const sameLevelVal = getFieldValue(['input'])
                                  for (let i = 0; i < sameLevelVal.length; i++) {
                                    if (i === currentIndex) {
                                      continue
                                    }
                                    if (sameLevelVal[i]?.key === value) {
                                      return Promise.reject(new Error('变量名不可重复'))
                                    }
                                  }

                                  // // 校验该变量是否被 提示词所使用
                                }

                                return Promise.resolve()
                              }
                            })
                          ]}
                          style={{ marginBottom: 0 }}
                        >
                          <BlurInput
                            placeholder="参数名"
                            showCount={!isOfficial}
                            maxLength={32}
                            disabled={isOfficial}
                          />
                        </Form.Item>
                        <Form.Item
                          name={[subField.name, 'value']}
                          style={{ marginBottom: 0, width: 198 }}
                          rules={[{ validator: (rule, value) => validateInputData(rule, value) }]}
                        >
                          <InputData options={treeData} placeholder={'请输入或引用参数值'} />
                        </Form.Item>
                        {subFields.length > 1 && (
                          <Form.Item style={{ marginBottom: 8, width: 10 }}>
                            <CloseCircleOutlined
                              onClick={() => remove(subField.name)}
                              className="text-[var(--flow-desc-color)]"
                            />
                          </Form.Item>
                        )}
                      </Space>
                    </>
                  )
                })}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(true)
    },

    {
      key: '11',
      label: genLabelTitle('输出'),
      children: (
        <Form.List name="output">
          {(subFields, { add, remove }) => {
            // console.log('****************subFields**************', subFields)
            outputFormListAdd = add

            return (
              <div className="relative">
                {subFields.map((subField) => (
                  <Space align="start" size={4} key={subField.key}>
                    <FormItemRenderer
                      field={subField}
                      remove={remove}
                      form={form}
                      path={['output', subField.name]}
                      closeable={subFields.length > 1}
                      hasExpand={hasExpand}
                      onNameChange={onNameChange}
                      isTop={true}
                    />
                    {/* <Form.Item style={{ marginBottom: 8, width: 20 }}>
                    <CloseCircleOutlined
                      onClick={() => remove(subField.name)}
                      className="text-[var(--flow-desc-color)]"
                    />
                  </Form.Item> */}
                  </Space>
                ))}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(false)
    }
  ]
    .filter(Boolean)
    .map((item) => {
      return {
        ...item,
        children: withDividingLine(item.children)
      }
    })

  return (
    <>
      <Form
        form={form}
        name="basic"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        labelWrap
        layout="vertical"
        onValuesChange={onValuesChange}
        requiredMark={false}
        className="flex-1 overflow-auto mr-[-24px] pr-0 "
        preserve={false}
      >
        <Collapse
          items={items}
          defaultActiveKey={['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', 'code']}
          size="small"
          collapsible={'icon'}
          ghost
          className="pr-[20px]"
          expandIcon={({ isActive }) => (
            <IconArrow
              style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
            ></IconArrow>
          )}
        />
      </Form>

      <NodeDebugDrawer {...props} nodeDebug={nodeDebugVisible} onCloseDebug={onCloseDebug} />
    </>
  )
})
