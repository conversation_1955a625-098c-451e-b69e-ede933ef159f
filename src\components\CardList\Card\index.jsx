import { Dropdown, message } from 'antd'
import styles from '../style.module.scss'
import { EllipsisOutlined } from '@ant-design/icons'
import { memo } from 'react'
import MoreIcon from '@/assets/svgs/more2.svg?react'
import DateIcon from '@/assets/svgs/date.svg?react'
import CopyIcon from '@/assets/svgs/copy.svg?react'
import { CopyToClipboard } from 'react-copy-to-clipboard'

function Card({
  item,
  dropdown,
  logo,
  title,
  id,
  description,
  updateTime,
  renderFooterTag,
  events
}) {
  return (
    <div
      className={styles['card-item-wrapper']}
      onClick={(e) => {
        events.onCardClick?.(item)
      }}
    >
      <div className={styles['card-item']}>
        {logo && (
          <div className={styles['card-icon']} style={{ backgroundImage: `url(${logo})` }}></div>
        )}
        <div className={styles['card-content']}>
          <div className={styles['card-header']}>
            <div>
              <h4 className={styles['card-title']} title={title}>{title}</h4>
              {id && (
                <p className={styles['card-id']}>
                  <span className={styles['card-id-content']} title={id} style={{ marginRight: 4 }}>
                    {id}
                  </span>
                  <CopyToClipboard text={id} onCopy={() => message.success('拷贝成功')}>
                    <CopyIcon onClick={(e) => e.stopPropagation()} />
                  </CopyToClipboard>
                </p>
              )}
            </div>
            <div className={styles['card-buttons']}>
              <Dropdown
                menu={{
                  items: dropdown.optItems,
                  onClick: (e) => {
                    dropdown?.onDropItemsClick(e, item)
                  }
                }}
                placement="bottom"
                className="nodrag w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              >
                <div className={`${styles['card-button']}`}>
                  {/* <EllipsisOutlined className="text-[var(--flow-title-color)]" /> */}
                  <MoreIcon />
                </div>
              </Dropdown>
            </div>
          </div>

          <p className={styles['card-desc']} title={description}>
            {description}
          </p>

          {(renderFooterTag || updateTime) && (
            <div className={styles['card-footer']}>
              <div>{renderFooterTag && renderFooterTag(item)}</div>
              <div className={styles['card-date']}>
                <DateIcon /> <span className={styles['time-wrapper']}> {updateTime}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
export default memo(Card)
