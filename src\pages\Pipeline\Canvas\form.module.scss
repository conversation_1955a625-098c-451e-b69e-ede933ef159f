.container {
  position: relative;
  width: 274px;
  margin-top: 8px;
  font-size: 12px;
  .item {
    display: flex;
    align-items: center;
    position: relative;
    background: #f5f6fb;
    border-radius: 4px;
    padding: 0 10px;
    .label {
      width: 40px;
      min-width: 40px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--flow-content-color);
    }
    .values-model {
      padding-left: 4px;
      min-height: 30px;
      line-height: 30px;
      flex: 1;
      color: var(--flow-desc-color);
    }
    .values {
      padding-left: 4px;
      min-height: 30px;
      flex: 1;
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      position: relative;

      .value-item {
        display: flex;
        align-items: center;
        background: #ffffff;
        border-radius: 4px;
        text-align: center;
        font-size: 12px;
        height: 22px;
        line-height: 22px;
        padding: 0 4px;
        // overflow: hidden;
        .property-name {
          color: var(--flow-desc-color);
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-left: 2px;
        }
      }
      .value-item + .value-item {
        margin-left: 4px;
      }
      .overlay {
        position: absolute;
        z-index: 1;
        display: flex;
        align-items: center;
        right: 0;
        top: 4px;
        height: 22px;
        .mask {
          background: linear-gradient(90deg, hsla(0, 0%, 100%, 0) 0, rgba(252, 255, 255, 1) 78%);
          height: 100%;
          width: 93px;
        }
      }
    }
  }
  .item + .item {
    margin-top: 2px;
  }

  .divider {
    border-color: #eaecf0;
    margin: 0;
    font-size: 12px;
    line-height: 12px;
  }
}

.model-icon {
  display: block;
  width: 20px;
  height: 20px;
  min-width: 20px;
  background-repeat: no-repeat;
  background-size: contain;
  margin-right: 2px;
}

.values-popper {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  position: relative;

  .value-item {
    display: flex;
    align-items: center;
    // max-width: 100px;
    background: #f5f6fb;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    padding: 0 4px;
    .property-name {
      color: var(--sparkos-primary-color);
      display: inline-block;
      //   max-width: 64px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-left: 2px;
    }
  }
}

:global {
  .sub-overlay2 {
    width: 360px !important;
    max-height: 462px;
    overflow: auto;
    background: #ffffff;
    border: 1px solid #eaecf0;
    border-radius: 6px;
    box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  }
}
