/**
 * Created by lycheng on 2018/8/6.
 */

 const AUTH_SITES = {
 	development: '',
  integration: 'https://sso.xfyun.cn',
  staging: 'https://sso.xfyun.cn',
  stagingHF: 'https://ssodev.xfyun.cn',
  production: 'https://sso.xfyun.cn',
 };

 const getAuthSite = (env) => {
  // if(env === 'development') {
  //   return ''
  // } else {
  //   if(location.host === 'teststudio.iflyos.cn') {
  //     return AUTH_SITES['stagingHF']
  //   }else {
  //     return AUTH_SITES['production']
  //   }
  // }
  return ''
 }

export default {
  AUTH_SITE: getAuthSite(),
  PASSPORT: 'xxx'
}
