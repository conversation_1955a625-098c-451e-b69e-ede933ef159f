import styles from '../form.module.scss'
import Input from '../Common/Input'
import Output from '../Common/Output'

function VariableGetNodeForm(props) {
  const { id, type, data, sourcePosition, isConnectable } = props

  return (
    <div className={styles.container}>
      <Input inputOrOutput={data?.input} label="输入" id={id} />
      <Output inputOrOutput={data?.output} keyProp="name" />
    </div>
  )
}
export default VariableGetNodeForm
