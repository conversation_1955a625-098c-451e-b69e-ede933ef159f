{"name": "sparkos-flow", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --mode base", "start:base": "vite --mode base", "start:auto": "vite --mode auto", "start:aiui": "vite --mode aiui", "dev": "vite --mode base", "build": "vite build --mode base", "build:base": "vite build --mode base", "build:auto": "vite build --mode auto", "build:aiui": "vite build --mode aiui", "lint": "eslint .", "preview": "vite preview", "postBuild": "node scripts/generateManifest.js"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@codemirror/commands": "^6.8.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.5", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "@xyflow/react": "^12.8.1", "ahooks": "^3.8.1", "antd": "^5.20.6", "antd-style": "^3.7.1", "async-validator": "^4.2.5", "axios": "^1.7.4", "classnames": "^2.5.1", "codemirror": "^6.0.1", "dagre": "^0.8.5", "dayjs": "^1.11.13", "js-base64": "^3.7.7", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "md5-js": "^0.0.3", "mitt": "^3.0.1", "prop-types": "^15.8.1", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "react-transition-group": "^4.4.5", "reset-css": "^5.0.2", "runes2": "^1.1.4", "uuid": "^10.0.0", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@module-federation/vite": "^1.2.6", "@tailwindcss/line-clamp": "^0.4.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.45", "sass": "^1.77.8", "sass-loader": "^16.0.1", "tailwindcss": "^3.4.11", "terser": "^5.39.0", "vite": "^5.4.1", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-static-copy": "^2.3.0", "vite-plugin-svgr": "^4.2.0", "vite-plugin-top-level-await": "^1.5.0"}}