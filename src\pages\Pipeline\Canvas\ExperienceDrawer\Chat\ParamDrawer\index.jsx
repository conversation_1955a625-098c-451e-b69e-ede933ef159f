import { Drawer } from 'antd'
import Header from './Header'
import Content from './Content'

function ParamDrawer(props) {
  const { onClose, paramDrawerVisible } = props

  const drawerStyles = {
    mask: {
      // backdropFilter: 'blur(10px)',
    },
    content: {
      // boxShadow: '-10px 0 10px #666',
    },
    header: {
      borderBottom: `1px solid #EAECF0`
    },
    body: {
      // fontSize: token.fontSizeLG,
      userSelect: 'text'
    },
    footer: {
      // borderTop: `1px solid ${token.colorBorder}`,
    }
  }
  return (
    <Drawer
      title={null}
      footer={null}
      height={'calc(100% - 70px)'}
      placement="bottom"
      closable={false}
      onClose={onClose}
      open={paramDrawerVisible}
      getContainer={false}
      destroyOnClose
      mask={false}
      rootStyle={{ position: 'absolute' }}
      width={420}
      styles={drawerStyles}
    >
      <div className="flex flex-col h-full">
        <Header {...props} onClose={onClose}/>
        <Content {...props} onClose={onClose}/>
      </div>
    </Drawer>
  )
}
export default ParamDrawer
