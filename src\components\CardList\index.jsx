import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { Input, Button, Pagination, Dropdown, Spin, Empty } from 'antd'
import ajax from '@/utils/http'
import PropTypes from 'prop-types'
import Card from './Card'
import styles from './style.module.scss'
import { LoadingOutlined } from '@ant-design/icons'
import CustomEmpty from '@/components/CustomEmpty'

const CardList = forwardRef(
  ({ title, searchConfig, cardConfig, dropdown, renderFooterTag, events }, ref) => {
    const [data, setData] = useState([])
    const [loading, setLoading] = useState(false)
    const [pagination, setPagination] = useState({ current: 1, pageSize: 9, total: 0 })
    const [searchValue, setSearchValue] = useState('')

    const fetchData = async (page) => {
      setLoading(true)
      try {
        const response = await ajax({
          url: searchConfig.url,
          method: searchConfig.method || 'get',
          data: {
            [searchConfig.searchKey]: searchValue,
            [searchConfig.pagination.page]: page || pagination.current,
            [searchConfig.pagination.pageSize]: pagination.pageSize
          },
          ...searchConfig.httpConfig
        })
        const formattedData = searchConfig.dataFormatter(response.data)
        setData(formattedData.list)
        setPagination((prev) => ({ ...prev, total: formattedData.total }))
      } catch (error) {
        console.error('Fetch data error:', error)
      } finally {
        setLoading(false)
      }
    }

    useEffect(() => {
      fetchData()
    }, [pagination.current, searchValue])

    useImperativeHandle(ref, () => ({
      reload: () => fetchData(1)
    }))

    const handleSearch = (value) => {
      setSearchValue(value)
      setPagination((prev) => ({ ...prev, current: 1 }))
    }

    const handlePaginationChange = (page) => {
      setPagination((prev) => ({ ...prev, current: page }))
    }

    return (
      <div className={styles.cardListContainer}>
        <div className={styles.header}>
          <h2>我的{title}</h2>
          <div className={styles.actions}>
            <Input.Search
              onSearch={handleSearch}
              placeholder={`搜索${title}...`}
              style={{ width: 206, marginRight: 14 }}
              allowClear
            />
            <Button type="primary" onClick={events?.onAddClick}>
              +&nbsp;创建{title}
            </Button>
          </div>
        </div>
        {loading ? (
          <Spin size="large" indicator={<LoadingOutlined spin />} className={styles.loading} />
        ) : data.length ? (
          <div className={styles['card-list']}>
            {data.map((item) => {
              let conf = {}
              Object.keys(cardConfig).forEach((k) => {
                if (item[cardConfig[k]]) {
                  conf[k] = item[cardConfig[k]]
                }
              })
              return (
                <Card
                  item={item}
                  key={item.id}
                  dropdown={dropdown}
                  renderFooterTag={renderFooterTag}
                  events={events}
                  {...conf}
                ></Card>
              )
            })}
          </div>
        ) : (
          <CustomEmpty />
        )}

        <div className={styles.pagination}>
          <Pagination
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onChange={handlePaginationChange}
            showSizeChanger={false}
            hideOnSinglePage={true}
          />
        </div>
      </div>
    )
  }
)

CardList.propTypes = {
  title: PropTypes.string.isRequired,
  searchConfig: PropTypes.shape({
    url: PropTypes.string.isRequired,
    method: PropTypes.oneOf(['get', 'post']).isRequired,
    dataFormatter: PropTypes.func.isRequired
  }),
  cardConfig: PropTypes.shape({
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    updateTime: PropTypes.string,
    logo: PropTypes.string
  }),
  dropdown: PropTypes.shape({
    optItems: PropTypes.array.isRequired,
    onDropItemsClick: PropTypes.func.isRequired
  }),
  events: PropTypes.shape({
    onAddClick: PropTypes.func.isRequired
  })
}

export default CardList
