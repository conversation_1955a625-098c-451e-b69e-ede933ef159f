import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import Schema from 'async-validator'
import { getTreeData } from '@/hooks/getTreeData'
import { validateNameRecursive } from './validateNameRecursive'
import useInputDataValidate from '@/hooks/useInputDataValidate'
// import useRefVariable from '@/hooks/useRefVariable'
import { validateVariableUtil } from '@/hooks/useRefVariable'

const validateNodeWithRules = async (node, rules, nodes, edges) => {
  // const { validateVariable } = useRefVariable()

  const treeData = getTreeData(node, nodes, edges)
  const { validateInputData } = useInputDataValidate(treeData)

  const inputKeyValidate = (node) => {
    // 校验该变量是否被 提示词所使用

    const validateInputKey = (rule, value, cb, source) => {
      // 官方大模型能力，没有提示词，不需要校验该项
      if (node.data.abilityOriginId) {
        return Promise.resolve()
      }

      if (value) {
        if(['user', 'system', 'app', 'local'].includes(value)) {
          return Promise.reject(new Error('变量名不能是系统保留关键字'))
        }
        const promptVal = node.data['prompt'] || ''
        const systemPromptVal = node.data['systemPrompt'] || ''

        const valid = validateVariableUtil(
          { key: source?.key, value: source?.value },
          `${promptVal}${systemPromptVal}`,
          nodes
        )
        if (!valid) {
          return Promise.reject(new Error('该变量未被提示词引用'))
        }

        // if (
        //   !(
        //     (promptVal || '').includes(`{${value}}`) ||
        //     (systemPromptVal || '').includes(`{${value}}`)
        //   )
        // ) {
        //   return Promise.reject(new Error('该变量未被提示词引用'))
        // }
      } else {
        return Promise.reject('参数不可为空')
      }
      return Promise.resolve()
    }
    return validateInputKey
  }

  const validateInputKey = inputKeyValidate(node)

  let errors = []
  for (const field in rules) {
    // 针对 field  是否是output等特殊类型
    if (field === 'output') {
      // 这里为方便处理，且考虑项目中实际，只对name进行校验
      for (const [index, data] of node.data?.[field]?.entries()) {
        const dataErrors = await validateNameRecursive(data, index, node.data?.[field])
        errors.push(...dataErrors)
      }
    } else {
      const rule =
        field === 'input' ? rules[field](validateInputData, validateInputKey) : rules[field]
      const validator = new Schema({ [field]: rule })
      try {
        await validator.validate({ [field]: node.data?.[field] })
      } catch ({ errors: validationErrors }) {
        if (validationErrors) {
          errors.push(...validationErrors)
        }
      }
    }
  }
  return errors
}

export const LLMNodeStrategy = async (node, nodes, edges) => {
  // 大模型节点需要检查是否为官方（模版），官方的不需要校验 某些字段（prompt， systemPrompt没有）
  const isOfficial = !!node?.data?.abilityOriginId
  const rule = getRules(NODETYPES.LLM, { isOfficial })
  const errors = await validateNodeWithRules(node, rule, nodes, edges)
  return errors
}
