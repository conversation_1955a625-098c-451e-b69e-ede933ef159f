import { Input, Popover, Button, message, Empty, Tooltip, Popconfirm } from 'antd'
import IconPlus from 'assets/svgs/plus.svg?react'
import { ApiOutlined, SearchOutlined, ToolOutlined } from '@ant-design/icons'
import styles from '../style.module.scss'
import IconTemplate from 'assets/svgs/template.svg?react'
import IconModel from 'assets/svgs/model-lite.svg?react'
import IconWorkflow from 'assets/svgs/workflow-lite.svg?react'
import { useEffect, useState, useRef, useDeferredValue } from 'react'
import ajax from '@/utils/http'
import { NODETYPES } from '@/utils/constant'
import eventBus from '@/utils/eventBus'

import { debounce } from 'lodash'
import { useParams } from 'react-router-dom'
import { NodeAdapterFactory } from '@/utils/adapter/NodeAdapterFactory'
import { APP_ENV } from '@/utils/constant'
import CustomEmpty from '@/components/CustomEmpty'

const INIT_COUNT = 4

const ListItem = (props) => {
  const { nodeType, title, official, ability } = props
  const { buttonRef, onDeleted, ...rest } = props

  const onPlusClick = () => {
    // let data = {}
    let param = {}
    const adapter = NodeAdapterFactory.getAdapter(rest.nodeType)
    if (adapter) {
      param = adapter.fromBackToFront(rest)
    }

    // 仿照coze，将新节点插入在模板或者节点弹框的右侧

    const rect = buttonRef.current?.getBoundingClientRect()
    const position = {
      // 360为popover的宽度，20为取一个偏移值
      x: rect.left + rect.width / 2 + 420 / 2 + 20,
      y: Math.floor(Math.random() * 500)
    }

    eventBus.emit('INSERT_NODE', { type: nodeType, position, data: param?.data })
  }

  const onDelete = async () => {
    console.log('delete', props)
    const result = await ajax({
      url: `/workflow/node/template/delete?backendId=${props.backendId}`,
      method: 'get'
    })
    if (result.data?.code === '0') {
      message.success('删除成功')
      onDeleted?.()
      // fetch(1)
    }
  }

  const onDragStart = (event, props) => {
    const { nodeType } = props
    const { buttonRef, ...rest } = props

    let param = {}
    const adapter = NodeAdapterFactory.getAdapter(rest.nodeType)
    if (adapter) {
      param = adapter.fromBackToFront(rest)
    }

    const eventPageX = event.clientX || event.pageX
    const eventPageY = event.clientY || event.pageY
    const domRect = event.target.getBoundingClientRect()
    const pageX = domRect.x || domRect.left
    const pageY = domRect.y || domRect.top
    event.dataTransfer.setData(
      'application/reactflow',

      JSON.stringify({
        nodeType,
        delta: { x: eventPageX - pageX, y: eventPageY - pageY },
        data: param?.data
      })
    )
    event.dataTransfer.effectAllowed = 'move'
  }

  return (
    <Tooltip placement={props.index % 2 === 0 ? 'left' : 'right'} title={props.description}>
      <div
        className={`${styles.listItem}`}
        onDragStart={(event) => onDragStart(event, props)}
        draggable
        onClick={onPlusClick}
      >
        <div className={styles.leftContent}>
          <div>
            <span className={styles.itemTitle} title={title} style={{ maxWidth: 130 }}>
              {title}
            </span>
            {ability.id && <p className={styles.itemDesc}>{ability.id}</p>}
          </div>
        </div>
        {official && <div className={styles.tagOfficial}>官方</div>}
        {!official && <div className={styles.tagCustom}>个人</div>}
        {/* {!official && <div className={styles.closeBtn}>×</div>} */}
        {!official && nodeType !== NODETYPES.FLOW && (
          <Popconfirm
            title={null}
            description="删除模版将无法恢复，但不会影响已发布工作流"
            onConfirm={onDelete}
            okText="删除"
            okButtonProps={{ loading: false }}
            cancelText="取消"
            onPopupClick={(e) => e.stopPropagation()}
          >
            <div className={styles.closeBtn} onClick={(e) => e.stopPropagation()}>
              ×
            </div>
          </Popconfirm>
        )}
      </div>
    </Tooltip>
  )
}

const SubContent = () => {
  const [template, setTemplate] = useState([])
  const [searchVal, setSearchVal] = useState('')
  const [modelData, setModelData] = useState([])
  const deferedModelData = useDeferredValue(modelData)
  const [flowData, setFlowData] = useState([])
  const deferedFlowData = useDeferredValue(flowData)

  // 插件
  const [pluginData, setPluginData] = useState([])
  const deferedPluginData = useDeferredValue(pluginData)

  const [showAllModel, setShowAllModel] = useState(false)
  const [showAllWorkflow, setShowAllWorkflow] = useState(false)
  const [showAllPlugin, setShowAllPlugin] = useState(false)

  const { id } = useParams()

  const buttonRef = useRef(null)

  const onModelToggle = () => {
    setShowAllModel((s) => !s)
  }

  const onFlowToggle = () => {
    setShowAllWorkflow((s) => !s)
  }

  const getTemplateOther = () => {
    ajax({
      url: `/workflow/node/template/other`,
      method: 'get',
      data: {
        id
      }
    }).then((res) => {
      if (res.data.code === '0') {
        const template = res.data.data || []
        setTemplate(template)
        setModelData(template.find((item) => item.id === NODETYPES.LLM)?.nodes || [])
        setFlowData(template.find((item) => item.id === NODETYPES.FLOW)?.nodes || [])
        // setPluginData(template.find((item) => item.id === NODETYPES.API)?.nodes || [])
        setPluginData(template.find((item) => item.id === 'tool')?.nodes || [])
      }
    })
  }

  useEffect(() => {
    getTemplateOther()

    // 订阅事件
    eventBus.on('REFRESH_TEMPLATE', getTemplateOther)
    // 清理订阅
    return () => {
      eventBus.off('REFRESH_TEMPLATE', getTemplateOther)
    }
  }, [])

  useEffect(() => {
    setModelData(
      template
        .find((item) => item.id === NODETYPES.LLM)
        ?.nodes?.filter(searchVal ? (nd) => nd.title.includes(searchVal) : Boolean)
    )
    setFlowData(
      template
        .find((item) => item.id === NODETYPES.FLOW)
        ?.nodes?.filter(searchVal ? (nd) => nd.title.includes(searchVal) : Boolean)
    )
  }, [searchVal])

  const onInputChange = (e) => {
    setSearchVal(e.target.value)
  }

  const onDeleted = () => {
    getTemplateOther()
  }

  const content = (
    <div className="pb-2">
      <div className={styles.inputContainer}>
        <Input
          placeholder="搜索节点"
          variant="filled"
          allowClear
          prefix={<SearchOutlined style={{ color: '#98A2B2' }} />}
          onChange={debounce(onInputChange, 100)}
        />
      </div>

      <div className={styles.listContainer}>
        <div className={styles.headTitle}>
          <IconModel />
          <span>大模型</span>
        </div>

        {deferedModelData?.length > 0 ? (
          <>
            <div className={styles.listWrap}>
              {deferedModelData.map((item, index) =>
                index <= INIT_COUNT - 1 || (index > INIT_COUNT - 1 && showAllModel) ? (
                  <ListItem
                    key={index}
                    index={index}
                    buttonRef={buttonRef}
                    onDeleted={onDeleted}
                    {...item}
                  />
                ) : null
              )}
            </div>
            {deferedModelData.length > INIT_COUNT && (
              <div onClick={onModelToggle} className={styles['toggle-button']}>
                {showAllModel ? '收起' : '更多大模型...'}
              </div>
            )}
          </>
        ) : (
          <CustomEmpty description={<span>暂无数据</span>} />
        )}
        <div className={styles.headTitle}>
          <IconWorkflow />
          <span>工作流</span>
        </div>
        {deferedFlowData?.length > 0 ? (
          <>
            <div className={styles.listWrap}>
              {deferedFlowData.map((item, index) =>
                index <= INIT_COUNT - 1 || (index > INIT_COUNT - 1 && showAllWorkflow) ? (
                  <ListItem key={index} index={index} buttonRef={buttonRef} {...item} />
                ) : null
              )}
            </div>
            {deferedFlowData.length > INIT_COUNT && (
              <div onClick={onFlowToggle} className={styles['toggle-button']}>
                {showAllWorkflow ? '收起' : '更多工作流...'}
              </div>
            )}
          </>
        ) : (
          <CustomEmpty description={<span>暂无数据</span>} />
        )}

        {/* 插件-工具 */}
        <div className={styles.headTitle}>
          <ApiOutlined style={{ color: '#F7840C' }} />
          <span>{APP_ENV === 'auto' ? '智能体' : '插件'}</span>
        </div>
        {deferedPluginData?.length > 0 ? (
          <>
            <div className={styles.listWrap}>
              {deferedPluginData.map((item, index) =>
                index <= INIT_COUNT - 1 || (index > INIT_COUNT - 1 && showAllPlugin) ? (
                  <ListItem key={index} index={index} buttonRef={buttonRef} {...item} />
                ) : null
              )}
            </div>
            {deferedPluginData.length > INIT_COUNT && (
              <div onClick={onFlowToggle} className={styles['toggle-button']}>
                {showAllPlugin ? '收起' : `更多${APP_ENV === 'auto' ? '智能体' : '插件'}...`}
              </div>
            )}
          </>
        ) : (
          <CustomEmpty description={<span>暂无数据</span>} />
        )}
      </div>
    </div>
  )
  return (
    <Popover
      placement="top"
      arrow={false}
      trigger="click"
      title={null}
      content={content}
      overlayClassName="sub-overlay"
    >
      <div className={styles['button']} ref={buttonRef}>
        <IconTemplate />
        <span>模板</span>
      </div>
    </Popover>
  )
}
export default SubContent
