import { useEffect } from 'react'
import { useParams } from 'react-router-dom'
import WorkflowWrapper from './WorkflowWrapper'
import ajax from '@/utils/http'
import { useOfficialAbilityStore } from '@/store/officialAbility'
import { useTemplateStore } from '@/store/template'
import { useKnowledgeStore } from '@/store/knowledge'
import { useModelStore } from '@/store/model'
import { useKnowledgeRepoStore } from '@/store/knowledgeRepo'
import { COG_DOMAINS } from '@/utils/constant'
import { NODETYPES } from '../../utils/constant'

function Pipeline() {
  const { id } = useParams()
  const setAbilities = useOfficialAbilityStore((state) => state.setAbilities)
  const setTemplate = useTemplateStore((state) => state.setTemplate)
  const setKnowledge = useKnowledgeStore((state) => state.setKnowledge)
  const setModel = useModelStore((state) => state.setModel)
  const setMaaSModel = useModelStore((state) => state.setMaaSModel)
  const setKnowledgeRepos = useKnowledgeRepoStore((state) => state.setKnowledgeRepos)

  useEffect(() => {
    // 两个与本页面无关的获取数据源的请求，
    fetchModels()
    fetchKnowledges()
    //获取官方能力
    fetchAbilities()
    // 获取模板
    fetchTemplates()

    // 获取工作流中可配置的所有知识库
    fetchKnowledgeRepos()
  }, [])

  const fetchAbilities = () => {
    ajax({
      url: `/workflow/official/ability`,
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        const abilities = res.data?.data?.ability || []
        setAbilities(abilities)
      }
    })
  }

  const fetchTemplates = () => {
    ajax({
      url: `/workflow/node/template`,
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        // 尝试修改
        const template = (res.data.data || []).map((item) => {
          if (item.id === 'tool') {
            return {
              ...item,
              nodes: (item.nodes || []).map((it) => {
                if (
                  it.nodeType === NODETYPES.VARIABLE_SET ||
                  it.nodeType === NODETYPES.VARIABLE_GET
                ) {
                  return {
                    ...it,
                    input: {
                      '': {
                        path: '',
                        ref: '',
                        desc: { schema: { type: 'string' }, format: 'plain' }
                      }
                    },
                    output: { '': { desc: { schema: { type: 'string' }, format: 'plain' } } }
                  }
                } else if (it.nodeType === NODETYPES.FUNCTION) {
                  return {
                    ...it,
                    subType: it.id
                  }
                } else {
                  return {
                    ...it
                  }
                }
              })
            }
          } else {
            return { ...item }
          }
        })
        setTemplate(template)
      }
    })
  }

  // 获取知识库模型
  const fetchKnowledges = () => {
    ajax({
      url: '/app/pluginStudio/getCbmKnowledgeDomains',
      data: {},
      method: 'get'
    }).then((res) => {
      setKnowledge(
        Object.keys(res.data?.data).map((k) => {
          return {
            label: res.data?.data[k],
            value: k
          }
        })
      )
    })
  }

  // const fetchModels = () => {
  //   ajax({
  //     url: '/workflow/model/list',
  //     data: {},
  //     method: 'get'
  //   }).then((res) => {
  //     const myModel = (res.data?.data?.model || [])
  //       .concat(
  //         COG_DOMAINS.map((it) => {
  //           return {
  //             domain: it,
  //             icon: '',
  //             modelType: 'lite',
  //             name: it,
  //             patchId: it,
  //             serverId: it,
  //             serviceId: it
  //           }
  //         })
  //       )
  //       .map((item) => {
  //         const uuid = `${item.domain ?? ''}_${item.patchId ?? ''}_${item.modelType ?? ''}`
  //         return {
  //           ...item,
  //           label: item.name,
  //           value: uuid
  //         }
  //       })
  //     setModel([...myModel])
  //   })
  // }

  /**
   * 拉取模型列表接口不太稳定，前端先兼容处理
   * @param {*} retryCount
   * @returns
   */

  const fetchModels = async (retryCount = 3) => {
    try {
      const res = await ajax({
        url: '/workflow/model/list',
        data: {},
        method: 'get'
      })
      if (retryCount > 0 && (res.data?.data?.model || []).length === 0) {
        await new Promise((resolve) => setTimeout(resolve, 500)) // 延迟 1s 重试
        return fetchModels(retryCount - 1)
      }

      const myModel = (res.data?.data?.model || [])
        .concat(
          COG_DOMAINS.map((it) => {
            return {
              domain: it,
              icon: '',
              modelType: 'lite',
              name: it,
              patchId: it,
              serverId: it,
              serviceId: it
            }
          })
        )
        .map((item) => {
          const uuid = `${item.domain ?? ''}_${item.patchId ?? ''}_${item.modelType ?? ''}`
          return {
            ...item,
            label: item.name,
            value: uuid
          }
        })
      setMaaSModel(res.data?.data?.model || [])
      setModel([...myModel])
    } catch (err) {
      
    }
  }

  const fetchKnowledgeRepos = () => {
    ajax({
      url: `/workflow/getKnowledges`,
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        const knowledges = res.data?.data?.knowledges || []
        setKnowledgeRepos(knowledges)
      }
    })
  }
  return <WorkflowWrapper workflowId={id} />
}
export default Pipeline
