import { nodesMap, NODETYPES, CONCURRENT_DIMENSION } from '@/utils/constant'
import IconTimeExtraction from 'assets/svgs/time-extraction.svg?react'
import IconDataConvert from 'assets/svgs/data-convert.svg?react'

const getNodeIconComponent = (type, id) => {
  let Component
  if (type === NODETYPES.FUNCTION) {
    if (id === 'cbm_function_time' || id === 'function_time') {
      Component = IconTimeExtraction
    } else if (id === 'cbm_function_express' || id === 'function_express') {
      Component = IconDataConvert
    } else {
      Component = nodesMap[type || 'llm'].component
    }
  } else {
    Component = nodesMap[type || 'llm'].component
  }
  return Component
}

export default getNodeIconComponent
