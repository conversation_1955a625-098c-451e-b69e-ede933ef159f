import React, { useState } from 'react'
import {
  Collapse,
  Form,
  Input,
  Button,
  InputNumber,
  Switch,
  message,
  Space,
  Empty,
  Select
} from 'antd'
import { useEffect } from 'react'
import { useNodesData } from '@xyflow/react'
import useInput from '@/hooks/useInput'
import { useNodes, useReactFlow } from '@xyflow/react'
import styles from './style.module.scss'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import KeyProp from '@/components/KeyProp'
import { ENTRY_SOURCE_ID } from '@/utils/constant'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import JsonEditor from '@/components/JsonEditor'

const Content = ({ paramData, onData, onClose }) => {
  const nodeData = useNodesData(ENTRY_SOURCE_ID)
  // const originInput = useInput(nodeData.data?.input)
  // 只接收引用的变量测试，自输入变量值默认附带
  // const input = (originInput || []).filter((item) => item.value?.ref === 'ref')
  // 过滤给用户输入的`text`参数
  const input = (nodeData.data?.input || [])
    .filter(Boolean)
    .filter((itm) => itm.name && itm.name !== 'text')
  console.log('------------------------input is ------', input)

  const [form] = Form.useForm()

  useEffect(() => {
    form &&
      form.setFieldsValue({
        params: input.map((item) => ({
          [item.name]: paramData[item.name]
        }))
      })
  }, [form, nodeData.data?.input])

  const saveConf = () => {
    // 先校验
    const inputData = ((form.getFieldsValue() || {}).params || []).map(
      ({ key, value, type, ...rest }) => {
        return {
          ...rest
        }
      }
    )

    onData?.(Object.assign({}, ...inputData))
  }

  const getFormItemByType = (type, name) => {
    switch (type) {
      case 'string':
        return (
          <Input.TextArea
            placeholder={`请输入${name}参数`}
            rows={3}
            style={{ width: '100%' }}
            maxLength={10000}
          />
        )
      case 'number':
      case 'integer':
        return <InputNumber  style={{ width: '100%' }}/>
      case 'boolean':
        return (
          // <Switch
          //   checkedChildren={
          //     <span>
          //       True
          //       <CheckOutlined />
          //     </span>
          //   }
          //   unCheckedChildren={
          //     <span>
          //       False
          //       <CloseOutlined />
          //     </span>
          //   }
          // />
          <Select
            options={[
              { value: true, label: 'True' },
              { value: false, label: 'False' }
            ]}
          ></Select>
        )
      default:
        return <JsonEditor />
    }
  }

  // Collapse 的 items 配置
  const collapseItems = [
    {
      key: '1',
      label: '入参',
      children: (
        <Form form={form} requiredMark={false} layout="vertical">
          <Form.List name="params">
            {(fields) => {
              return (
                <>
                  {fields.map(({ key, name, ...restField }) => {
                    return (
                      <Form.Item
                        {...restField}
                        name={[name, input[name].name]}
                        label={
                          <>
                            <KeyProp
                              value={{
                                type: input[name].type,
                                name: input[name].name,
                                required: false
                              }}
                            />
                          </>
                        }
                      >
                        {/* <Input.TextArea
                          placeholder={`请输入${input[name].name}参数`}
                          rows={3}
                          style={{ width: '100%' }}
                          maxLength={1000}
                        /> */}
                        {getFormItemByType(input[name].type, input[name].name)}
                      </Form.Item>
                    )
                  })}
                </>
              )
            }}
          </Form.List>
        </Form>
      )
    }
  ]

  return (
    <>
      <Collapse
        items={collapseItems}
        defaultActiveKey={['1', '2']}
        ghost
        size="small"
        className="flex-1 pr-[20px] mr-[-24px] overflow-auto max-h-[calc(100vh-280px)]"
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
      />
      <div>
        <Button onClick={saveConf} style={{ marginTop: 16 }}>
          保存配置
        </Button>
      </div>
    </>
  )
}

export default Content
