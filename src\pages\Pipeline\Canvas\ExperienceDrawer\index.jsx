import { Drawer } from 'antd'
import { memo, useState } from 'react'
import Head<PERSON> from './Header'
import Chat from './Chat'

export default memo((props) => {
  const { onClose, visible } = props

  const drawerStyles = {
    mask: {
      // backdropFilter: 'blur(10px)',
    },
    content: {
      // boxShadow: '-10px 0 10px #666',
    },
    header: {
      borderBottom: `1px solid #EAECF0`
    },
    body: {
      // fontSize: token.fontSizeLG,
      background: '#f7f8fa',
      userSelect: 'text'
    },
    footer: {
      // borderTop: `1px solid ${token.colorBorder}`,
    }
  }

  return (
    <Drawer
      title={null}
      footer={null}
      placement="right"
      closable={false}
      onClose={onClose}
      open={visible}
      getContainer={false}
      destroyOnClose
      mask={false}
      rootStyle={{ position: 'absolute' }}
      width={420}
      styles={drawerStyles}
      zIndex={2000}
    >
      <div className="flex flex-col h-full">
        <Header {...props} />
        <Chat {...props} />
      </div>
    </Drawer>
  )
})
