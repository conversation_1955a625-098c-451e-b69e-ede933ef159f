import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'
import { MarkerType, Position, getOutgoers } from '@xyflow/react'


// 1、后端给定的 大模型 节点模板
const choiceTemplate = {
  title: '分支器',
  description: '连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行“否则”分支',
  nodeType: 'choice',
  choice: [
    {
      type: 'is',
      rule: [
        {
          variable: '',
          const: '',
          func: ''
        }
      ],
      next: ''
    }
  ],
  // else 分支指向节点的id
  else: ''
}

/**
 * 将选择器后端模板转成前端data
 * @param {*} template
 */
const convertChoiceTemplateFromBack2Front = function (template = choiceTemplate) {
  const { title, ...rest } = template
  return {
    name: title,
    ...rest
  }
}

/**
 * 将前端data转成后端协议结构
 * @param {*} data
 */
const convertChoiceTemplateFromFront2Back = function (data = {}) {
  const newData = {}
  return newData
}

export class ChoiceNodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    if (nd.else) {
      // 添加之前看有没有，存在不要重复添加,

      // const findIndex = originEdges.findIndex(
      //   (edge) =>
      //     edge.source === nd.id &&
      //     edge.target === nd.else &&
      //     edge.sourceHandle === `source-else` &&
      //     edge.targetHandle === 'target'
      // )
      // if (findIndex === -1) {
      //   originEdges.push({
      //     id: `xyflow__edge-${nd.id}-source-else-${nd.else}`,
      //     source: nd.id,
      //     target: nd.else,
      //     sourceHandle: `source-else`,
      //     targetHandle: 'target'
      //   })
      // }
    }
    param = {
      ...param,
      data: {
        name: nd.title,
        description: nd.description,
        else: nd.else,
        choice: (nd.choice || []).map((item, index) => {
          if (item.next) {
            // const findIndex = originEdges.findIndex(
            //   (edge) =>
            //     edge.source === nd.id &&
            //     edge.target === item.next &&
            //     edge.sourceHandle === `source-if_${index}` &&
            //     edge.targetHandle === 'target'
            // )
            // if (findIndex === -1) {
            //   originEdges.push({
            //     id: `xyflow__edge-${nd.id}-source-if_${index}-${item.next}`,
            //     source: nd.id,
            //     target: item.next,
            //     sourceHandle: `source-if_${index}`,
            //     targetHandle: 'target'
            //   })
            // }
          }
          return {
            ...item,
            // type默认设置为 “is”
            type: item.type || 'is',
            rule: (item.rule || []).map((r) => {
              return {
                ...r,
                func: r.func ? r.func : undefined,
                const: r.const ? r.const : undefined,
                variable:
                  r.variable?.ref && r.variable?.path
                    ? `${r.variable?.ref}.${r.variable?.path}`
                    : undefined
              }
            }),
          }
        })
      }
    }
    return param
  }

  static fromFrontToBack(nd, nodes, edges) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    param = {
      ...param,
      title: data?.name,
      description: data?.description,
      type: nd.type,
      else:
        (edges || [])
          .filter((eg) => eg.source === nd.id)
          .find((eg) => eg.sourceHandle === 'source-else')?.target || '',
      choice: (data?.choice || []).map((item, index) => {
        if (item) {
          return {
            ...item,
            rule: (item.rule || []).map((r) => {
              if (r) {
                return {
                  ...r,
                  variable:
                    r.variable && typeof r.variable === 'string'
                      ? {
                          ref: r.variable.split('.')[0],
                          path: r.variable.split('.').slice(1).join('.')
                        }
                      : {
                          ref: '',
                          path: ''
                        }
                }
              } else {
                return r
              }
            }),
            next:
              (edges || [])
                .filter((eg) => eg.source === nd.id)
                .find(
                  (eg) =>
                    eg.sourceHandle.includes('source-if_') &&
                    eg.sourceHandle.split('_') &&
                    eg.sourceHandle.split('_')[1] === String(index)
                )?.target || ''
          }
        } else {
          return item
        }
      })
    }
    return param
  }
}
