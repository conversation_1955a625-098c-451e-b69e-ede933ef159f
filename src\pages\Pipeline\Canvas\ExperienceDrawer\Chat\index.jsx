import React, { useState, useEffect, useRef } from 'react'
import styles from './style.module.scss'
import Inputing from './Inputing'
import MarkdownIt from 'markdown-it'
import { Divider, message, Space, Tooltip } from 'antd'

import IconSend from '@/assets/images/send.png'

import { fetchEventSource } from '@microsoft/fetch-event-source'
import handleLoginExpire from '@/utils/handleLoginExpire'
import genUUID from '@/utils/genUUID'
import { DeleteOutlined, SettingOutlined } from '@ant-design/icons'
import { useNodes, useReactFlow } from '@xyflow/react'
import { NODETYPES, ENTRY_SOURCE_ID, EXIT_SOURCE_ID } from '@/utils/constant'
import eventBus from '@/utils/eventBus'
import { useTimeStore } from '../store'
import ParamDrawer from './ParamDrawer'
import { useNodesData } from '@xyflow/react'

const md = new MarkdownIt({ breaks: true })

const Chat = ({ flowId }) => {
  const setTime = useTimeStore((state) => state.setTime)
  const nodes = useNodes()
  const { setNodes, updateNodeData } = useReactFlow()
  const uuid = useRef(genUUID())
  const [inputText, setInputText] = useState('')
  const [chatList, setChatList] = useState([
    {
      people: 'ai',
      con: '你好，我是智能的小飞~',
      type: 'text',
      logId: '',
      preset: true
    }
  ])
  const [isReplying, setIsReplying] = useState(false)
  //   const [showJson, setShowJson] = useState(false)
  //   const [resJson, setResJson] = useState({})

  const chatboxRef = useRef(null)

  const abortController = useRef(new AbortController())

  useEffect(() => {
    return () => {
      stopResponseSSE()
    }
  }, [])

  useEffect(() => {
    scrollMessage()
  }, [chatList])

  const stopResponseSSE = () => {
    abortController.current.abort()
    abortController.current = new AbortController()
  }

  const scrollMessage = () => {
    if (chatboxRef.current) {
      chatboxRef.current.scrollTop = chatboxRef.current.scrollHeight
    }
  }

  const handleInputChange = (e) => {
    setInputText(e.target.value)
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMsgSse()
    }
  }

  const sendMsgSse = () => {
    if (isReplying) {
      message.error('请让机器人回答完问题再进行回复')
      return
    }
    if (!inputText.trim()) {
      message.error('输入文本不能为空')
      return
    }
    if (inputText.length > 1000) {
      message.error('输入文本不能超过1000字符')
      return
    }

    // 这里可以清空各节点的debugData
    nodes.forEach((nd) => {
      eventBus.emit('DEBUG_DATA', { id: nd.id, debugData: null })
    })

    const newChatList = [
      ...chatList,
      { people: 'me', con: inputText, type: 'text' },
      { people: 'ai', type: 'loading' }
    ]
    setChatList(newChatList)
    setIsReplying(true)

    let baseUrl = `${import.meta.env.VITE_API_BASE_URL}/user/chat`

    let formData = new FormData()
    formData.append('version', 'vflowtest')
    formData.append('expUid', uuid.current)
    formData.append('input', JSON.stringify(Object.assign({ text: inputText }, paramData.current)))
    formData.append('flowId', flowId)
    // flowId

    setInputText('')
    let headers = {}
    if (window.microApp?.getData()?.cookie) {
      headers['X-Auto-Token'] = window.microApp?.getData()?.cookie
    }
    fetchEventSource(baseUrl, {
      method: 'POST',
      openWhenHidden: true,
      body: formData,
      headers,
      async onopen(response) {
        if (response.ok) {
          console.log('连接了')
          setIsReplying(true)
        } else if (response.status === 401) {
          console.error('体验请求过期！！！！！！！！！！！！')
          handleLoginExpire()
        }
      },

      onmessage(event) {
        try {
          const result = JSON.parse(event.data || '{}')
          console.log(result)
          // 处理每一条信息
          if (result.code == '300001') {
            handleLoginExpire()
            setIsReplying(false)
          } else if (result.code == '0') {
            const data = result.data
            handleMessage(data)
          } else if (result.code == '300017') {
            let targetNode = nodes.find(
              (nd) => `cbm_${nd.data?.abilityId}` === result.data?.error?.id
            )
            if (!targetNode) {
              targetNode = nodes.find((nd) => nd.id === result.data?.error?.id)
            }
            // if(result.data?.error?.id === EXIT_SOURCE_ID) {
            // targetNode = nodes.find(
            //   (nd) => nd.id === result.data?.error?.id
            // )
            // } else {
            // targetNode = nodes.find(
            //   (nd) => `cbm_${nd.data?.abilityId}` === result.data?.error?.id
            // )
            // }

            if (targetNode) {
              eventBus.emit('DEBUG_DATA', {
                id: targetNode.id,
                debugData: { nodeStatus: 'error', logId: 'error', text: result.data?.error?.msg }
              })
            }

            setChatList((chatList) => chatList.filter((it) => it.type !== 'loading'))
            setIsReplying(false)
          } else {
            message.error(result.desc || '未知错误')
            setChatList((chatList) => chatList.filter((it) => it.type !== 'loading'))
            setIsReplying(false)
          }
        } catch (e) {
          console.log('打印体验', e)
          setIsReplying(false)
        }
      },
      onclose() {
        console.info('断开了')
        setIsReplying(false)
      },
      onerror(err) {
        // console.info('报错了')
        // throw new Error(err)
        console.log(err)
        setIsReplying(false)
        throw err
      },
      signal: abortController.current.signal
    })
  }

  const handleMessage = (data) => {
    console.log(data)
    if (data.finish || data.nlpFinish) {
      setIsReplying(false)
    }

    if (data.type === 'nlp') {
      // console.log(data)
      setChatList((message) => {
        const index = message.findIndex((item) => item.id === data.logId)

        if (index === -1) {
          // 没找到第一次添加回复数据

          const newMessage = {
            id: data.logId,
            people: 'ai',
            con: data.text
          }
          return [...message.filter((it) => it.type !== 'loading'), newMessage]
        } else {
          return message
            .filter((it) => it.type !== 'loading')
            .map((item) => {
              if (item.people !== 'ai') {
                return item
              } else {
                return {
                  ...item,
                  con: item.id === data.logId ? item.con + data.text : item.con
                }
              }
            })
        }
      })
    }
    const targetNode =
      data.type === 'nlp'
        ? nodes.find((nd) => nd.type === NODETYPES.EXIT)
        : nodes.find((nd) => `cbm_${nd.data?.abilityId}` === data.type)
    // const targetNode =
    //   data.type === 'nlp'
    //     ? nodes.find((nd) => nd.type === NODETYPES.EXIT)
    //     : nodes.find((nd) => nd.id === data.type)

    if (targetNode) {
      eventBus.emit('DEBUG_DATA', { id: targetNode.id, debugData: data })

      // 最后一帧 nodeStatus为true，allTime有值，设置链路耗时
      if (data?.nodeStatus && data?.allTime) {
        setTime(data?.allTime)
      }
    }
  }

  const cleanHistory = () => {
    // 这里可以清空各节点的debugData
    nodes.forEach((nd) => {
      eventBus.emit('DEBUG_DATA', { id: nd.id, debugData: null })
    })
    setTime(0)
    setChatList(chatList.filter((item) => item.preset))
    uuid.current = genUUID()
    stopResponseSSE()
    setIsReplying(false)
  }

  //   const copyJson = (data) => {
  //     navigator.clipboard.writeText(JSON.stringify(data, null, '    '))
  //   }

  const [paramDrawerVisible, setParamDrawerVisible] = useState(false)

  const paramData = useRef({})

  const paramSet = () => {
    setParamDrawerVisible(true)
  }

  const onCloseParamDrawer = () => {
    setParamDrawerVisible(false)
  }

  const onParamData = (data) => {
    console.log('onParamData', data)
    paramData.current = data || {}
    setParamDrawerVisible(false)
  }

  const nodeData = useNodesData(ENTRY_SOURCE_ID)
  const input = (nodeData.data?.input || [])
    .filter(Boolean)
    .filter((itm) => itm.name && itm.name !== 'text')

  return (
    <>
      <div className={`${styles['content-right']}`}>
        <div className={styles['rightChatboxContainer']}>
          <div className={styles['rightChatbox']} ref={chatboxRef}>
            {chatList.map((item, index) => (
              <div key={index} className={`${styles.chatbox} ${styles[item.people]}`}>
                <div className={styles.con}>
                  {item.type === 'loading' ? (
                    <>
                      <div className={styles['icon-ai']}></div>
                      <div>
                        <span>正在处理</span>&nbsp;
                        <Inputing />
                      </div>
                    </>
                  ) : (
                    <div>
                      {item.people === 'me' ? (
                        <>
                          <div className={styles['icon-me']}></div>
                          {item.con}
                        </>
                      ) : (
                        <>
                          <div className={styles['icon-ai']}></div>
                          <div dangerouslySetInnerHTML={{ __html: md.render(item.con || '') }} />
                        </>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className={styles['control-bar-wrap']}>
          <div className={styles['control-bar-input']}>
            <div className={styles.sendWrapper}>
              <textarea
                value={inputText}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="请在此处输入文本"
              />
              <div className={styles.send} onClick={sendMsgSse}>
                <img src={IconSend} alt="Send" />
              </div>
              <div className={styles.delete}>
                {input.length > 0 ? (
                  <Space size={0} split={<Divider type="vertical" />}>
                    <Tooltip title="入参配置">
                      <SettingOutlined
                        className="text-[--flow-desc-color] cursor-pointer"
                        onClick={paramSet}
                      />
                    </Tooltip>
                    <Tooltip title="清空历史">
                      <DeleteOutlined
                        className="text-[--flow-desc-color] cursor-pointer"
                        onClick={cleanHistory}
                      />
                    </Tooltip>
                  </Space>
                ) : (
                  <Tooltip title="清空历史">
                    <DeleteOutlined
                      className="text-[--flow-desc-color] cursor-pointer"
                      onClick={cleanHistory}
                    />
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </div>
        {/* {showJson && (
        <div className={styles['debug-json-dialog']}>
          <div className={styles['request-json']}>
            <i
              className={styles['ic-r-copy']}
              onClick={() => copyJson(resJson)}
              title="复制代码"
            ></i>
            <pre className={styles['json-wrap']}>{JSON.stringify(resJson, null, 2)}</pre>
          </div>
        </div>
      )} */}
      </div>
      <ParamDrawer
        paramDrawerVisible={paramDrawerVisible}
        paramData={paramData.current}
        onClose={onCloseParamDrawer}
        onData={onParamData}
      />
    </>
  )
}

export default Chat
