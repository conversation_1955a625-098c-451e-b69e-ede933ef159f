import { DownOutlined, SettingOutlined } from '@ant-design/icons'
import { Dropdown, Empty, Input, Tag, Tooltip, Tree } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import { useNodes } from '@xyflow/react'
import { DATA_TYPES_MAP } from '@/utils/constant'

import './input.data.scss'
import { cloneDeep } from 'lodash'
import CustomEmpty from '@/components/CustomEmpty'

const UNDEFINED_TEXT = '未定义'

function InputData(props) {
  const nodes = useNodes()

  const { id, placeholder, options, value = {}, onChange, canRef = true } = props

  const inputRef = useRef(null)

  const [inputValue, setInputValue] = useState('')

  // 存储已经添加的 Tag 列表
  const [tags, setTags] = useState([])

  useEffect(() => {
    if (options) {
      if (value.ref === 'ref') {
        setRefValue(value.path)
      } else {
        setDefValue(value.path)
      }
    }
  }, [options, value?.ref, value?.path])

  const handleInputChange = (e) => {
    if (tags.length > 0) {
      return
    }

    hide()

    setInputValue(e.target.value)

    onChange({ path: e.target.value, ref: 'const' })
  }

  const handleInputBlur = () => {
    hide()
  }

  const hide = () => {
    // setOpen(false)
  }

  const isExisitInData = (value, data) => {
    for (const node of data) {
      if (node.key === value) return true
      if (node.children) {
        const found = isExisitInData(value, node.children)
        if (found) return true
      }
    }
    return false
  }

  const setRefValue = (value) => {
    if (value) {
      // 将当前输入的内容添加到 tags 列表中
      if (value.split('.').length > 0) {
        // 1、处理引用的节点是否仍然存在（被删除的情况） 2、判断引用的变量在原节点中是否仍然存在
        const refNodeId = value.split('.')[0]
        const refNode = nodes.find((n) => n.id === refNodeId)
        if (refNode) {
          // 判断引用的原来变量是否仍然存在
          const isExist = isExisitInData(value, options)

          if (isExist) {
            const nodeName = refNode?.data?.name
            const path = value.split('.').slice(1).join('.')
            const x = `${nodeName}.${path}`
            setTags([x])
          } else {
            setTags([UNDEFINED_TEXT])
          }
        } else {
          // 原来的节点不存在了
          setTags([UNDEFINED_TEXT])
        }
        // 用空格占位
        setInputValue(' ')
      }
    }
  }

  const setDefValue = (value) => {
    setTags([])
    setInputValue(value)
  }

  // 处理添加 Tag 的操作
  const handleAddTag = (value) => {
    setRefValue(value)
  }

  // 处理删除 Tag 的操作
  const handleDeleteTag = (index) => {
    setDefValue('')
    onChange({ path: '', ref: 'const' })
    inputRef.current?.focus()
  }

  const onTitleClick = (v) => {
    inputRef.current?.blur()
    handleAddTag(v)
    hide()
    onChange({ path: v, ref: 'ref' })
  }

  const titleRender = (nodeData) => {
    // console.log('titlerender----------', nodeData)
    const Component = nodeData?.type
      ? DATA_TYPES_MAP[nodeData?.type]?.component
      : DATA_TYPES_MAP['string']?.component

    return (
      <div className="flex items-center flex-nowrap" onClick={() => onTitleClick(nodeData?.key)}>
        {Component && <Component className="text-[var(--flow-desc-color)]" />}
        <span className="ml-1">{nodeData?.title}</span>
      </div>
    )
  }

  const handleCustomerOption = (opt) => {
    if (opt && opt.length) {
      let array = []
      //
      // console.log('handleCustomerOption is', opt)
      opt.forEach((item) => {
        let one = { label: item.title, key: item.key }

        if (item.children && item.children.length) {
          let child = {}
          if (
            item.children.length === 1 &&
            item.children[0].type !== 'object' &&
            item.children[0].type !== 'array<object>'
          ) {
            child.key = item.children[0].key
            child.label = titleRender(item.children[0])
          } else {
            let treeData = cloneDeep(item.children)
            child.label = (
              <Tree
                defaultExpandAll
                switcherIcon={<DownOutlined />}
                treeData={treeData}
                titleRender={titleRender}
                rootStyle={{ maxHeight: 400, overflowY: 'auto', overflowX: 'hidden' }}
              />
            )
          }
          // let treeData = cloneDeep(item.children)
          // child.label = (
          //   <Tree
          //     defaultExpandAll
          //     showLine
          //     switcherIcon={<DownOutlined />}
          //     treeData={treeData}
          //     titleRender={titleRender}
          //   />
          // )
          one.children = [child]
        }
        array.push(one)
      })
      return array
    }
    return []
  }

  const onDropdownClick = ({ key } = props) => {
    console.log('onDropdownClick', props)
  }

  const dropdownRenderEmpty = () => {
    return (
      <div className="bg-white border border-solid border-gray-300 py-2.5">
        <CustomEmpty description={'暂无可引用参数'} />
      </div>
    )
  }

  const dropdown = (options) => {
    const items = handleCustomerOption(options)
    console.log('*********************dropdown items*********************', items)
    return items.length > 0 ? (
      <Dropdown
        menu={{ items }}
        fieldNames={{ label: 'title', key: 'key', children: 'children' }}
        // trigger={['click']}
        placement={'bottomRight'}
        onClick={onDropdownClick}
      >
        <a onClick={(e) => e.preventDefault()}>
          <SettingOutlined className="text-[--flow-desc-color]" />
        </a>
      </Dropdown>
    ) : (
      <Dropdown dropdownRender={dropdownRenderEmpty} trigger={['click']} placement={'bottomRight'}>
        <a onClick={(e) => e.preventDefault()}>
          <SettingOutlined className="text-[--flow-desc-color]" />
        </a>
      </Dropdown>
    )
  }

  return (
    <div className={'x-input-data'}>
      <div className={'x-input-data-tag'}>
        {tags.map((tag, index) => (
          <Tooltip title={tag}>
            <Tag
              key={index}
              closeIcon
              style={{ marginLeft: '3px' }}
              onClose={handleDeleteTag}
              color={tag === UNDEFINED_TEXT ? 'warning' : 'default'}
              className="x-input-data-tag-inner"
            >
              {/* <span className="x-input-data-tag-inner-content">{tag}</span> */}
              <div className="x-input-data-tag-inner-content">{tag}</div>
            </Tag>
          </Tooltip>
        ))}
      </div>

      <Input
        ref={inputRef}
        value={inputValue}
        onChange={handleInputChange}
        onBlur={handleInputBlur}
        placeholder={placeholder}
        suffix={canRef ? dropdown(options) : null}
      />
    </div>
  )
}

export default InputData
