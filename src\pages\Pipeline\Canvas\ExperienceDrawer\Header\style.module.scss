.drawer-header {
  width: 100%;
  padding-bottom: 14px;
  .header-top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .top-left {
      display: flex;
      align-items: center;
      .icon {
        width: 26px;
        height: 26px;
      }
      .title {
        // width: 227px;
        height: 24px;
        font-size: 16px;
        font-weight: 600;
        color: var(--flow-title-color);
        line-height: 24px;
        margin-left: 5px;
        display: inline-block;
        white-space: nowrap;
        max-width: 280px;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 8px;
      }
      .title2.span {
        width: 100px;
        height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: var(--flow-title-color);
        line-height: 24px;
        margin-left: 5px;
      }
    }
    .top-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .button-left {
        display: flex;
        align-items: center;
        margin-right: 32px;
        gap: 12px;
      }
    }
  }
  .header-desc {
    width: 380px;
    // height: 18px;
    font-size: 12px;
    font-weight: 400;
    color: var(--flow-content-color);
    line-height: 18px;
    margin-top: 17px;
  }
  .icon-opt {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  .icon-frame {
    // margin-right: 12px;
  }
}

.divider-big {
  height: 1px;
  background: #eaecf0;
  margin: 0 -10px;
  margin-bottom: 4px;
}
