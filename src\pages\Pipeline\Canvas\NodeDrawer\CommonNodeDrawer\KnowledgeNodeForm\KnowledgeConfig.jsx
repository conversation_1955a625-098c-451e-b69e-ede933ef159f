import React, { useState } from 'react'
import { Space, Switch, Input, InputNumber, Empty } from 'antd'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { useKnowledgeRepoStore } from '@/store/knowledgeRepo'
import { useEffect } from 'react'
import CustomEmpty from '@/components/CustomEmpty'

function KnowledgeConfig(props) {
  const { id, value = [], onChange } = props

  const knowledgeRepos = useKnowledgeRepoStore((state) => state.knowledgeRepos)

  const mergedRepos = knowledgeRepos.map((item) => {
    return {
      ...item,
      threshold: String((value || []).find((it) => it.id === item.id)?.threshold ?? 0.4),
      used: (value || []).findIndex((it) => it.id === item.id) !== -1
    }
  })

  const [repos, setRepos] = useState([])

  useEffect(() => {
    setRepos(mergedRepos)
  }, [JSON.stringify(mergedRepos)])

  //   useEffect(() => {
  //     setRepos(mergedRepos)
  //   }, [mergedRepos])

  const onInputNumberChange = (item, val) => {
    console.log(item, val)

    // let newRepos = mergedRepos.map((it, i) => {
    //   return {
    //     ...item,
    //     threshold: item.id === it.id ? val : it.threshold
    //   }
    // })
    // setRepos((repos) => {
    //   return repos.map((it, i) => {
    //     return {
    //       ...it,
    //       threshold: item.id === it.id ? val : it.threshold
    //     }
    //   })
    // })
    // setTimeout(() => {
    //   const list = repos.filter((item) => item.used)
    //
    //   onChange?.(list)
    // })
    const newRepos = repos.map((it, i) => {
      return {
        ...it,
        threshold: item.id === it.id ? val : it.threshold
      }
    })

    setRepos(newRepos)
    onChange?.(newRepos.filter((item) => item.used))
  }

  const onSwitchChange = (item, val) => {
    console.log(item, val)

    // setRepos((repos) => {
    //   return repos.map((it, i) => {
    //     return {
    //       ...it,
    //       used: item.id === it.id ? val : it.used
    //     }
    //   })
    // })

    const newRepos = repos.map((it, i) => {
      return {
        ...it,
        used: item.id === it.id ? val : it.used
      }
    })

    setRepos(newRepos)
    onChange?.(newRepos.filter((item) => item.used))
    // setTimeout(() => {
    //   const list = repos.filter((item) => item.used)
    //
    //   onChange?.(list)
    // })
  }

  return (
    <div id={id}>
      {repos.length > 0 ? (
        repos.map((item, index) => {
          return (
            <Space key={item.id} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
              <Input disabled style={{ width: 180 }} value={item.name} />
              <InputNumber
                stringMode
                disabled={!item.used}
                min={0}
                max={0.99}
                step={0.1}
                value={item.threshold}
                onChange={(value) => onInputNumberChange(item, value)}
              />
              <Switch
                value={item.used}
                checkedChildren={
                  <span>
                    启用
                    <CheckOutlined />
                  </span>
                }
                unCheckedChildren={
                  <span>
                    启用
                    <CloseOutlined />
                  </span>
                }
                onChange={(value) => onSwitchChange(item, value)}
              />
            </Space>
          )
        })
      ) : (
        <CustomEmpty description={<span>未添加知识库</span>} />
      )}
    </div>
  )
}
export default KnowledgeConfig
