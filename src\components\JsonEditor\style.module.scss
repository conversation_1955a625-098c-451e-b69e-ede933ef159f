.editor {
  position: relative;
  border: 1px solid #dcdcdf;
  border-radius: 6px;
  transition: border 0.3s ease;
  &:focus-within {
    border: 1px solid var(--sparkos-primary-color);
  }
}

.editorHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  padding: 0 10px;
  width: 100%;
  border-bottom: 1px solid #eaecf0;
}

.editorBody {
  height: calc(100% - 30px);
}

:global {
  .ant-form-item-has-error .variable-ref-editor {
    border-color: var(--ant-color-error) !important;
    // box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
  }
}
