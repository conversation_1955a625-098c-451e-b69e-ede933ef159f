import IconDebug from 'assets/svgs/debug.svg?react'
import IconMore from 'assets/svgs/more.svg?react'
import IconClose from 'assets/svgs/close.svg?react'
import styles from './style.module.scss'
import { NODETYPES, CONCURRENT_DIMENSION } from '@/utils/constant'
import { nodesMap } from '@/utils/constant'
import { useReactFlow, useNodes, useNodesData } from '@xyflow/react'
import useStore from '@/store.js'
import { genNewNodeIdAndTitle, genNewAbilityId } from '@/utils/utils'
import { useState, useCallback, useRef } from 'react'
import { Dropdown, Input, message, Space } from 'antd'
import { runes } from 'runes2'
import { globalDelayUpdate } from '@/utils/delayUpdate.js'
import { useEffect } from 'react'
import { useUpdateEffect } from '@/hooks/useUpdateEffect'
import { isLLMRelatedNode } from '@/utils/isLLMRelatedNode'
import { NodeAdapterFactory } from '@/utils/adapter/NodeAdapterFactory'
import ajax from '@/utils/http'
import eventBus from '@/utils/eventBus'
import { useOfficialAbilityStore } from '@/store/officialAbility'
import { APP_ENV } from '@/utils/constant'
import getNodeIconComponent from '@/utils/getNodeIconComponent'

function Header(props) {
  const { node, onClose } = props
  // const Component = getNodeIconComponent(
  //   node.type,
  //   `${node?.data?.abilityOriginId || node?.data?.abilityCategory}`
  // )
  const Component = getNodeIconComponent(
    node.type,
    node.type === NODETYPES.FUNCTION
      ? `${node?.data.subType || node?.data.abilityOriginId || node?.data.abilityCategory}`
      : `${node?.data.abilityOriginId || node?.data.abilityCategory}`
  )
  const { type, id } = node
  const normal = type !== NODETYPES.ENTRY && type !== NODETYPES.EXIT
  const llmRelated =
    type !== NODETYPES.ENTRY &&
    type !== NODETYPES.EXIT &&
    type !== NODETYPES.CHOICE &&
    type !== NODETYPES.CONCURRENT &&
    type !== NODETYPES.PASS

  const isOfficialModel = llmRelated && !!node.data?.abilityOriginId

  const showDebug =
    type !== NODETYPES.ENTRY &&
    type !== NODETYPES.EXIT &&
    type !== NODETYPES.CHOICE &&
    type !== NODETYPES.CONCURRENT &&
    type !== NODETYPES.PASS &&
    type !== NODETYPES.FLOW &&
    type !== NODETYPES.VARIABLE_SET &&
    type !== NODETYPES.VARIABLE_GET

  const nodeData = useNodesData(id)
  const { debouncedSetDirty } = useStore()

  // console.log('%c node id now id ', 'background: #ff0000; color: #ffffff;', id);

  const { setNodes, deleteElements, updateNodeData } = useReactFlow()
  const [isEditX, setIsEditX] = useState(false)
  const [isEditA, setIsEditA] = useState(false)
  const [isEditB, setIsEditB] = useState(false)

  const [isEditDesc, setIsEditDesc] = useState(false)
  const [isEditAbilityCategory, setIsEditAbilityCategory] = useState(false)

  const { setIsDirty } = useStore()
  const nodes = useNodes()

  // const [abilities, setAbilities] = useState([])
  const abilities = useOfficialAbilityStore((state) => state.abilities)

  // let isEditA = false
  // let isEditB = false
  // let timerId = 0

  const onEditClick = () => {
    setIsEditX(true)
  }

  const dataUpdate = useRef({ name: nodeData?.data?.name, abilityId: nodeData?.data?.abilityId })
  // console.log('dataUpdate.current', dataUpdate.current)

  useEffect(() => {
    dataUpdate.current = {
      name: nodeData?.data?.name,
      abilityId: nodeData?.data?.abilityId
    }
  }, [nodeData])

  const isLLMNode = () => {
    return nodeData?.type === 'llm'
  }

  const onBlurA = (event) => {
    if (event.target.value !== '') {
      dataUpdate.current.name = event.target.value
    }
    setIsEditA(false)
    // onDelay()
  }

  const onBlurB = (event) => {
    if (event.target.value !== '') {
      dataUpdate.current.abilityId = event.target.value
    }
    setIsEditB(false)
    // onDelay()
  }

  const onFocusA = (event) => {
    setIsEditA(true)
  }

  const onFocusB = (event) => {
    setIsEditB(true)
  }

  useEffect(() => {
    setIsEditX(isEditA || isEditB)
  }, [isEditA, isEditB])

  useUpdateEffect(() => {
    if (!isEditX) {
      // 需要判断abilityId
      const regex = /^[a-z0-9_]+$/
      const nameReg = /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/
      if (!nameReg.test(dataUpdate.current.name)) {
        return message.warning('名称只支持中文/英文/数字/下划线格式')
      }
      if (!regex.test(dataUpdate.current.abilityId)) {
        return message.warning('能力id只能包含英文小写字母、下划线、数字，且不能为空')
      }
      if (
        !node.data?.abilityOriginId &&
        abilities.includes(`cbm_${dataUpdate.current.abilityId}`)
      ) {
        return message.warning('能力id不能与官方能力重名')
      }
      // 增加校验，工作流内唯一
      const index = nodes.findIndex(
        (nd) =>
          nd.id !== id && nd.data?.abilityId && nd.data?.abilityId === dataUpdate.current.abilityId
      )
      if (index !== -1) {
        return message.warning('能力id不能与该工作流内已有能力同名')
      }
      updateNodeData(id, dataUpdate.current)
      debouncedSetDirty()
    }
  }, [isEditX])

  // useEffect(() => {
  //   ajax({
  //     url: `/workflow/official/ability`,
  //     method: 'get'
  //   }).then((res) => {
  //     if (res.data.code === '0') {
  //       const abilities = res.data?.data?.ability || []
  //       setAbilities(abilities)
  //     }
  //   })
  // }, [])

  const onInputChang = (event) => {
    updateNodeData(id, { name: event.target.value })
  }

  const onBlurCategory = (event) => {
    const val = event.target.value
    if (!val || !val.trim()) {
      return message.warning('类型ID不能为空')
    }
    updateNodeData(id, { abilityCategory: val })
    setTimeout(() => {
      setIsDirty(true)
    })
    setIsEditAbilityCategory(false)
  }

  const onBlurDesc = (event) => {
    const val = event.target.value
    if (!val || !val.trim()) {
      return message.warning('描述不能为空')
    }
    updateNodeData(id, { description: val })
    setTimeout(() => {
      setIsDirty(true)
    })
    setIsEditDesc(false)
  }

  const onDescClick = (e) => {
    setIsEditDesc(true)
  }

  const onAbilityCategoryClick = () => {
    setIsEditAbilityCategory(true)
  }

  const onNameDbClick = (e) => {
    e.stopPropagation()
    onEditClick()
  }

  const onDeleteClick = () => {
    deleteElements({ nodes: nodes.filter((nd) => nd.id === id) })
  }

  const copyNode = (sourceNode, option = {}) => {
    const { id, name } = genNewNodeIdAndTitle(nodes, sourceNode.type, sourceNode.data)

    let targetNode = {
      ...sourceNode,
      id,
      position: {
        x: sourceNode.position.x + 50,
        y: sourceNode.position.y + 50
      },
      selected: false,
      data: {
        ...(sourceNode.data || {}),
        name
      }
    }
    if (sourceNode.type === NODETYPES.CONCURRENT) {
      targetNode = {
        ...targetNode,
        style: {
          width: CONCURRENT_DIMENSION.width,
          height: CONCURRENT_DIMENSION.height
        }
      }
    }
    // 大模型相关节点初始化abilityId
    if (
      isLLMRelatedNode(sourceNode.type) ||
      sourceNode.type === NODETYPES.FLOW ||
      sourceNode.type === NODETYPES.KNOWLEDGE ||
      sourceNode.type === NODETYPES.CODE ||
      sourceNode.type === NODETYPES.API ||
      sourceNode.type === NODETYPES.FUNCTION ||
      sourceNode.type === NODETYPES.VARIABLE_SET ||
      sourceNode.type === NODETYPES.VARIABLE_GET
    ) {
      targetNode = {
        ...targetNode,
        data: {
          ...targetNode.data,
          abilityId: genNewAbilityId(
            nodes,
            sourceNode.type,
            sourceNode.data,
            sourceNode.type === NODETYPES.KNOWLEDGE
          ),
          abilityCategory: genNewAbilityId(
            nodes,
            sourceNode.type,
            sourceNode.data,
            sourceNode.type === NODETYPES.KNOWLEDGE
          )
        }
      }
    }
    setNodes((nds) => nds.concat(targetNode))
  }

  const onCopyClick = () => {
    const sourceNode = nodes.find((nd) => nd.id === id)
    copyNode(sourceNode)
    setTimeout(() => {
      setIsDirty(true)
    })
  }

  const onPublishClick = async () => {
    // 发布为模板
    let obj = {}
    // 通过适配器转换数据
    const adapter = NodeAdapterFactory.getAdapter(type)
    if (adapter) {
      obj = adapter.fromFrontToBack(nodeData)
    }
    const param = { ...obj, nodeType: type }

    const result = await ajax({
      url: '/workflow/node/template/save',
      method: 'post',
      data: JSON.stringify(param)
    })
    if (result.data.code === '0') {
      message.success('保存为模板成功')
      eventBus.emit('REFRESH_TEMPLATE')
    }
  }

  const onDebugClick = () => {
    eventBus.emit('DEBUG_NODE')
  }

  const items = [
    {
      key: 'rename',
      label: <span className="nodrag">重命名</span>
    },
    {
      key: 'duplicate',
      label: <span className="nodrag">{APP_ENV === 'auto' ? '复制' : '创建副本'}</span>
    },
    !node.data.abilityOriginId && isLLMRelatedNode(type)
      ? {
          key: 'publish',
          label: <span className="nodrag">保存模板</span>
        }
      : null,
    {
      key: 'delete',
      label: <span className="nodrag">删除</span>
    }
  ].filter(Boolean)

  const onClick = (e) => {
    const key = e.key
    e.domEvent.stopPropagation()
    switch (key) {
      case 'rename':
        onEditClick()
        break
      case 'duplicate':
        onCopyClick()
        break
      case 'publish':
        onPublishClick()
        break
      case 'delete':
        onDeleteClick()
        break
    }
  }

  const displayTitle = llmRelated
    ? `${nodeData?.data?.name}<cbm_${nodeData?.data?.abilityId}>`
    : `${nodeData?.data?.name}`

  const showEditCategory = !!nodeData?.data?.abilityId && !nodeData?.data?.abilityOriginId

  return (
    <>
      <div className={styles['drawer-header']}>
        <div className={styles['header-top']}>
          <div className={styles['top-left']}>
            <div className={styles['icon']}>
              <Component />
            </div>
            {isEditX ? (
              <>
                <Space direction={'vertical'}>
                  <Space.Compact>
                    <Input
                      size="small"
                      defaultValue={nodeData?.data?.name}
                      placeholder={llmRelated ? '能力名称' : '节点名称'}
                      autoFocus
                      onBlur={onBlurA}
                      onFocus={onFocusA}
                      count={{
                        show: true,
                        max: 10,
                        strategy: (txt) => runes(txt).length,
                        exceedFormatter: (txt, { max }) => runes(txt).slice(0, max).join('')
                      }}
                      className={styles.title}
                    />
                    {llmRelated && (
                      <Input
                        size="small"
                        defaultValue={nodeData?.data?.abilityId}
                        placeholder={'能力ID'}
                        onBlur={onBlurB}
                        onFocus={onFocusB}
                        addonBefore={'cbm_'}
                        count={{
                          show: true,
                          max: 50,
                          strategy: (txt) => runes(txt).length,
                          exceedFormatter: (txt, { max }) => runes(txt).slice(0, max).join('')
                        }}
                        className={styles.title2}
                      />
                    )}
                  </Space.Compact>
                </Space>
              </>
            ) : (
              <span onDoubleClick={onNameDbClick} title={displayTitle}>
                {displayTitle}
              </span>
            )}
          </div>
          {!isEditX && (
            <div className={styles['top-right']}>
              <div className={styles['button-left']}>
                {showDebug && (
                  <div
                    className={` w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer `}
                    onClick={onDebugClick}
                  >
                    <IconDebug />
                  </div>
                )}

                {normal && (
                  <Dropdown
                    menu={{
                      items,
                      onClick
                    }}
                    placement="bottom"
                    className="nodrag w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer "
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div>
                      <IconMore />
                    </div>
                  </Dropdown>
                )}
              </div>
              <div className={styles['button-close']} onClick={onClose}>
                <div className="w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer">
                  <IconClose />
                </div>
              </div>
            </div>
          )}
        </div>

        {showEditCategory && (
          <div>
            {isEditAbilityCategory ? (
              <Input
                size="small"
                defaultValue={nodeData?.data?.abilityCategory}
                placeholder={'类型ID'}
                onBlur={onBlurCategory}
                addonBefore={'cbm_'}
                count={{
                  show: true,
                  max: 50,
                  strategy: (txt) => runes(txt).length,
                  exceedFormatter: (txt, { max }) => runes(txt).slice(0, max).join('')
                }}
                className={styles.category}
              />
            ) : (
              <div
                className={`${styles['header-desc']} cursor-pointer`}
                onClick={onAbilityCategoryClick}
              >
                <span>类型ID：</span> {`cbm_${nodeData.data?.abilityCategory}`}
              </div>
            )}
          </div>
        )}

        {isEditDesc ? (
          <Input.TextArea
            size="small"
            defaultValue={nodeData?.data?.description}
            autoFocus
            onBlur={onBlurDesc}
            maxLength={100}
            showCount
            className={`${styles['header-desc']} mb-3`}
          />
        ) : (
          <div className={`${styles['header-desc']} cursor-pointer`} onClick={onDescClick}>
            {nodeData.data?.description}
          </div>
        )}
      </div>
      <div className={styles['divider-big']}></div>
    </>
  )
}
export default Header
