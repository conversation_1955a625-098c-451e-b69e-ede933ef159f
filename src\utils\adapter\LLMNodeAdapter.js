import { useModelStore } from '@/store/model'
import { NODETYPES, COG_DOMAINS } from '@/utils/constant'
import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'

import { MarkerType, Position, getOutgoers } from '@xyflow/react'

// 1、后端给定的 大模型 节点模板
const llmTemplate = {
  ability: {
    domain: 'cogs',
    name: '',
    id: '',
    originId: '',
    type: 'LLM',
    prompt: '',
    systemPrompt: '',
    memoryRound: -1,
  },
  audit: true,
  description: '大模型',
  flow_in: true,
  flow_out: true,
  input: {},
  nodeType: 'llm',
  output: {
    nlp: {}
  },
  title: '大模型',
  type: 'task',
  dist: true
}

/**
 * 将大模型后端模板转成前端data
 * @param {*} template
 */
const convertLLMTemplateFromBack2Front = function (template = llmTemplate) {
  // validated 前端字段，在这里过滤
  let { ability, input, nodeType, output, title, type, position, validated, ...rest } = template
  // TODO: output 没有数据，先取默认的数据显示
  const keys = Object.keys(output || {})
  if (keys.length === 1) {
    const k = keys[0]
    if (!(output || {})[k]?.desc) {
      // 没有值，搞个默认值
      output = { text: { desc: { format: 'plain', schema: { type: undefined } } } }
    }
  }

  let param = {
    ...rest,

    input: handleInputFromBack2Canvas(input),
    type: nodeType,
    output: handleOutputFromBack2Canvas(output),
    name: title
    // 确认 type  task? 前端不需要这个task，转换回来的时候写死
  }
  if (ability) {
    let aid = ability.id
    if (aid) {
      if (aid.startsWith('cbm_')) {
        aid = aid.substring(4)
      }
    }

    let acategory = ability.category
    if (acategory) {
      if (acategory.startsWith('cbm_')) {
        acategory = acategory.substring(4)
      }
    } else {
      acategory = aid
    }

    // 生成一个前端的model uuid 用于 select组件的value

    let modelUUID
    if (nodeType === NODETYPES.KNOWLEDGE) {
      modelUUID = ability.domain
    } else {
      if (COG_DOMAINS.includes(ability.domain)) {
        modelUUID = `${ability.domain}_${ability.domain}_lite`
      } else {
        if (ability.domain || ability.patchId || ability.modelType) {
          modelUUID = `${ability.domain ?? ''}_${ability.patchId ?? ''}_${ability.modelType ?? ''}`
        }
      }
    }

    param = {
      ...param,
      abilityId: aid,
      abilityCategory: acategory,
      abilityName: ability.name,
      abilityType: ability.type,
      abilityOriginId: ability.originId,
      // 后端到前端，将前端domain字段存放 `${ability.domain ?? ''}_${ability.patchId ?? ''}_${ability.modelType ?? ''}`
      // domain: ability.domain,
      memoryRound: ability.memoryRound,
      domain: modelUUID,
      prompt: ability.prompt,
      systemPrompt: ability.systemPrompt
    }
  }
  return param
}

/**
 * 将前端data转成后端协议结构
 * @param {*} data
 */
const convertLLMTemplateFromFront2Back = function (data = {}, nodes) {
  const {
    abilityId,
    abilityCategory,
    abilityName,
    abilityType,
    abilityOriginId,
    memoryRound,
    domain,
    prompt,
    systemPrompt,
    input,
    output,
    name,
    // 前端字段，在这里去除
    validated,
    debug,
    ...rest
  } = data

  const handleInput = handleInputFromCanvas2Back(input, nodes)
  const handleOutput = handleOutputFromCanvas2Back(output)

  const { model } = useModelStore.getState()

  let newData = {
    ...rest,
    title: name,
    type: 'task',
    output: handleOutput,
    input: handleInput,
    ability: {
      id: abilityId ? `cbm_${abilityId}` : '',
      category: abilityCategory ? `cbm_${abilityCategory}` : abilityId ? `cbm_${abilityId}` : '',
      originId: abilityOriginId,
      // ability.name 接收页面 的title
      name,
      type: abilityType || 'LLM',
      prompt,
      systemPrompt,
      memoryRound : Number(memoryRound) > 0 ? memoryRound : -1
    }
  }
  if (data?.type === NODETYPES.KNOWLEDGE) {
    newData.ability.domain = domain
  } else {
    // 前端的 domain ,实际上是一个uuid，需要转换
    const m = model.find((it) => {
      // const uuid = it => it.value === domain
      const uuid = `${it.domain ?? ''}_${it.patchId ?? ''}_${it.modelType ?? ''}`
      return uuid === domain
    })
    if (m?.domain) {
      newData.ability.domain = m?.domain
    }

    if (m?.url) {
      newData.ability.url = m.url
    }
    if (m?.patchId) {
      newData.ability.patchId = m.patchId
    }
    if (m?.modelType) {
      newData.ability.modelType = m.modelType
    }
  }

  return newData
}
export class LLMNodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    param = {
      ...param,
      data: convertLLMTemplateFromBack2Front(nd)
    }
    return param
  }

  static fromFrontToBack(nd, nodes) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    const newData = convertLLMTemplateFromFront2Back(data, nodes)
    param = {
      ...newData,
      ...param
    }
    return param
  }
}
