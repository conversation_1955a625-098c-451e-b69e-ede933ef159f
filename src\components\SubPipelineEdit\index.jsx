import { EditOutlined } from '@ant-design/icons'
import { Input, InputNumber, Select } from 'antd'
import { useRef, useState } from 'react'
import { useReactFlow } from '@xyflow/react'
import useStore from '@/store.js'

function SubPipelineEdit({ id, type, k, value }) {
  const { setNodes, setEdges, updateNodeData, updateEdgeData } = useReactFlow()
  const [isEdit, setIsEdit] = useState(false)
  const { setIsDirty } = useStore()
  const inputRef = useRef(null)

  const onEditClick = () => {
    setIsEdit(true)
  }

  const onSubmit = () => {
    setIsEdit(false)
    setIsDirty(true)
  }

  const onChange = (val) => {
    updateNodeData(id, { workflowId: val })
    setTimeout(() => {
      setIsDirty(true)
    }, 0)
  }
  return (
    <>
      <div className="flex pl-1 items-center" ref={inputRef}>
        <span className="mr-2" style={{ color: 'rgba(0, 0, 0, 0.88)', fontSize: 12 }}>
          pipeline:
        </span>

        <Select
          size="small"
          className="nodrag"
          style={{
            width: 180
          }}
          value={value}
          getPopupContainer={() => inputRef.current}
          onChange={onChange}
          options={[
            {
              value: '1',
              label: 'pipeline 1'
            },
            {
              value: '2',
              label: 'pipeline 2'
            }
          ]}
        />
      </div>
    </>
  )
}
export default SubPipelineEdit
