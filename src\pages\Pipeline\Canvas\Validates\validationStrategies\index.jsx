import { NODETYPES } from '@/utils/constant'
import { LLMNodeStrategy } from './LLMNodeStrategy'
import { LLMKnowledgeNodeStrategy } from './LLMKnowledgeNodeStrategy'
import { APINodeStrategy } from './APINodeStrategy'
import { CodeNodeStrategy } from './CodeNodeStrategy'
import { EntryNodeStrategy } from './EntryNodeStrategy'
import { PassNodeStrategy } from './PassNodeStrategy'
import { FlowNodeStrategy } from './FlowNodeStrategy'
import { ChoiceNodeStrategy } from './ChoiceNodeStrategy'
import { ExitNodeStrategy } from './ExitNodeStrategy'
import { PKNodeStrategy } from './PKNodeStrategy'
import { FunctionNodeStrategy } from './FunctionNodeStrategy'
import { SemanticNodeStrategy } from './SemanticNodeStrategy'

import { VariableSetNodeStrategy } from './VariableSetNodeStrategy'
import { VariableGetNodeStrategy } from './VariableGetNodeStrategy'

// 校验策略
export const validationStrategies = {
  [NODETYPES.LLM]: LLMNodeStrategy,
  [NODETYPES.DENIAL]: LLMNodeStrategy,

  [NODETYPES.ARC]: LLMNodeStrategy,
  [NODETYPES.INTENT_DOMAIN]: LLMNodeStrategy,

  [NODETYPES.KNOWLEDGE]: LLMKnowledgeNodeStrategy,
  [NODETYPES.API]: APINodeStrategy,
  [NODETYPES.CODE]: CodeNodeStrategy,
  [NODETYPES.PK]: PKNodeStrategy,
  [NODETYPES.FUNCTION]: FunctionNodeStrategy,
  [NODETYPES.SEMANTIC]: SemanticNodeStrategy,

  [NODETYPES.ENTRY]: EntryNodeStrategy,
  [NODETYPES.PASS]: PassNodeStrategy,
  [NODETYPES.FLOW]: FlowNodeStrategy,
  [NODETYPES.CHOICE]: ChoiceNodeStrategy,
  [NODETYPES.EXIT]: ExitNodeStrategy,

  [NODETYPES.VARIABLE_SET]: VariableSetNodeStrategy,
  [NODETYPES.VARIABLE_GET]: VariableGetNodeStrategy
}
