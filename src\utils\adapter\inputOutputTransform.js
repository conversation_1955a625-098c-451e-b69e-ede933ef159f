import { getRefVariable } from '@/hooks/useRefVariable'
/**
 * input 数据格式 前端---》后端
 * @param {*} input
 * @returns
 */
export const handleInputFromCanvas2Back = (input = [], nodes) => {
  let handleInput = {}
  ;(input || []).filter(Boolean).forEach((item) => {
    const key = item.key
    const value = item.value || {}
    const keyProp = item.keyProp || {}

    const tree = getRefVariable(key, value, nodes)
    let desc = null
    if (tree?.[0]) {
      desc = handleSchemaFromCanvas2Back(tree[0], { name: 'title', schema: 'children' })
    }
    // console.log('引用变量的schema desc', desc)
    let obj = {}
    if (value.ref === 'ref') {
      if (value.path && value.path.split('.').length > 0) {
        obj = {
          ref: value.path.split('.')[0],
          path: value.path.split('.').slice(1).join('.')
        }
      }
    } else if (value.ref === 'const') {
      obj = {
        ref: '',
        path: value.path
      }
    }
    if (desc) {
      obj = {
        ...obj,
        ...desc
      }
    }

    // if(typeof keyProp.required !== 'undefined') {
    //   obj.required = keyProp.required
    // }
    if (keyProp.required === false) {
      obj.required = keyProp.required
    }

    if (typeof keyProp.type !== 'undefined') {
      obj.type = keyProp.type
    }

    if (typeof keyProp.location !== 'undefined') {
      obj.location = keyProp.location
    }

    handleInput[item.key] = obj
  })
  return handleInput
}

const extractStringFromType = (type) => {
  // 使用正则表达式匹配 '<' 和 '>' 之间的内容
  let match = type.match(/<(.*?)>/)
  if (match && match[1]) {
    return match[1]
  }
  return null
}

const handleSchemaFromCanvas2Back = (
  schema = {},
  fieldNames = { name: 'name', schema: 'schema' }
) => {
  const { type } = schema

  const generateDesc = (schema, innerType) => {
    const { type } = schema
    if (type === 'integer' || type === 'boolean' || type === 'number') {
      return {
        format: type,
        schema
      }
    } else if (type === 'string') {
      return {
        format: 'plain',
        schema
      }
    } else if (type === 'object') {
      // console.log('come in generateDesc', schema)
      return {
        format: 'json',
        schema
      }
    } else if (type === 'array') {
      if (innerType === 'string') {
        return {
          format: 'array<plain>',
          schema
        }
      } else if (innerType === 'integer' || innerType === 'boolean' || innerType === 'number') {
        return {
          format: `array<${innerType}>`,
          schema
        }
      } else if (innerType === 'object') {
        return {
          format: 'array<json>',
          schema
        }
      }
    } else {
      return {}
    }
  }

  if (type === 'string' || type === 'integer' || type === 'boolean' || type === 'number') {
    return {
      desc: generateDesc({ type })
    }
  } else if (
    type === 'array<string>' ||
    type === 'array<integer>' ||
    type === 'array<boolean>' ||
    type === 'array<number>'
  ) {
    const innerType = extractStringFromType(type)
    return {
      desc: generateDesc(
        {
          type: 'array',
          items: { type: innerType }
        },
        innerType
      )
    }
  } else if (type === 'object') {
    // console.log('come in object schema', schema)
    return {
      desc: generateDesc({
        type: 'object',
        properties: (schema[fieldNames.schema] || []).reduce((acc, s) => {
          if (s && s[fieldNames.name]) {
            acc[s[fieldNames.name]] = handleSchemaFromCanvas2Back(s, fieldNames)?.desc?.schema
          }
          return acc
        }, {})
      })
    }
  } else if (type === 'array<object>') {
    return {
      desc: generateDesc(
        {
          type: 'array',
          items: {
            type: 'object',
            properties: (schema[fieldNames.schema] || []).reduce((acc, s) => {
              if (s && s[fieldNames.name]) {
                acc[s[fieldNames.name]] = handleSchemaFromCanvas2Back(s, fieldNames)?.desc?.schema
              }
              return acc
            }, {})
          }
        },
        'object'
      )
    }
  }
}

export const handleOutputFromCanvas2Back = (output = []) => {
  return output.reduce((acc, o) => {
    if (o && o.name) {
      acc[o.name] = handleSchemaFromCanvas2Back(o)
    }
    return acc
  }, {})
}

const handleSchemaFromBack2Canvas = (schema = {}, name) => {
  const { type, properties, items } = schema

  if (type === 'string' || type === 'integer' || type === 'boolean' || type === 'number') {
    return { name, type }
  } else if (type === 'array') {
    // 数组类型，根据 items 中的类型返回相应的前端结构
    const innerType = schema.items?.type || 'string'
    if (innerType === 'object') {
      return {
        name,
        type: `array<object>`,
        schema: Object.keys(schema.items.properties).map((key) =>
          handleSchemaFromBack2Canvas(schema.items.properties[key], key)
        )
      }
    } else {
      return {
        name,
        type: `array<${innerType}>`
      }
    }
  } else if (type === 'object') {
    // 对象类型，递归处理 properties 中的字段
    return {
      name,
      type: 'object',
      schema: Object.keys(properties).map((key) => {
        const prop = properties[key]
        return { name: key, ...handleSchemaFromBack2Canvas(prop, key) }
      })
    }
  }
  return {} // 如果没有匹配的类型，返回空对象
}

export const handleOutputFromBack2Canvas = (output = {}) => {
  const result = Object.keys(output)
    .map((key) => {
      const desc = output[key]?.desc
      if (desc && desc.schema) {
        return {
          name: key,
          ...handleSchemaFromBack2Canvas(desc.schema, key)
        }
      }
      return null
    })
    .filter(Boolean) // 过滤掉空值，返回数组
  // console.log('----------result', result)
  return result
}

export const handleInputFromBack2Canvas = (input = {}) => {
  // 将 input: {text: {ref:"sos::entry", path: 'hello'}}
  // 转成 input: [{key: 'text', value: {ref: 'ref', value: 'sos::entry.hello'}]
  // 将 input: {text: {ref:"", path: 'hello'}}
  // 转成 input: [{key: 'text', value: {ref: 'const', path: 'hello'}}]
  let handleInput = []
  Object.keys(input).forEach((k) => {
    if (input[k]) {
      let obj
      if (input[k].ref) {
        obj = {
          key: k,
          value: {
            ref: 'ref',
            path: `${input[k].ref}.${input[k].path}`
          },
          keyProp: { name: k }
        }
      } else {
        obj = {
          key: k,
          value: {
            ref: 'const',
            path: input[k].path
          },
          keyProp: { name: k }
        }
      }
      // if (typeof input[k].required !== 'undefined') {
      //   obj.keyProp.required = input[k].required
      // } else {
      //   // 默认为true
      //   obj.keyProp.required = true
      // }
      if (input[k].required === false) {
        obj.keyProp.required = false
      }

      if (typeof input[k].type !== 'undefined') {
        obj.keyProp.type = input[k].type
      }

      if (typeof input[k].location !== 'undefined') {
        obj.keyProp.location = input[k].location
      }

      handleInput.push(obj)
    }
  })
  return handleInput
}
