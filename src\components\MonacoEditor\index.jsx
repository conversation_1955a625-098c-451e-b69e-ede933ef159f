import Editor, { loader } from '@monaco-editor/react'
// import * as monaco from 'monaco-editor';
// import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker';
// import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker';
// import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker';
// import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker';
// import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker';

// self.MonacoEnvironment = {
//   getWorker(_, label) {
//     if (label === 'json') {
//       return new jsonWorker();
//     }
//     if (label === 'css' || label === 'scss' || label === 'less') {
//       return new cssWorker();
//     }
//     if (label === 'html' || label === 'handlebars' || label === 'razor') {
//       return new htmlWorker();
//     }
//     if (label === 'typescript' || label === 'javascript') {
//       return new tsWorker();
//     }
//     return new editorWorker();
//   },
// };

// // you can change the source of the monaco files
loader.config({
  paths: {
    vs: import.meta.env.PROD
      ? `${import.meta.env.VITE_ROUTER_BASE_URL}/flow/assets/monaco-editor/vs`
      : 'node_modules/monaco-editor/min/vs'
  }
})

// loader.init()

function MonacoEditor({ value, onChange, height, readOnly, readOnlyMessage }) {
  const handleEditorChange = (val, event) => {
    console.log('here is the current model value:', val)
    onChange?.(val)
  }
  return (
    <Editor
      height={height ?? '260px'}
      language="python"
      value={value}
      onChange={handleEditorChange}
      theme="vs-dark"
      options={{
        readOnly: !!readOnly,
        readOnlyMessage,
        automaticLayout: true, // 自动调整布局
        minimap: { enabled: false } // 禁用 minimap
      }}
    />
  )
}
export default MonacoEditor
