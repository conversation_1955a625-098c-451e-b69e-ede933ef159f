import { useNodes } from '@xyflow/react'
import { useEffect, useState } from 'react'
import { NODETYPES } from '@/utils/constant'
import generateTreeData from './generateTreeData'

const useInput = (inputArr) => {
  const [input, setInput] = useState([])
  const nodes = useNodes()

  const updateInput = () => {
    const newRefList = nodes
      .filter((n) => n.type !== NODETYPES.CHOICE) // 排除 CHOICE 类型节点
      .map((n) => {
        const inputOutput = n.type === NODETYPES.ENTRY ? n.data.input : n.data.output

        return {
          key: n.id,
          name: n.data?.name,
          children: inputOutput
        }
      })

    const treeData = (newRefList || []).map((data) => {
      return {
        title: data.name,
        key: data.key,
        selectable: false, // 顶层节点不可选
        children: generateTreeData(data.key, data.children) // 用 schema 转换为树形数据
      }
    })

    const getTypeByValue = (value, data) => {
      if (!data) {
        return ''
      }
      for (const node of data) {
        if (node.key === value) return node.type
        if (node.children) {
          const found = getTypeByValue(value, node.children)
          if (found) return found
        }
      }
      return ''
    }

    const newInput = (inputArr || []).filter(Boolean).map((it) => {
      return {
        ...it,
        type: getTypeByValue(it.value, treeData) || 'string'
      }
    })
    setInput(newInput)
  }

  useEffect(() => {
    updateInput()
  }, [inputArr])

  return input
}
export default useInput
