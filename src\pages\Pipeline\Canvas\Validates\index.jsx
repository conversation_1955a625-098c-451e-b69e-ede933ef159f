import { nodesMap, NODETYPES } from '@/utils/constant'
import styles from './style.module.scss'
import { useState, useEffect } from 'react'
import eventBus from '@/utils/eventBus'
import { useNodes, useEdges, useReactFlow, getOutgoers, Panel } from '@xyflow/react'

import { validationStrategies } from './validationStrategies'
import { message } from 'antd'
import getNodeIconComponent from '@/utils/getNodeIconComponent'
// import ValidateWorker from '@/workers/validate.worker.js?worker&module'

export default function Validates({ onNodeClick, visible }) {
  const [showValidate, setShowValidate] = useState(false)
  const { updateNodeData, getNodes, getEdges } = useReactFlow()
  const [issues, setIssues] = useState([])

  // const nodes = useNodes()
  // const edges = useEdges()

  useEffect(() => {
    // 订阅事件
    eventBus.on('DEBUG', handler)
    // 清理订阅
    return () => {
      eventBus.off('DEBUG', handler)
    }
  }, [])

  // 递归获取所有后继节点
  const getAllSuccessors = (node, nodes, edges, visited = new Set()) => {
    visited.add(node.id)

    // 获取直接后继节点
    const outgoers = getOutgoers(node, nodes, edges)

    let allSuccessors = [...outgoers]

    outgoers.forEach((outgoer) => {
      // 递归获取后继节点的后继节点
      allSuccessors = [...allSuccessors, ...getAllSuccessors(outgoer, nodes, edges, visited)]
    })

    // 去重并过滤有效节点
    let finalAllSuccessors = Array.from(new Set(allSuccessors))
    const hasExitNode = finalAllSuccessors.find((nd) => nd.type === NODETYPES.EXIT)
    if (!hasExitNode) {
      const exitNode = nodes.find((nd) => nd.type === NODETYPES.EXIT)
      finalAllSuccessors = [...finalAllSuccessors, exitNode]
    }
    return [node, ...finalAllSuccessors]
  }

  // const validateAllNodes = async () => {
  //   // 仿照coze，获取从开始节点开始的所有后置节点，如果结束节点不在连线上，为其单独加上
  //   const nodes = getNodes()
  //   const edges = getEdges()
  //   const entryNode = nodes.find((nd) => nd.type === NODETYPES.ENTRY)
  //   const nodesToBeValidated = getAllSuccessors(entryNode, nodes, edges)
  //   console.log('---------nodesToBeValidated---------', nodesToBeValidated)
  //   let issues = []

  //   for (const nd of nodesToBeValidated) {
  //     let totalErrors = []
  //     // 给节点设置 validated 标识
  //     updateNodeData(nd.id, { validated: true })
  //     // 为每一个要校验的节点进行校验

  //     const validateStrategy = validationStrategies[nd.type]
  //     if (validateStrategy) {
  //       const errors = await validateStrategy(nd, nodes, edges)
  //       totalErrors.push(...errors)
  //     }

  //     if (totalErrors.length > 0) {
  //       issues.push({
  //         node: nd,
  //         errors: totalErrors
  //       })
  //     }
  //   }

  //   return issues
  // }

  const validateAllNodesInWorker = () => {
    return new Promise((resolve) => {
      // const worker = new ValidateWorker()
      const worker = new Worker(
        new URL('@/workers/validate.worker.js', import.meta.url),
        { type: 'module' }
      )
      const nodes = getNodes()
      const edges = getEdges()
  
      worker.postMessage({ nodes, edges })
  
      worker.onmessage = (e) => {
        const { issues } = e.data
        // 更新节点 validated 标识（仍然主线程中处理）
        issues.forEach(issue => {
          updateNodeData(issue.node.id, { validated: true })
        })
        resolve(issues)
        worker.terminate()
      }
  
      // 可选：处理worker报错
      worker.onerror = (err) => {
        console.error('Worker error', err)
        resolve([])
      }
    })
  }

  const handler = async (callback) => {
    const issues = await validateAllNodesInWorker()
    if (issues.length > 0) {
      setShowValidate(true)
      setIssues(issues)
    } else {
      setShowValidate(false)
      // message.success('校验通过，发布前可先进行链路调试')
    }
    callback && callback(issues.length)
  }

  const onCloseClick = () => {
    setShowValidate(false)
  }

  return showValidate ? (
    <Panel
      className={`${styles.pannel} ${visible ? styles['pannel-adapt'] : styles['pannel-origin']}`}
    >
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles['header-left']}>错误列表&nbsp;({issues.length})</div>
          <div className={styles['header-right']} onClick={onCloseClick}>
            ×
          </div>
        </div>
        <ul className={styles.content}>
          {issues.map((iss) => {
            // const Component = nodesMap[iss.node.type].component
            const Component = getNodeIconComponent(
              iss.node.type,
              `${iss.node.data?.abilityOriginId || iss.node.data?.abilityCategory}`
            )
            const error = iss.errors.map((it) => it.message).join('；')
            return (
              <li
                className={styles.node}
                key={iss.node.id}
                onClick={() => onNodeClick && onNodeClick(iss.node)}
              >
                <Component className="w-[26px] h-[26px] min-w-[26px]" />
                <div className={styles['node-info']}>
                  <div className={styles['node-info-title']}>{iss.node.data?.name}</div>
                  <div className={styles['node-info-validates']} title={error}>
                    {error}
                  </div>
                </div>
              </li>
            )
          })}
        </ul>
      </div>
    </Panel>
  ) : null
}
