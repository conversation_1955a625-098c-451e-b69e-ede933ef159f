import Subflow from 'assets/svg/nd-flow.svg?react'
import Parallel from 'assets/svg/nd-parallel.svg?react'

// 大模型节点
import Model from 'assets/svgs/model.svg?react'
import WorkFlow from 'assets/svgs/workflow.svg?react'

import Logic from 'assets/svgs/logic.svg?react'
import Tool from 'assets/svgs/tool.svg?react'
import Agent from 'assets/svgs/agent.svg?react'

import Entry from 'assets/svgs/entry.svg?react'
import Exit from 'assets/svgs/exit.svg?react'

import Choice from 'assets/svgs/choice.svg?react'
import Concurrent from 'assets/svgs/concurrent.svg?react'
import Api from 'assets/svgs/api.svg?react'
import Semantic from 'assets/svgs/semantic.svg?react'
import Knowledge from 'assets/svgs/knowledge.svg?react'
import Denial from 'assets/svgs/denial.svg?react'
import PK from 'assets/svgs/pk.svg?react'
import Function from 'assets/svgs/model.svg?react'

import Arc from 'assets/svgs/model.svg?react'
import IntentDomain from 'assets/svgs/model.svg?react'

import Code from 'assets/svgs/code.svg?react'

import IconString from 'assets/svgs/string.svg?react'
import IconBoolean from 'assets/svgs/boolean.svg?react'
import IconNumber from 'assets/svgs/number.svg?react'
import IconObject from 'assets/svgs/object.svg?react'

import IconArrayString from 'assets/svgs/array-string.svg?react'
import IconArrayBoolean from 'assets/svgs/array-boolean.svg?react'
import IconArrayNumber from 'assets/svgs/array-number.svg?react'
import IconArrayObject from 'assets/svgs/array-object.svg?react'

export const ENTRY_SOURCE_ID = 'sos::entry'
export const EXIT_SOURCE_ID = 'sos::exit'

export const NODETYPES = {
  // 开始节点
  ENTRY: 'entry',
  // 结束节点
  EXIT: 'exit',
  LOGIC: 'logic',
  LLM: 'llm',
  SEMANTIC: 'semantic',
  KNOWLEDGE: 'knowledge',
  DENIAL: 'denial',
  ARC: 'arc',
  INTENT_DOMAIN: 'intent_domain',
  PK: 'pk',
  FUNCTION: 'function',
  API: 'plugin',

  FLOW: 'flow',
  CHOICE: 'choice',
  CONCURRENT: 'concurrent',
  PASS: 'pass',
  TOOL: 'plugin',
  AGENT: 'agent',

  CODE: 'code',

  VARIABLE_SET: 'variablesSet',
  VARIABLE_GET: 'variablesGet'
}

export const EDGETYPES = {
  CLOSABLE: 'closable'
}

export const nodesMap = {
  // 开始节点
  [NODETYPES.ENTRY]: { component: Entry, key: 'node' },
  // 结束节点
  [NODETYPES.EXIT]: { component: Exit, key: 'node' },

  [NODETYPES.LLM]: { component: Model, key: 'node' },
  [NODETYPES.SEMANTIC]: { component: Semantic, key: 'node' },
  [NODETYPES.KNOWLEDGE]: { component: Knowledge, key: 'node' },
  [NODETYPES.DENIAL]: { component: Denial, key: 'node' },

  [NODETYPES.ARC]: { component: Arc, key: 'node' },
  [NODETYPES.INTENT_DOMAIN]: { component: IntentDomain, key: 'node' },

  [NODETYPES.PK]: { component: PK, key: 'node' },
  [NODETYPES.FUNCTION]: { component: Function, key: 'node' },
  [NODETYPES.API]: { component: Api, key: 'node' },

  [NODETYPES.CODE]: { component: Code, key: 'node' },

  [NODETYPES.FLOW]: { component: WorkFlow, key: 'node' },
  [NODETYPES.LOGIC]: { component: Logic, key: 'node' },
  [NODETYPES.CHOICE]: { component: Choice, key: 'node', name: '分支器' },
  [NODETYPES.CONCURRENT]: { component: Concurrent, key: 'node' },
  [NODETYPES.PARALLEL]: { component: Logic, key: 'node' },
  [NODETYPES.AGENT]: { component: Agent, key: 'node' },
  [NODETYPES.PASS]: { component: Logic, key: 'node' },

  // 变量设置
  [NODETYPES.VARIABLE_SET]: { component: Function, key: 'node' },

  // 变量提取
  [NODETYPES.VARIABLE_GET]: { component: Function, key: 'node' }
}

export const SYMBOL_OPTIONS = [
  { label: '字符串等于', value: 'StringEquals', type: 'string' },
  { label: '字符串小于等于', value: 'StringLessThanEquals', type: 'string' },
  { label: '字符串大于等于', value: 'StringGreaterThanEquals', type: 'string' },
  { label: '字符串存在', value: 'StringExist', type: 'string' },

  { label: '字符串长度等于', value: 'StringLengthEquals', type: 'string' },
  { label: '字符串长度小于', value: 'StringLengthLessThan', type: 'string' },
  { label: '字符串长度大于', value: 'StringLengthGreaterThan', type: 'string' },

  { label: '数值等于', value: 'NumericEquals', type: 'number' },
  { label: '数值小于', value: 'NumericLessThan', type: 'number' },
  { label: '数值大于', value: 'NumericGreaterThan', type: 'number' },
  { label: '数值小于等于', value: 'NumericLessThanEquals', type: 'number' },
  { label: '数值大于等于', value: 'NumericGreaterThanEquals', type: 'number' },
  { label: '数值存在', value: 'NumericExist', type: 'number' },
  { label: '布尔值相等', value: 'BooleanEquals', type: 'boolean' },
  { label: '布尔值存在', value: 'BooleanExist', type: 'boolean' },
  // { label: '时间戳相等', value: 'TimestampEquals' },
  // { label: '时间戳小于', value: 'TimestampLessThan' },
  // { label: '时间戳大于', value: 'TimestampGreaterThan' },
  // { label: '时间戳小于等于', value: 'TimestampLessThanEquals' },
  // { label: '时间戳大于等于', value: 'TimestampGreaterThanEquals' },
  // { label: '时间戳存在', value: 'TimestampExist' }
  { label: '数值等于', value: 'NumericEquals', type: 'integer' },
  { label: '数值小于', value: 'NumericLessThan', type: 'integer' },
  { label: '数值大于', value: 'NumericGreaterThan', type: 'integer' },
  { label: '数值小于等于', value: 'NumericLessThanEquals', type: 'integer' },
  { label: '数值大于等于', value: 'NumericGreaterThanEquals', type: 'integer' },

  // 增加一波数组的比较
  { label: '数组长度等于', value: 'ArrayLengthEquals', type: 'array<string>' },
  { label: '数组长度小于', value: 'ArrayLengthLessThan', type: 'array<string>' },
  { label: '数组长度大于', value: 'ArrayLengthGreaterThan', type: 'array<string>' },
  { label: '数组长度小于等于', value: 'ArrayLengthLessThanEquals', type: 'array<string>' },
  { label: '数组长度大于等于', value: 'ArrayLengthGreaterThanEquals', type: 'array<string>' },
  { label: '数组存在', value: 'ArrayExist', type: 'array<string>' },

  { label: '数组长度等于', value: 'ArrayLengthEquals', type: 'array<boolean>' },
  { label: '数组长度小于', value: 'ArrayLengthLessThan', type: 'array<boolean>' },
  { label: '数组长度大于', value: 'ArrayLengthGreaterThan', type: 'array<boolean>' },
  { label: '数组长度小于等于', value: 'ArrayLengthLessThanEquals', type: 'array<boolean>' },
  { label: '数组长度大于等于', value: 'ArrayLengthGreaterThanEquals', type: 'array<boolean>' },
  { label: '数组存在', value: 'ArrayExist', type: 'array<boolean>' },

  { label: '数组长度等于', value: 'ArrayLengthEquals', type: 'array<object>' },
  { label: '数组长度小于', value: 'ArrayLengthLessThan', type: 'array<object>' },
  { label: '数组长度大于', value: 'ArrayLengthGreaterThan', type: 'array<object>' },
  { label: '数组长度小于等于', value: 'ArrayLengthLessThanEquals', type: 'array<object>' },
  { label: '数组长度大于等于', value: 'ArrayLengthGreaterThanEquals', type: 'array<object>' },
  { label: '数组存在', value: 'ArrayExist', type: 'array<object>' },

  { label: '数组长度等于', value: 'ArrayLengthEquals', type: 'array<number>' },
  { label: '数组长度小于', value: 'ArrayLengthLessThan', type: 'array<number>' },
  { label: '数组长度大于', value: 'ArrayLengthGreaterThan', type: 'array<number>' },
  { label: '数组长度小于等于', value: 'ArrayLengthLessThanEquals', type: 'array<number>' },
  { label: '数组长度大于等于', value: 'ArrayLengthGreaterThanEquals', type: 'array<number>' },
  { label: '数组存在', value: 'ArrayExist', type: 'array<number>' },

  { label: '数组长度等于', value: 'ArrayLengthEquals', type: 'array<integer>' },
  { label: '数组长度小于', value: 'ArrayLengthLessThan', type: 'array<integer>' },
  { label: '数组长度大于', value: 'ArrayLengthGreaterThan', type: 'array<integer>' },
  { label: '数组长度小于等于', value: 'ArrayLengthLessThanEquals', type: 'array<integer>' },
  { label: '数组长度大于等于', value: 'ArrayLengthGreaterThanEquals', type: 'array<integer>' },
  { label: '数组存在', value: 'ArrayExist', type: 'array<integer>' }
]

// 定义一个初始的concurrent节点的宽、高
export const CONCURRENT_DIMENSION = {
  width: 420,
  height: 240
}

export const DATA_TYPES_MAP = {
  string: { component: IconString, label: 'String' },
  boolean: { component: IconBoolean, label: 'Boolean' },
  object: { component: IconObject, label: 'Object' },
  number: { component: IconNumber, label: 'Number' },
  integer: { component: IconNumber, label: 'Integer' },

  'array<string>': { component: IconArrayString, label: 'Array<String>' },
  'array<boolean>': { component: IconArrayBoolean, label: 'Array<Boolean>' },
  'array<object>': { component: IconArrayObject, label: 'Array<Object>' },
  'array<number>': { component: IconArrayNumber, label: 'Array<Number>' },
  'array<integer>': { component: IconArrayNumber, label: 'Array<Integer>' }
}

/**
 * 不能建立连接的源节点-目标节点对
 */
export const CONNECTION_BLACK_LIST = [
  {
    source: NODETYPES.ENTRY,
    target: NODETYPES.EXIT,
    message: '开始节点不能连接结束节点'
  }
]

export const APP_ENV = import.meta.env.VITE_APP_ENV

export const COG_DOMAINS = [
  'cogs',
  'cog',
  'cog-auto',
  'cog-interaction',
  'sp8-jh-m26b',
  'cogs-pre-auto',
  'cogs-stable-auto',
  'cogsoslite',
  'max70-8k-v4',
  'max70-8k-v5',
  'sp-m13b-kl-gray',
  '3.5pro-2k',
  'cog_music',
  'sp8-m13b-interactionllm',
  'cogcarmaster'
]

export const cssVariables = {
  base: {
    '--sparkos-primary-color': '#4d53e8',
    '--sparkos-button-color': '#1D1F25', // 黑色
    '--flow-second-color': 'rgba(0, 0, 0, 0.8)',
    '--flow-desc-color': '#667085',
    '--flow-title-color': '#101828',
    '--flow-title-color2': '#354052',
    '--flow-content-color': '#495464',
    '--flow-placeholder-color': '#98a2b2',
    '--flow-bg-color': '#f5f6fb',
    '--flow-bg-color2': '#f9fafb',
    '--flow-bg-color3': '#fcfcfd',
    '--flow-bg-color4': '#f1f7ff',
    '--flow-success-color': 'rgba(0, 178, 60, 1)',

    '--sparkos-text-color': '#111827',
    '--sparkos-desc-color': '#6B7280',
    // 定义卡片列表卡片的样式
    '--sparkos-card-border-color': '#d1d5db',
    '--sparkos-card-border-radius': '12px',
    '--sparkos-card-background': 'linear-gradient(180deg,#ffffff, #fafafa 100%)',
    '--sparkos-card-hover-box-shadow':
      '0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 6px -1px rgba(0,0,0,0.10)'
  },
  auto: {
    '--sparkos-primary-color': '#3271fa',
    '--sparkos-button-color': '#3271fa',
    '--flow-second-color': 'rgba(0, 0, 0, 0.8)',
    '--flow-desc-color': '#667085',
    '--flow-title-color': '#101828',
    '--flow-title-color2': '#354052',
    '--flow-content-color': '#495464',
    '--flow-placeholder-color': '#98a2b2',
    '--flow-bg-color': '#f5f6fb',
    '--flow-bg-color2': '#f9fafb',
    '--flow-bg-color3': '#fcfcfd',
    '--flow-bg-color4': '#f1f7ff',
    '--flow-success-color': '#67C23A',

    '--sparkos-text-color': '#333',
    '--sparkos-desc-color': '#666',
    // 定义卡片列表卡片的样式
    '--sparkos-card-border-color': '#d1d5db',
    '--sparkos-card-border-radius': '12px',
    '--sparkos-card-background': 'linear-gradient(180deg,#ffffff, #fafafa 100%);',
    '--sparkos-card-hover-box-shadow': ''
  },
  aiui: {
    '--sparkos-primary-color': '#009BFF',
    '--flow-second-color': 'rgba(0, 0, 0, 0.8)',
    '--flow-desc-color': '#667085',
    '--flow-title-color': '#101828',
    '--flow-title-color2': '#354052',
    '--flow-content-color': '#495464',
    '--flow-placeholder-color': '#98a2b2',
    '--flow-bg-color': '#f5f6fb',
    '--flow-bg-color2': '#f9fafb',
    '--flow-bg-color3': '#fcfcfd',
    '--flow-bg-color4': '#f1f7ff',
    '--flow-success-color': 'rgba(0, 178, 60, 1)',

    '--sparkos-text-color': '#111827',
    '--sparkos-desc-color': '#6B7280',
    // 定义卡片列表卡片的样式
    '--sparkos-card-border-color': '#d1d5db',
    '--sparkos-card-border-radius': '12px',
    '--sparkos-card-background': 'linear-gradient(180deg,#ffffff, #fafafa 100%)',
    '--sparkos-card-hover-box-shadow':
      '0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 6px -1px rgba(0,0,0,0.10)'
  }
}

export const token = {
  base: {
    // Seed Token，影响范围大
    colorPrimary: cssVariables[APP_ENV]['--sparkos-primary-color'],
    colorText: '#495464',
    colorTextPlaceholder: '#98a2b2',
    colorBorder: '#DCDCDF',
    colorSuccess: 'rgba(0, 178, 60, 1)',
    colorWarning: 'rgba(255,161,84,1)',
    colorWarningBg: 'rgba(255,188,133,0.2)',
    colorFillTertiary: '#F5F6FB'
    // borderRadius: 2,

    // 派生变量，影响范围小
    // colorBgContainer: '#f6ffed'
  },
  auto: {
    // Seed Token，影响范围大
    colorPrimary: cssVariables[APP_ENV]['--sparkos-primary-color'],
    colorText: '#495464',
    colorTextPlaceholder: '#98a2b2',
    colorBorder: '#DCDCDF',
    colorWarning: 'rgba(255,161,84,1)',
    colorWarningBg: 'rgba(255,188,133,0.2)',
    colorFillTertiary: '#F5F6FB'
    // borderRadius: 2,

    // 派生变量，影响范围小
    // colorBgContainer: '#f6ffed'
  }
}

export const baseFooterStyles = {
  footer: {
    background: '#fafafa',
    borderTop: '1px solid #eaeaea',
    height: '68px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    padding: '0 24px',
    margin: '0 -24px -24px -24px', // 负边距来抵消弹窗的内边距，使背景色延伸到完整宽度
    borderBottomLeftRadius: '8px', // 添加底部左圆角
    borderBottomRightRadius: '8px' // 添加底部右圆角
  }
}

export const autoFooterStyles = {
  footer: {
    textAlign: 'center'
  }
}
