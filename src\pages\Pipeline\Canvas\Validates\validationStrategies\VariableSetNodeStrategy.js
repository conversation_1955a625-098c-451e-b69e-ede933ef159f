import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import Schema from 'async-validator'
import { getTreeData } from '@/hooks/getTreeData'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { validateNameRecursive } from './validateNameRecursive'

const validateNodeWithRules = async (node, rules, nodes, edges) => {
  const treeData = getTreeData(node, nodes, edges)
  const { validateInputData } = useInputDataValidate(treeData)
  let errors = []
  for (const field in rules) {
    if (field === 'output') {
      // 这里为方便处理，且考虑项目中实际，只对name进行校验
      for (const [index, data] of node.data?.[field]?.entries()) {
        const dataErrors = await validateNameRecursive(data, index, node.data?.[field])
        errors.push(...dataErrors)
      }
    } else {
      // 针对 field  是否是output等特殊类型
      const rule = field === 'input' ? rules[field](validateInputData) : rules[field]
      const validator = new Schema({ [field]: rule })
      try {
        await validator.validate({ [field]: node.data?.[field] })
      } catch ({ errors: validationErrors }) {
        if (validationErrors) {
          errors.push(...validationErrors)
        }
      }
    }
  }
  return errors
}

export const VariableSetNodeStrategy = async (node, nodes, edges) => {
  const rule = getRules(NODETYPES.VARIABLE_SET)
  const errors = await validateNodeWithRules(node, rule, nodes, edges)
  return errors
}
