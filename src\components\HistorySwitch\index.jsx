import React, { useState } from 'react'
import { Button, Form, Input, Select, Switch } from 'antd'
const { Option } = Select
const HistorySwitch = (props) => {
  const { id, value, onChange, ...rest } = props
  // const [val, setVal] = useState(-1)

  console.log('historySwitch-----------------', value)

  const onSwitchChange = (val) => {
    // setVal(val ? 1 : -1)
    onChange && onChange(val ? 1 : -1)
  }

  return (
    <span id={id}>
      <Switch onChange={onSwitchChange} checked={Number(value) > 0} {...rest} />
    </span>
  )
}

export default HistorySwitch
