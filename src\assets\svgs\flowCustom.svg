<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4228_4858)">
<g filter="url(#filter0_ddd_4228_4858)">
<path d="M5.08939 30.6246C4.82608 28.4907 6.29672 26.5304 8.41905 26.1863L28.7696 22.8864C30.9097 22.5394 32.9367 23.9605 33.34 26.0907L37.3664 47.357C37.7861 49.5738 36.2903 51.6989 34.0619 52.0518L12.3268 55.4943C10.0918 55.8483 8.00832 54.2793 7.73118 52.0335L5.08939 30.6246Z" fill="url(#paint0_linear_4228_4858)"/>
</g>
<g filter="url(#filter1_dddd_4228_4858)">
<path d="M12.8165 15.917C12.4215 12.7162 14.6275 9.77572 17.811 9.25951L43.3404 5.11993C46.5505 4.5994 49.591 6.73108 50.196 9.92641L55.2509 36.6248C55.8805 39.95 53.6369 43.1377 50.2942 43.6671L23.0265 47.9859C19.6739 48.5169 16.5488 46.1634 16.1331 42.7945L12.8165 15.917Z" fill="url(#paint1_linear_4228_4858)"/>
</g>
<mask id="mask0_4228_4858" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="5" width="44" height="44">
<path d="M12.8165 15.9165C12.4215 12.7157 14.6275 9.77523 17.811 9.25902L43.3404 5.11944C46.5505 4.59892 49.591 6.73059 50.196 9.92592L55.2509 36.6243C55.8805 39.9495 53.6369 43.1372 50.2942 43.6666L23.0265 47.9854C19.6739 48.5164 16.5488 46.1629 16.1331 42.7941L12.8165 15.9165Z" fill="url(#paint2_linear_4228_4858)"/>
</mask>
<g mask="url(#mask0_4228_4858)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M79.1588 21.5099L27.8119 -16.2069L28.1079 -16.6099L79.4548 21.1069L79.1588 21.5099ZM77.6195 23.6054L26.2727 -14.1114L26.5687 -14.5143L77.9155 23.2024L77.6195 23.6054ZM76.0803 25.7009L24.7334 -12.0159L25.0294 -12.4188L76.3763 25.2979L76.0803 25.7009ZM74.541 27.7964L23.1941 -9.92034L23.4901 -10.3233L74.837 27.3934L74.541 27.7964ZM73.0017 29.8919L21.6549 -7.82483L21.9509 -8.2278L73.2977 29.489L73.0017 29.8919ZM71.4625 31.9874L20.1156 -5.72931L20.4116 -6.13228L71.7585 31.5845L71.4625 31.9874ZM69.9232 34.083L18.5764 -3.63379L18.8724 -4.03676L70.2192 33.68L69.9232 34.083ZM68.384 36.1785L17.0371 -1.53828L17.3331 -1.94124L68.68 35.7755L68.384 36.1785ZM66.8447 38.274L15.4978 0.557241L15.7938 0.154272L67.1407 37.871L66.8447 38.274ZM65.3054 40.3695L13.9586 2.65276L14.2546 2.24979L65.6014 39.9665L65.3054 40.3695ZM63.7662 42.465L12.4193 4.74827L12.7153 4.34531L64.0622 42.0621L63.7662 42.465ZM62.2269 44.5605L10.8801 6.84379L11.1761 6.44082L62.5229 44.1576L62.2269 44.5605ZM60.6877 46.6561L9.34081 8.93931L9.63681 8.53634L60.9837 46.2531L60.6877 46.6561ZM59.1484 48.7516L7.80155 11.0348L8.09755 10.6319L59.4444 48.3486L59.1484 48.7516ZM57.6092 50.8471L6.26229 13.1303L6.55829 12.7274L57.9052 50.4441L57.6092 50.8471ZM56.0699 52.9426L4.72303 15.2259L5.01903 14.8229L56.3659 52.5396L56.0699 52.9426ZM54.5306 55.0381L3.18377 17.3214L3.47977 16.9184L54.8266 54.6352L54.5306 55.0381ZM52.9914 57.1336L1.64452 19.4169L1.94052 19.0139L53.2874 56.7307L52.9914 57.1336ZM51.4521 59.2292L0.105258 21.5124L0.401258 21.1094L51.7481 58.8262L51.4521 59.2292ZM49.9129 61.3247L-1.434 23.6079L-1.138 23.205L50.2089 60.9217L49.9129 61.3247ZM48.3736 63.4202L-2.97326 25.7034L-2.67726 25.3005L48.6696 63.0172L48.3736 63.4202ZM46.8343 65.5157L-4.51252 27.799L-4.21652 27.396L47.1303 65.1127L46.8343 65.5157ZM45.2951 67.6112L-6.05178 29.8945L-5.75578 29.4915L45.5911 67.2083L45.2951 67.6112ZM43.7558 69.7067L-7.59103 31.99L-7.29503 31.587L44.0518 69.3038L43.7558 69.7067Z" fill="url(#paint3_linear_4228_4858)"/>
</g>
<g filter="url(#filter2_i_4228_4858)">
<path d="M40.7448 34.9804C40.4131 35.0329 40.1209 34.9665 39.8682 34.7812C39.6155 34.5959 39.4633 34.3403 39.4117 34.0143L39.1735 32.5107L34.9652 33.1772C34.6402 33.2287 34.35 33.1624 34.0945 32.9785C33.8397 32.7926 33.6866 32.5371 33.6351 32.2121L32.7737 26.773L28.8098 27.4008L29.0437 28.8775C29.0952 29.2025 29.0284 29.4923 28.8434 29.7469C28.6584 30.0016 28.4034 30.1546 28.0784 30.2061L22.257 31.1281C21.9272 31.1803 21.636 31.1138 21.3832 30.9285C21.1305 30.7432 20.9783 30.4876 20.9267 30.1616L20.2298 25.7614C20.1783 25.4365 20.2451 25.1466 20.4301 24.892C20.6151 24.6374 20.8706 24.4842 21.1965 24.4326L27.0179 23.5106C27.3477 23.4584 27.6389 23.5249 27.8916 23.7102C28.1444 23.8955 28.2965 24.1507 28.348 24.4757L28.5861 25.9793L32.5486 25.3517L31.6828 19.8856C31.6312 19.5597 31.6979 19.2694 31.8829 19.0148C32.0688 18.76 32.3243 18.6068 32.6493 18.5554L36.8576 17.8888L36.6195 16.3851C36.568 16.0602 36.6348 15.7703 36.8198 15.5157C37.0048 15.2611 37.2602 15.1079 37.5862 15.0563L43.4332 14.1302C43.7648 14.0777 44.057 14.1441 44.3097 14.3294C44.5625 14.5147 44.7141 14.7699 44.7646 15.0951L45.4661 19.5237C45.5175 19.8487 45.4508 20.1385 45.2658 20.3931C45.0808 20.6478 44.8253 20.8009 44.4994 20.8525L38.6552 21.7782C38.3236 21.8307 38.0314 21.7643 37.7786 21.579C37.5259 21.3937 37.3738 21.1385 37.3223 20.8135L37.0842 19.3099L33.1473 19.9334L35.0132 31.7141L38.95 31.0906L38.7162 29.6139C38.6647 29.2889 38.731 28.9992 38.915 28.7447C39.0991 28.4902 39.3546 28.3371 39.6814 28.2853L45.5285 27.3592C45.8601 27.3067 46.1523 27.3731 46.405 27.5584C46.6578 27.7437 46.8094 27.9989 46.8599 28.3241L47.5568 32.7243C47.6085 33.0502 47.5418 33.3405 47.3568 33.5951C47.1718 33.8498 46.9163 34.0029 46.5904 34.0545L40.7448 34.9804Z" fill="url(#paint4_linear_4228_4858)"/>
</g>
<ellipse cx="6.33002" cy="41.7407" rx="4.08148" ry="4.15869" fill="#E6E8FC"/>
<path d="M6.06766 39.8733L6.59257 43.6082M4.46265 42.0032L8.19758 41.4783" stroke="#C4C7DE" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter3_d_4228_4858)">
<ellipse cx="52.3198" cy="22.1235" rx="4.08148" ry="4.15869" fill="#DFEDFB" fill-opacity="0.8" shape-rendering="crispEdges"/>
</g>
<path d="M52.0574 20.2561L52.5823 23.991M50.4524 22.386L54.1873 21.8611" stroke="#5AA4EF" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_ddd_4228_4858" x="4.05908" y="22.8345" width="34.3779" height="36.71" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.137255 0 0 0 0 0.160784 0 0 0 0.17 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4228_4858"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.137255 0 0 0 0 0.160784 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_4228_4858" result="effect2_dropShadow_4228_4858"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.137255 0 0 0 0 0.160784 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_4228_4858" result="effect3_dropShadow_4228_4858"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_4228_4858" result="shape"/>
</filter>
<filter id="filter1_dddd_4228_4858" x="8.771" y="5.0415" width="50.5859" height="58.019" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4228_4858"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.13 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_4228_4858" result="effect2_dropShadow_4228_4858"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_4228_4858" result="effect3_dropShadow_4228_4858"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="11"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_4228_4858" result="effect4_dropShadow_4228_4858"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow_4228_4858" result="shape"/>
</filter>
<filter id="filter2_i_4228_4858" x="20.2129" y="14.1128" width="27.3608" height="21.3848" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.25"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_4228_4858"/>
</filter>
<filter id="filter3_d_4228_4858" x="46.2383" y="16.9648" width="12.1631" height="12.3174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4228_4858"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4228_4858" result="shape"/>
</filter>
<linearGradient id="paint0_linear_4228_4858" x1="18.4506" y1="23.8798" x2="23.1855" y2="53.7748" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEEFF"/>
<stop offset="1" stop-color="#D4D7F0"/>
</linearGradient>
<linearGradient id="paint1_linear_4228_4858" x1="18.0077" y1="38.8632" x2="48.7044" y2="13.545" gradientUnits="userSpaceOnUse">
<stop stop-color="#F3F3F3"/>
<stop offset="1" stop-color="#C9F5FC"/>
</linearGradient>
<linearGradient id="paint2_linear_4228_4858" x1="30.3898" y1="6.32102" x2="36.6471" y2="45.8286" gradientUnits="userSpaceOnUse">
<stop stop-color="#7CDD9D"/>
<stop offset="1" stop-color="#20BC5F"/>
</linearGradient>
<linearGradient id="paint3_linear_4228_4858" x1="31.0394" y1="6.91709" x2="39.7702" y2="45.5947" gradientUnits="userSpaceOnUse">
<stop stop-color="#94C8EB"/>
<stop offset="1" stop-color="#E6F3F6"/>
</linearGradient>
<linearGradient id="paint4_linear_4228_4858" x1="38.3988" y1="16.2958" x2="32.8245" y2="36.7961" gradientUnits="userSpaceOnUse">
<stop stop-color="#0596FF" stop-opacity="0.7"/>
<stop offset="1" stop-color="#5799F7" stop-opacity="0.9"/>
</linearGradient>
<clipPath id="clip0_4228_4858">
<rect width="60" height="60" fill="white"/>
</clipPath>
</defs>
</svg>
