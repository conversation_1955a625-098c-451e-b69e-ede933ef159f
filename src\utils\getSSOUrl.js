export const isProduction = function () {
  const hostnames = [
    'www.iflyaicloud.com',
    'iflyaicloud.com',
    'pre.iflyaicloud.com',
    'in.iflyaicloud.com'
  ]
  if (hostnames.includes(window.location.hostname)) {
    return true
  } else {
    return false
  }
}

export const isReallyProduction = function () {
  const hostnames = [
    'www.iflyaicloud.com',
    'iflyaicloud.com',
    // 'pre.iflyaicloud.com',
    'in.iflyaicloud.com'
  ]
  if (hostnames.includes(window.location.hostname)) {
    return true
  } else {
    return false
  }
}

const getSSOloginUrl = function () {
  const isP = isProduction()
  if (isP) {
    return 'https://sso.iflytek.com:8443/sso/login?service='
  } else {
    return 'https://ssoqxb.iflytek.com:8443/sso/login?service='
  }
}

const getSSOlogoutUrl = function () {
  const isP = isProduction()
  if (isP) {
    return 'https://sso.iflytek.com:8443/sso/logout?service='
  } else {
    return 'https://ssoqxb.iflytek.com:8443/sso/logout?service='
  }
}

export const SSOloginUrl = getSSOloginUrl()
export const SSOlogoutUrl = getSSOlogoutUrl()
