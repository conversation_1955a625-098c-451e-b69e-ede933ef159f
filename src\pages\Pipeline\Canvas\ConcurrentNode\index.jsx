import React, { memo, useCallback } from 'react'

import { useNodes, Handle, NodeResizer, NodeResizeControl } from '@xyflow/react'

import NodeHeader from '@/components/NodeHeader'
import { NODETYPES, CONCURRENT_DIMENSION } from '@/utils/constant'
import useStore from '@/store.js'

function ResizeIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      strokeWidth="2"
      stroke="#ff0071"
      fill="none"
      strokeLinecap="round"
      strokeLinejoin="round"
      style={{ position: 'absolute', right: 5, bottom: 5 }}
    >
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <polyline points="16 20 20 20 20 16" />
      <line x1="14" y1="14" x2="20" y2="20" />
      <polyline points="8 4 4 4 4 8" />
      <line x1="4" y1="4" x2="10" y2="10" />
    </svg>
  )
}

export default memo(({ data, sourcePosition, targetPosition, isConnectable, id }) => {
  const nodes = useNodes()
  const { debouncedSetDirty } = useStore()

  const onResizeEnd = (event, params) => {
    console.log('onResize end', params)
    debouncedSetDirty()
  }
  const controlStyle = {
    background: 'transparent',
    border: 'none'
  }
  return (
    <div className="">
      <NodeHeader id={id} type={NODETYPES.CONCURRENT} data={data} />
      <NodeResizer
        minWidth={CONCURRENT_DIMENSION.width}
        minHeight={CONCURRENT_DIMENSION.height}
        onResizeEnd={onResizeEnd}
      />
      {/* <NodeResizeControl style={controlStyle} minWidth={360} minHeight={150} onResizeEnd={onResizeEnd}>
        <ResizeIcon />
      </NodeResizeControl> */}
    </div>
  )
})
