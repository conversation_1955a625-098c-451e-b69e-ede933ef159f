import { NODETYPES } from '@/utils/constant'

import { LLMNodeAdapter } from './LLMNodeAdapter'
import { KnowledgeNodeAdapter } from './KnowledgeNodeAdapter'
import { APINodeAdapter } from './APINodeAdapter'
import { ChoiceNodeAdapter } from './ChoiceNodeAdapter'
import { CodeNodeAdapter } from './CodeNodeAdapter'
import { FlowNodeAdapter } from './FlowNodeAdapter'
import { PassNodeAdapter } from './PassNodeAdapter'

import { EntryNodeAdapter } from './EntryNodeAdapter'
import { ExitNodeAdapter } from './ExitNodeAdapter'
import { ConcurrentNodeAdapter } from './ConcurrentNodeAdapter'
import { VariableSetNodeAdapter } from './VariableSetNodeAdapter'
import { VariableGetNodeAdapter } from './VariableGetNodeAdapter'
import { FunctionNodeAdapter } from './FunctionNodeAdapter'

export class NodeAdapterFactory {
  static getAdapter(nodeType) {
    switch (nodeType) {
      case NODETYPES.SEMANTIC:
      case NODETYPES.DENIAL:
      case NODETYPES.ARC:
      case NODETYPES.INTENT_DOMAIN:
      case NODETYPES.PK:

      case NODETYPES.LLM:
        return LLMNodeAdapter
      case NODETYPES.FUNCTION:
        return FunctionNodeAdapter
      case NODETYPES.KNOWLEDGE:
        return KnowledgeNodeAdapter
      case NODETYPES.API:
        return APINodeAdapter
      case NODETYPES.CHOICE:
        return ChoiceNodeAdapter
      case NODETYPES.CODE:
        return CodeNodeAdapter
      case NODETYPES.FLOW:
        return FlowNodeAdapter
      case NODETYPES.PASS:
        return PassNodeAdapter

      case NODETYPES.ENTRY:
        return EntryNodeAdapter
      case NODETYPES.EXIT:
        return ExitNodeAdapter
      case NODETYPES.CONCURRENT:
        return ConcurrentNodeAdapter
      case NODETYPES.VARIABLE_SET:
        return VariableSetNodeAdapter
      case NODETYPES.VARIABLE_GET:
        return VariableGetNodeAdapter
      default:
        return null
    }
  }
}
