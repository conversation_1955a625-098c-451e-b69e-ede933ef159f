import InputOrOutput from '../InputOrOutput'
import useInput from '@/hooks/useInput'
import { useNodesData , useEdges} from '@xyflow/react'
import useRefTreeData from '@/hooks/useRefTreeData'
import useElements from '@/hooks/useElements'

function Input({ inputOrOutput, label = '输入', id, ...rest }) {
  // 获取带类型的input
  const input = useInput(inputOrOutput)
  const nodeData = useNodesData(id)
  const elements = useElements()
  const treeData = useRefTreeData(nodeData, elements, true)
  // console.log('------useInput input------', input, nodeData, treeData)
  return <InputOrOutput inputOrOutput={input} label={label} treeData={treeData} {...rest} />
}
export default Input
