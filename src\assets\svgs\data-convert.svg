<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_4540_5033)">
<rect width="26" height="26" rx="8" fill="#4575FF"/>
<rect width="26" height="26" rx="8" fill="url(#paint0_radial_4540_5033)" fill-opacity="0.5"/>
<g clip-path="url(#clip0_4540_5033)">
<path d="M13.0002 13.348L15.9005 11.5353M13.0002 13.348L10.0999 11.5353L13.0002 9.72266L15.9005 11.5353M13.0002 13.348V16.6109L15.9005 14.7982V11.5353" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.0999 11.5352V14.798L13.0002 16.6107" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13 19.5381V19.5454M6.4707 10.8324V10.8397M19.5293 10.8324V10.8397M10.0981 18.8852C9.10806 18.4023 8.25629 17.6768 7.62206 16.7762C6.98783 15.8756 6.59176 14.8292 6.4707 13.7343M15.9019 18.8852C16.8919 18.4023 17.7437 17.6768 18.3779 16.7762C19.0122 15.8756 19.4082 14.8292 19.5293 13.7343M8.79223 7.93051C9.95887 6.97575 11.4199 6.4541 12.9275 6.4541C14.435 6.4541 15.896 6.97575 17.0627 7.93051" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_i_4540_5033" x="-1" y="-2" width="27" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_4540_5033"/>
</filter>
<radialGradient id="paint0_radial_4540_5033" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8.31148 5.11475) rotate(55.9679) scale(19.8013 19.8013)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_4540_5033">
<rect width="16" height="16" fill="white" transform="translate(5 5)"/>
</clipPath>
</defs>
</svg>
