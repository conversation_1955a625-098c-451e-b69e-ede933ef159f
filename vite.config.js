import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import svgr from 'vite-plugin-svgr'
// import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'
import { resolve } from 'path'
import { viteStaticCopy } from 'vite-plugin-static-copy'
// import legacy from '@vitejs/plugin-legacy'
import { federation } from '@module-federation/vite'
import topLevelAwait from 'vite-plugin-top-level-await'
import { dependencies } from './package.json'

const proxys = {
  base: {
    '/iflycloud/api': {
      target: 'https://dev.iflyaicloud.com',
      // target: 'http://aitest.iflyaicloud.com:8000',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    },
    '/api/v1': {
      target: 'https://dev.iflyaicloud.com',
      // target: 'http://aitest.iflyaicloud.com:8000',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    }
  },
  auto: {},
  aiui: {
    '/aiui/web': {
      target: 'http://teststudio.iflyos.cn',
      // target: 'https://aiui.xfyun.cn/',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    },
    '/api/v1': {
      target: 'https://dev.iflyaicloud.com',
      // target: 'https://aiui.xfyun.cn/',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    },
    '/SSOService': {
      // target: 'https://sso.xfyun.cn',
      target: 'https://ssodev.xfyun.cn',
      changeOrigin: true
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  const isDevCommand = command === 'serve'
  const proxy = proxys[env.VITE_APP_ENV]

  return {
    base: `${env.VITE_ROUTER_BASE_URL}/flow/`,
    // css: {
    //   preprocessorOptions: {
    //     scss: {
    //       // 全局引入的 SCSS 文件路径
    //       additionalData: `@import "assets/css/variables.scss";`
    //     }
    //   }
    // },
    plugins: [
      svgr(),
      isDevCommand
        ? null
        : federation({
            filename: 'remoteEntry.js',
            name: 'remote',
            exposes: {
              './remote-app': './src/pages/PipelineList/indexWrap.jsx'
            },
            shared: ['react', 'react-dom', 'react-router-dom']
          }),
      react(),
      topLevelAwait(),
      viteStaticCopy({
        targets: [
          {
            src: 'node_modules/monaco-editor/min/vs',
            dest: 'assets/monaco-editor' // 输出到 dist/monaco-editor/vs
          }
        ]
      })
    ].filter(Boolean),

    resolve: {
      alias: {
        '@': resolve('./src'),
        assets: resolve('./src/assets')
      }
    },
    server: {
      port: 9999,
      proxy
    }
  }
})
