import { message } from 'antd'

import { useOfficialAbilityStore } from '@/store/officialAbility'
import { NODETYPES } from './constant'

export const identifierOptions = [
  {
    label: '空两格',
    value: '  '
  },
  {
    label: '空格',
    value: ' '
  },
  {
    label: '中文逗号',
    value: '，'
  },
  {
    label: '英文逗号',
    value: ','
  },
  {
    label: '中文感叹号',
    value: '！'
  },
  {
    label: '英文感叹号',
    value: '!'
  },
  {
    label: '中文句号',
    value: '。'
  },
  {
    label: '英文句号',
    value: '.'
  },
  {
    label: '中文问号',
    value: '？'
  },
  {
    label: '英文问号',
    value: '?'
  },
  {
    label: '制表符',
    value: '\t'
  }
]

// 根据文件名获取文件格式
export function extractFileFormat(fileName) {
  // 检查文件名是否为空
  if (!fileName || typeof fileName !== 'string') {
    return ''
  }
  // 使用正则表达式提取文件格式
  const match = fileName.match(/\.([^.]+)$/)
  // 如果匹配成功，返回文件格式，否则返回空字符串
  return match ? match[1] : ''
}

/**
 * 生成快速调试 uid
 */
export function experienceUid() {
  let randomStr = ''
  for (let i = 0; i < 6; i++) {
    const randomNum = Math.ceil(Math.random() * 25)
    randomStr += String.fromCharCode(97 + randomNum)
  }
  return new Date().getTime() + randomStr
}

/**
 * 复制到剪切板
 */
export function copyClipboard(value) {
  const input = document.createElement('textarea')
  document.body.appendChild(input)
  input.value = value
  input.select()
  document.execCommand('copy')
  document.body.removeChild(input)
  message.success('成功复制到粘贴板中')
}

export function generateUrl(obj) {
  let res = ''
  for (const key in obj) {
    if (res !== '') {
      res += '&'
    }
    res += `${key}=${encodeURIComponent(obj[key])}`
  }
  return res
}

export const checkUtf = (content) => {
  for (let b = 0; b < content.length; b++) {
    // If ? is encountered it's definitely not utf8!
    if (content[b] === '�') {
      return false
    }
  }
  return true
}

export const regTest = (target, type) => {
  if (!type) {
    return false
  }
  let types = {
    phone: /^1[3456789]\d{9}$/
  }
  return types[type].test(target)
}

export const clearCookie = () => {
  var keys = document.cookie.match(/[^ =;]+(?=\=)/g)
  // 主账号退出时，避免清楚子账号的 cookies
  if (keys) {
    keys = keys.filter((item) => !['sub_account_id', 'subSessionId'].includes(item))
    for (var i = keys.length; i--; ) {
      document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString()
      document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString() + ';path=/;'
    }
  }
}

export const setCookie = (name, value) => {
  var Days = 30
  var exp = new Date()
  exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000)
  document.cookie = `${name}=${escape(value)};expires=${exp.toGMTString()};path=/`
}

export const getCookie = (name) => {
  var arr
  var reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
  if ((arr = document.cookie.match(reg))) {
    return unescape(arr[2])
  } else {
    return null
  }
}

export function debounce(fn, wait = 500, immediate) {
  let timer = null
  return function (...args) {
    if (timer) {
      clearTimeout(timer)
    }
    // immediate 为 true时, 表示第一次触发时也执行
    // timer 为空时，表示首次触发
    if (immediate && !timer) {
      fn.apply(this, args)
    }
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, wait)
  }
}

export function formatFileSize(bytes) {
  if (bytes === 0 || !bytes || bytes === '') return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getCurrentUser() {
  let user = sessionStorage.getItem('user') || '{}'
  user = decodeURI(user)
  user = JSON.parse(user)
  return user.username || ''
}

function findMissingNumber(arr) {
  // 确保数组是数字类型并升序排序
  const sortedArr = [...new Set(arr)].sort((a, b) => a - b)

  // 从数组的最小值开始递增查找
  let candidate = sortedArr[0] // 从最小值开始
  while (sortedArr.includes(candidate)) {
    candidate++ // 如果包含当前值，则递增
  }

  return candidate // 返回第一个不包含的数字
}

const getGrowingIndex = (existedNodes = []) => {
  // 取得existedNodes中的name后面的index,放在一个数组中
  let serialNumber = []
  existedNodes.forEach((nd) => {
    if (nd?.id && nd?.id.includes('_')) {
      const str = nd?.id
      const lastPart = str.substring(str.lastIndexOf('_') + 1)
      console.log(lastPart)
      const isNumber = !isNaN(lastPart) && !isNaN(parseInt(lastPart))
      if (isNumber) {
        serialNumber.push(parseInt(lastPart))
      }
    }
  })
  if (serialNumber.length === 0) {
    return 1
  } else {
    return findMissingNumber(serialNumber)
  }
}

/**
 *
 * @param {*} nodes 画布上所有节点
 * @param {*} data  待插入节点的参考节点的data 1、拷贝的情况 指的是源节点 2、新增的情况 指的是即将新增的节点
 */
export const genNewNodeIdAndTitle = (nodes, type, data) => {
  let tempTitle = ''
  let id = ''
  if (data?.abilityOriginId) {
    const existedAbilityNodes = nodes.filter(
      (nd) => nd.data?.abilityOriginId === data?.abilityOriginId
    )
    if (existedAbilityNodes.length === 0) {
      tempTitle = data?.name || ''
      id = `${data?.abilityOriginId}`
    } else {
      const number = getGrowingIndex(existedAbilityNodes)
      tempTitle = `${data?.name || ''}_${number}`
      id = `${data?.abilityOriginId}_${number}`
    }
  } else {
    const existedNodes = nodes.filter((nd) => nd.type === type)
    if (existedNodes.length === 0) {
      tempTitle = data?.name || ''
      id = type
    } else {
      const number = getGrowingIndex(existedNodes)
      if (number > 0) {
        tempTitle = `${data?.name || ''}_${number}`
        id = `${type}_${number}`
      } else {
        tempTitle = `${data?.name || ''}`
        id = `${type}`
      }
    }
  }
  return { id, name: tempTitle }
}

const generateUniqueAbilityId = (existingNodes, type, data, isCustom) => {
  // 
  const { abilities } = useOfficialAbilityStore.getState()



  // const baseName = data.abilityOriginId ? data.abilityOriginId.replace(/^cbm_/, '') : type || 'llm'
  let baseName
  if(type === NODETYPES.FUNCTION) {
    const temp = data.subType || data.abilityOriginId
    baseName = temp ? temp.replace(/^cbm_/, '') : type || 'llm'
  } else {
    baseName = data.abilityOriginId ? data.abilityOriginId.replace(/^cbm_/, '') : type || 'llm'
  }
  
  let counter = 0
  let newName = baseName

  if (isCustom) {
    // 检查生成的 name 是否已存在，如果存在则递增 counter
    while (
      existingNodes.find((nd) => nd.data.abilityId === newName) ||
      abilities.includes(`cbm_${newName}`)
    ) {
      counter++
      newName = baseName + '_' + counter
    }
  } else {
    // 检查生成的 name 是否已存在，如果存在则递增 counter
    while (existingNodes.find((nd) => nd.data.abilityId === newName)) {
      counter++
      newName = baseName + '_' + counter
    }
  }

  return newName
}

/**
 *
 * @param {*} nodes
 * @param {*} type
 * @param {*} data
 * @param {*} reuse 是否重用abilityId
 * @returns
 */
export const genNewAbilityId = (nodes, type, data = {}, isCustom) => {
  return generateUniqueAbilityId(nodes, type, data, isCustom)
}
