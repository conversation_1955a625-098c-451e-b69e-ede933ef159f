.content-right {
//   flex: 1;
//   height: 100%;
//   position: fixed;
//   bottom: 20px;
//   right: 124px;
//   z-index: 1001;
//   width: 467px;
//   height: 90vh;
//   max-height: 894px;
//   border-radius: 17px;
//   background: linear-gradient(179deg, #cbe2ff 1%, #ffffff 100%);
//   border: 1px solid #cadcf8;
//   opacity: 1;
//   transition: opacity 0.2s linear, transform 0.25s linear;
height: calc(100% - 70px);

  &.content-right-hide {
    bottom: -100vh;
    opacity: 0;
    visibility: hidden !important;
    z-index: -1 !important;
    transform: translateY(40px);
  }

  .title {
    height: 56px;
    line-height: 56px;
    font-size: 16px;
    font-weight: 400;
    color: #222222;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #b5ceef;
    padding: 0 20px;
    cursor: move;
  }

//   .top-guide {
//     display: flex;
//     padding: 20px;

//     .top-guide-head {
//       width: 65px;
//       height: 72px;
//       background: url(assets/images/robot.png) center/contain no-repeat;
//     }

//     .top-guide-body {
//       margin-left: 20px;

//       .top-guide-title {
//         font-size: 18px;
//         font-weight: 600;
//         color: #222222;
//         line-height: 25px;
//       }

//       .top-guide-desc {
//         margin-top: 4px;
//         font-size: 12px;
//         font-weight: 400;
//         color: #8093ad;
//         line-height: 17px;
//       }
//     }
//   }

  .rightChatboxContainer {
    width: 100%;
    height: calc(100% - 60px);
    // height: 100%;
    padding-top: 10px;
    position: relative;
    overflow: auto;
  }

  .rightChatbox {
    width: 100%;
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding: 0 50px;

    &::-webkit-scrollbar-track {
      // background-color: rgba(255, 255, 255, 0.3);
      display: none;
    }

    &::-webkit-scrollbar-thumb {
      // background: #bacff2;
      display: none;
    }
  }

  .control-bar-wrap {
    // height: 140px;
    // padding: 18px 20px 0;
    display: flex;
  }

  .control-bar-input {
    position: relative;
    width: 100%;
    // height: 130px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .sendWrapper {
    align-items: center;
    position: relative;
    flex: 1;

    .send {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 10px;
      bottom: 14px;
      cursor: pointer;
      width: 68px;
      height: 40px;
      background: linear-gradient(180deg, var(--sparkos-primary-color), #9ad0ff);
      border-radius: 20px;

      img {
        margin-left: -2px;
        transform: scale(0.5);
      }
    }

    .delete {
      position: absolute;
      right: 10px;
      bottom: 110px;
    }

    textarea {
      width: 100%;
      height: 90px;
      // margin-top: 30px;
      font-size: 14px;
      font-weight: 400;
      color: #222222;
      line-height: 22px;
      padding: 15px 100px 0 15px;
      outline: none;
      background: #eaf0f8;
      border: 1px solid rgba(255, 255, 255, 0.87);
      border-radius: 12px;

      &::placeholder {
        color: #b1bccb;
      }
    }
  }

  .chatbox {
    position: relative;
    margin-bottom: 24px;

    &.me {
      display: flex;
      justify-content: flex-start;
      flex-direction: row-reverse;

      .icon-me {
        width: 40px;
        height: 40px;
        position: absolute;
        z-index: 1;
        top: 0;
        right: -50px;
        background: url(assets/images/icon-me2.png) center/contain no-repeat;
      }

      .con {
        display: flex;
        align-items: center;
        max-width: 90%;
        min-height: 40px;
        padding: 12px 16px;
        background: var(--sparkos-primary-color);
        border-radius: 12px 0 12px 12px;
        padding-right: 16px;
        font-size: 14px;
        line-height: 20px;
        word-break: break-word;
        box-shadow: -6px 0px 4.65px 0.42px rgba(201, 221, 255, 0.3);
        color: #fff;
      }
    }

    &.ai {
      .icon-ai {
        width: 40px;
        height: 40px;
        position: absolute;
        z-index: 1;
        top: 0px;
        left: -50px;
        background: url(assets/images/icon-ai.png) center/contain no-repeat;
      }
      .con {
        color: #fff;
        box-sizing: border-box;
        width: fit-content;
        max-width: 300px;
        min-width: 200px;
        min-height: 40px;
        padding: 12px 16px 12px 16px;
        background: #fff;
        border-radius: 0px 12px 12px 12px;
        color: #282c33;
        font-size: 14px;
        line-height: 20px;
        word-break: break-all;

        pre {
          overflow: auto;
        }
      }
    }
  }
}
