import { But<PERSON>, Collapse, Form, Divider, Input, Select, Space, Switch } from 'antd'

import React, { memo, useCallback, useEffect } from 'react'
import { CheckOutlined, CloseCircleOutlined, CloseOutlined } from '@ant-design/icons'

import { useReactFlow, useNodesData } from '@xyflow/react'
import { debounce } from 'lodash'

import FormItemRenderer from '@/components/FormItemRender'
import useRefTreeData from '@/hooks/useRefTreeData'
import IconCustomFlow from 'assets/svgs/cog.svg?react'
import { useModelStore } from '@/store/model'
import { useKnowledgeStore } from '@/store/knowledge'
import { DATA_TYPES_MAP, NODETYPES } from '@/utils/constant'
import InputData from '@/components/InputData/input_data.jsx'

import { globalDelayUpdate } from '@/utils/delayUpdate.js'
import { getRules, outputRule } from '@/utils/rule'
import useStore from '@/store.js'
import useRefUpdate from '@/hooks/useRefUpdate'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { BlurInput, BlurTextArea } from '@/components/BlurInput'
import withDividingLine from '@/components/WithDividingLine'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'

const genLabelTitle = (title) => {
  return <span className="text-[var(--flow-title-color2)] text-[14px] font-semibold">{title}</span>
}

export default memo((props) => {
  const { node, onClose, visible } = props
  const [form] = Form.useForm()
  const { updateNodeData } = useReactFlow()
  const { updateRefName } = useRefUpdate(node)
  const { debouncedSetDirty } = useStore()
  const nodeData = useNodesData(node.id)
  const hasExpand = (nodeData?.data?.output || [])
    .filter(Boolean)
    .some((val) => (val.schema || []).length > 0)
  const treeData = useRefTreeData(node, form, visible)
  const { validateInputData } = useInputDataValidate(treeData)

  const models = useModelStore((state) => state.model)
  const knowledges = useKnowledgeStore((state) => state.knowledge)

  const domains = node.type === NODETYPES.KNOWLEDGE ? knowledges : models

  const ruleObj = getRules(node.type)

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log('\n\n----onValuesChange---', allValues)
    updateNodeData(node.id, allValues)
    debouncedSetDirty()
  }

  const setFields = () => {
    console.log('useEffect trigged pass drawer', node.type, node?.data)
    form.setFieldsValue({
      // domain: node.data?.domain,
      // abilityId: node.data?.abilityId,
      // abilityName: node.data?.abilityName,
      // prompt: node.data?.prompt,
      // audit: !!node.data?.audit,
      dist: !!node.data?.dist,
      flow_in: !!node.data?.flow_in,
      flow_out: !!node.data?.flow_out,
      input: node.data?.input,
      output: node.data?.output
    })

    //  校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      setTimeout(() => {
        form.validateFields()
      })
    }
  }

  const onNameChange = (path, value) => {
    console.log('onNameChange', path, value)
    updateRefName(path, value)
  }

  useEffect(() => {
    setFields()
    // 校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      form.validateFields()
    }
  }, [node, visible, form])

  let inputFormListAdd
  let outputFormListAdd

  const genExtra = (isInput) => (
    <Space size={0} split={<Divider type="vertical" />}>
      {/* {isInput && (
        <Switch
          key="memory"
          checkedChildren={
            <span>
              记忆
              <CheckOutlined />
            </span>
          }
          unCheckedChildren={
            <span>
              记忆
              <CloseOutlined />
            </span>
          }
        />
      )} */}
      {!isInput && (
        <Form.Item name="dist" style={{ marginBottom: 0 }}>
          <Switch
            checkedChildren={
              <span>
                下发
                <CheckOutlined />
              </span>
            }
            unCheckedChildren={
              <span>
                下发
                <CloseOutlined />
              </span>
            }
          />
        </Form.Item>
      )}

      <Form.Item
        name={isInput ? 'flow_in' : 'flow_out'}
        initialValue={true}
        style={{ marginBottom: 0 }}
      >
        <Switch
          disabled
          key="flow"
          checkedChildren={
            <span>
              流式
              <CheckOutlined />
            </span>
          }
          unCheckedChildren={
            <span>
              流式
              <CloseOutlined />
            </span>
          }
        />
      </Form.Item>

      {/* <Button
        type="dashed"
        onClick={() => {
          if (isInput) {
            inputFormListAdd({})
          } else {
            outputFormListAdd()
          }
        }}
        size="small"
      >
        +
      </Button> */}
    </Space>
  )

  const items = [
    {
      key: '3',
      label: genLabelTitle('输入'),
      children: (
        <Form.List name="input">
          {(subFields, { add, remove }) => {
            inputFormListAdd = add
            return (
              <div className="relative">
                {subFields.map((subField) => {
                  // 获取当前subField对应的method值
                  // const method = subField?.value?.method
                  const method = form.getFieldValue(['input', subField.name, 'method'])

                  console.log('method', method)
                  return (
                    <>
                      <Space
                        key={subField.key + 'ext'}
                        style={{ display: 'flex', marginBottom: -4 }}
                        align="middle"
                        size={4}
                      >
                        <Form.Item
                          name={[subField.name, 'key']}
                          rules={[
                            ...outputRule.name,
                            ({ getFieldValue }) => ({
                              validator(_, value) {
                                if (value) {
                                  const currentIndex = subField.name

                                  const sameLevelVal = getFieldValue(['input'])
                                  for (let i = 0; i < sameLevelVal.length; i++) {
                                    if (i === currentIndex) {
                                      continue
                                    }
                                    if (sameLevelVal[i]?.key === value) {
                                      return Promise.reject(new Error('变量名不可重复'))
                                    }
                                  }
                                }

                                return Promise.resolve()
                              }
                            })
                          ]}
                        >
                          <BlurInput placeholder="参数名"  maxLength={32} disabled />
                        </Form.Item>
                        <Form.Item
                          name={[subField.name, 'value']}
                          style={{ marginBottom: 0, width: 198 }}
                          rules={[{ validator: (rule, value) => validateInputData(rule, value) }]}
                        >
                          <InputData options={treeData} placeholder={'请输入或引用参数值'} />
                        </Form.Item>
                        {subFields.length > 1 && (
                          <Form.Item style={{ marginBottom: 8, width: 10 }}>
                            <CloseCircleOutlined
                              onClick={() => remove(subField.name)}
                              className="text-[var(--flow-desc-color)]"
                            />
                          </Form.Item>
                        )}
                      </Space>
                    </>
                  )
                })}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(true)
    },

    {
      key: '11',
      label: genLabelTitle('输出'),
      children: (
        <Form.List name="output">
          {(subFields, { add, remove }) => {
            // console.log('****************form.getFieldValue(output)**************', form.getFieldValue('output'))
            outputFormListAdd = add

            return (
              <div className="relative">
                {subFields.map((subField) => (
                  <Space align="start" size={4} key={subField.key}>
                    <FormItemRenderer
                      field={subField}
                      remove={remove}
                      form={form}
                      path={['output', subField.name]}
                      closeable={subFields.length > 1}
                      hasExpand={hasExpand}
                      onNameChange={onNameChange}
                      isTop={true}
                    />
                    {/* <Form.Item style={{ marginBottom: 8, width: 20 }}>
                    <CloseCircleOutlined
                      onClick={() => remove(subField.name)}
                      className="text-[var(--flow-desc-color)]"
                    />
                  </Form.Item> */}
                  </Space>
                ))}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(false)
    }
  ]
    .filter(Boolean)
    .map((item) => {
      return {
        ...item,
        children: withDividingLine(item.children)
      }
    })

  return (
    <Form
      form={form}
      name="basic"
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      labelWrap
      layout="vertical"
      onValuesChange={onValuesChange}
      requiredMark={false}
      className="flex-1 overflow-auto mr-[-24px] pr-0"
      preserve={false}
    >
      <Collapse
        items={items}
        defaultActiveKey={['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']}
        size="small"
        collapsible={'icon'}
        ghost
        className="pr-[20px]"
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
      />
    </Form>
  )
})
