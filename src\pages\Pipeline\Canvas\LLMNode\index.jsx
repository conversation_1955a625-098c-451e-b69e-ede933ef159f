import React, { memo, useCallback } from 'react'

import { useN<PERSON>, Handle, useNodeConnections, useReactFlow } from '@xyflow/react'
import cls from 'classnames'
import Icon from 'assets/svg/nd-switch.svg?react'
import { Tooltip, Popover } from 'antd'
import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons'
import TextEdit from '@/components/TextEdit'
import Form from './Form'
import NodeHeader from '@/components/NodeHeader'
import { NODETYPES, ENTRY_SOURCE_ID } from '@/utils/constant'

export default memo((props) => {
  const { updateNodeData } = useReactFlow()

  const { data, sourcePosition, targetPosition, isConnectable, id } = props
  const nodes = useNodes()

  const connections = useNodeConnections({
    id,
    type: 'target'
  })
  const preNodeIsEntry = !!connections?.find((c) => c.source === ENTRY_SOURCE_ID && c.target === id)
  if (preNodeIsEntry && data.flow_in) {
    updateNodeData(id, { flow_in: false })
    // 不需要保存，外面监听connection的地方有保存
  }

  // console.log('useNodeConnections in llnode-------', connections)

  return (
    <div>
      <NodeHeader id={id} type={data?.type} data={data} />
      <Form {...props} />
      <Handle
        type="source"
        position={sourcePosition}
        className="handle"
        id="source"
        isConnectable={isConnectable}
      />
      <Handle
        type="target"
        position={targetPosition}
        className="handle"
        id="target"
        isConnectable={isConnectable}
      />
    </div>
  )
})
