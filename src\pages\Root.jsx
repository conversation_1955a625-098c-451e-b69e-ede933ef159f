import { Link, Outlet } from 'react-router-dom'
import React, { useState, useEffect } from 'react'

import { Layout } from 'antd'

import styles from './style.module.scss'

import Headers from './Header'
import { APP_ENV } from '@/utils/constant'

const { Content } = Layout

const Root = () => {
  return (
    <div className={styles.container}>
      <Layout className={styles.layout}>
        {APP_ENV !== 'auto' && <Headers />}
        <div className={styles.body}>
          {APP_ENV !== 'auto' && (
            <div className={styles.banner}>
              <div className={styles.bannerLeft}>
                <div className={styles.bannerLeftTitle}>工作流</div>
                <div className={styles.bannerLeftDesc}>
                  AI应用流程定制工具, 帮助定义应用的运行步骤, 从输入到输出, 对业务进行流程化抽象。
                </div>
              </div>
              <div className={styles.bannerRight}></div>
            </div>
          )}
          <Content className={styles.content}>
            <Outlet />
          </Content>
        </div>
      </Layout>
    </div>
  )
}
export default Root
