import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'

import { MarkerType, Position, getOutgoers } from '@xyflow/react'


// 1、后端给定的 API 节点模板
const codeTemplate = {
  ability: {
    name: '代码执行器',
    id: 'cbm_code_interpreter',
    type: 'CODE',
    code: 'async def main(args: Args) -> Output:\r\n\t\t    params = args.params\r\n\t\t    # 构建输出对象\r\n\t\t    ret: Output = {\r\n\t\t        "key0": params[\'input\'] + params[\'input\'], \r\n\t\t        "key1": ["hello", "world"],  \r\n\t\t        "key2": { # 输出一个Object \r\n\t\t            "key21": "hi"\r\n\t\t        },\r\n\t\t    }\r\n\t\t    return ret'
  },
  audit: true,
  description: '编写代码，处理输入变量来生成返回值',
  flow_in: false,
  flow_out: false,
  input: {
    text: {}
  },
  nodeType: 'code',
  output: { nlp: { desc: { format: 'plain', schema: { type: 'string' } } } },
  title: '代码执行器',
  type: 'task',
  dist: true
}

/**
 * 将大模型后端模板转成前端data
 * @param {*} template
 */
const convertCodeTemplateFromBack2Front = function (template = codeTemplate) {
  // validated 前端字段，在这里过滤
  let { ability, input, inputAlias, nodeType, output, title, type, position, validated, ...rest } =
    template
  // TODO: output 没有数据，先取默认的数据显示
  const keys = Object.keys(output || {})
  if (keys.length === 1) {
    const k = keys[0]
    if (!(output || {})[k]?.desc) {
      // 没有值，搞个默认值
      output = { text: { desc: { format: 'plain', schema: { type: undefined } } } }
    }
  }

  let param = {
    ...rest,

    input: handleInputFromBack2Canvas(input),
    type: nodeType,
    output: handleOutputFromBack2Canvas(output),
    name: title
    // 确认 type  task? 前端不需要这个task，转换回来的时候写死
  }
  if (ability) {
    let aid = ability.id
    if (aid) {
      if (aid.startsWith('cbm_')) {
        aid = aid.substring(4)
      }
    }

    let acategory = ability.category
    if (acategory) {
      if (acategory.startsWith('cbm_')) {
        acategory = acategory.substring(4)
      }
    } else {
      acategory = aid
    }

    param = {
      ...param,
      abilityId: aid,
      abilityCategory: acategory,
      abilityName: ability.name,
      abilityType: ability.type,
      abilityOriginId: ability.originId,
      // 后端到前端，将前端domain字段存放 `${ability.domain ?? ''}_${ability.patchId ?? ''}_${ability.modelType ?? ''}`

      code: ability.code
    }
  }
  return param
}

/**
 * 将前端data转成后端协议结构
 * @param {*} data
 */
const convertCodeTemplateFromFront2Back = function (data = {}, nodes) {
  const {
    abilityId,
    abilityName,
    abilityType,
    abilityOriginId,
    abilityCategory,
    code,

    input,
    output,
    name,
    // 前端字段，在这里去除
    validated,
    debug,
    ...rest
  } = data

  const handleInput = handleInputFromCanvas2Back(input, nodes)
  const handleOutput = handleOutputFromCanvas2Back(output)

  let newData = {
    ...rest,
    title: name,
    type: 'task',
    output: handleOutput,
    input: handleInput,
    ability: {
      id: abilityId ? `cbm_${abilityId}` : '',
      category: abilityCategory ? `cbm_${abilityCategory}` : abilityId ? `cbm_${abilityId}` : '',
      originId: abilityOriginId,
      // ability.name 接收页面 的title
      name,
      type: abilityType || 'LLM',

      code
    }
  }

  return newData
}

export class CodeNodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    param = {
      ...param,
      data: convertCodeTemplateFromBack2Front(nd)
    }
    return param
  }

  static fromFrontToBack(nd, nodes) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    const newData = convertCodeTemplateFromFront2Back(data, nodes)
    param = {
      ...newData,
      ...param
    }
    return param
  }
}
