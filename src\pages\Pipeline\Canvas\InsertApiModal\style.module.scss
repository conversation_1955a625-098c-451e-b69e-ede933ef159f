.create-tool-tab-normal {
  width: 100%;
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #757575;
  border-radius: 10px;
  cursor: pointer;
  &.create-tool-tab-active, &:hover {
    background: #fff;
    color: var(--color-primary);
  }
}


.customModal {
  /* 你的样式 */
  // padding: 0;
  :global {
    .ant-modal-content {
      padding: 0;
      .ant-modal-body {
        border-radius: 8px;
      }
    }
  }
  // :global {
  //   .ant-table-body {
  //     overflow-x: hidden;
  //   }
  // }
}

.paramWrap {
  li + li {
    margin-top: 10px;
  }
}
