<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_4218_33011)">
<rect width="26" height="26" rx="8" fill="#4575FF"/>
<rect width="26" height="26" rx="8" fill="url(#paint0_radial_4218_33011)" fill-opacity="0.5"/>
<g clip-path="url(#clip0_4218_33011)">
<path d="M6.33325 13.0002C6.33325 16.6822 9.31792 19.6668 12.9999 19.6668C16.6819 19.6668 19.6666 16.6822 19.6666 13.0002C19.6666 9.31816 16.6819 6.3335 12.9999 6.3335C9.31792 6.3335 6.33325 9.31816 6.33325 13.0002Z" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.3332 14.5C14.3332 14.776 14.5572 15 14.8332 15H15.6665C15.8433 15 16.0129 14.9298 16.1379 14.8047C16.2629 14.6797 16.3332 14.5101 16.3332 14.3333V13.6667C16.3332 13.4899 16.2629 13.3203 16.1379 13.1953C16.0129 13.0702 15.8433 13 15.6665 13H14.9998C14.823 13 14.6535 12.9298 14.5284 12.8047C14.4034 12.6797 14.3332 12.5101 14.3332 12.3333V11.6667C14.3332 11.4899 14.4034 11.3203 14.5284 11.1953C14.6535 11.0702 14.823 11 14.9998 11H15.8332C15.9658 11 16.093 11.0527 16.1867 11.1464C16.2805 11.2402 16.3332 11.3674 16.3332 11.5M9.6665 11L10.9998 15L12.3332 11" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_i_4218_33011" x="-1" y="-2" width="27" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_4218_33011"/>
</filter>
<radialGradient id="paint0_radial_4218_33011" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8.31148 5.11475) rotate(55.9679) scale(19.8013 19.8013)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_4218_33011">
<rect width="1em" height="1em" fill="white" transform="translate(5 5)"/>
</clipPath>
</defs>
</svg>
