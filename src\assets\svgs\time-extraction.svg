<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_4540_4957)">
<rect width="26" height="26" rx="8" fill="#4575FF"/>
<rect width="26" height="26" rx="8" fill="url(#paint0_radial_4540_4957)" fill-opacity="0.5"/>
<path d="M13.3334 19H9.00008C8.64646 19 8.30732 18.8595 8.05727 18.6095C7.80722 18.3594 7.66675 18.0203 7.66675 17.6667V9.66667C7.66675 9.31304 7.80722 8.97391 8.05727 8.72386C8.30732 8.47381 8.64646 8.33333 9.00008 8.33333H17.0001C17.3537 8.33333 17.6928 8.47381 17.9429 8.72386C18.1929 8.97391 18.3334 9.31304 18.3334 9.66667V13M15.6667 7V9.66667M10.3334 7V9.66667M7.66675 12.3333H18.3334M17.6667 19.6667V15.6667M17.6667 15.6667L19.6667 17.6667M17.6667 15.6667L15.6667 17.6667" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_i_4540_4957" x="-1" y="-2" width="27" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_4540_4957"/>
</filter>
<radialGradient id="paint0_radial_4540_4957" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8.31148 5.11475) rotate(55.9679) scale(19.8013 19.8013)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
