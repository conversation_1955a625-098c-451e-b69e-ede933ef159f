import { Alert, Form, Input, message, Modal, Space } from 'antd'
import ajax from '@/utils/http'
import { useUserStore } from '@/store/user'
import { APP_ENV, cssVariables, baseFooterStyles, autoFooterStyles } from '@/utils/constant'
import { useNavigate } from 'react-router-dom'
import { validateTrim } from '@/utils/validateTrim'
import IconArrowRight from '@/assets/svgs/arrow-right.svg?react'
import IconFlow from '@/assets/svgs/icon-flow.svg?react'

import React, { useEffect, useRef, useState } from 'react'

function AddModal({ isModalOpen, handleCancel }) {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const limitCount = useUserStore((state) => state.limitCount)
  const [createLoading, setCreateLoading] = useState(false)

  const handleOk = () => {
    form.validateFields().then((values) => {
      saveData(values)
    })
  }
  const saveData = async ({ name, description, flowId }) => {
    setCreateLoading(true)
    let param = {
      name,
      description
    }
    if (flowId) {
      param.flowId = `workflow_${flowId}`
    }

    try {
      const result = await ajax({
        url: '/workflow/save',
        method: 'post',
        data: param
      })

      if (result.data?.code === '0') {
        handleCancel()
        //
        if (APP_ENV === 'auto') {
          navigate(`/${result.data?.data?.id}`)
        } else {
          // console.log('location', location)
          // /sparkos/flow/
          // /sparkos/workspace/flow
          const pathname = window.location.pathname
          console.log('pathname is', pathname)
          if (pathname.includes('workspace')) {
            // navigate(`/flow/${item.id}`, '_blank')
            window.location.href = `${import.meta.env.VITE_ROUTER_BASE_URL}/flow/${
              result.data?.data?.id
            }`
          } else {
            navigate(`/${result.data?.data?.id}`)
          }
        }
      }
    } catch (e) {
    } finally {
      setCreateLoading(false)
    }
  }
  return (
    <Modal
      title={
        APP_ENV === 'base' ? (
          <Space size={4}>
            <IconFlow /> {'创建工作流'}
          </Space>
        ) : (
          '创建工作流'
        )
      }
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnClose={true}
      confirmLoading={createLoading}
      cancelButtonProps={{ style: { display: 'none' } }}
      okText="创建"
      okButtonProps={
        APP_ENV === 'base'
          ? {
              icon: <IconArrowRight style={{ transform: 'translateY(2px)' }} />,
              iconPosition: 'end'
            }
          : {
              type: 'primary',
              size: 'middle',
              children: '确定'
            }
      }
      styles={APP_ENV === 'auto' ? autoFooterStyles : baseFooterStyles}
    >
      <Form
        form={form}
        name="basic"
        layout="vertical"
        initialValues={{}}
        autoComplete="off"
        preserve={false}
      >
        <Form.Item
          label="名称"
          name="name"
          rules={[
            {
              required: true,
              message: '请输入工作流名称'
            },
            {
              pattern: /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/,
              message: '只支持中文/英文/数字/下划线格式'
            }
          ]}
        >
          <Input placeholder="请输入工作流名称" showCount maxLength={32} />
        </Form.Item>
        {limitCount && String(limitCount.flow_id_edit) === '1' && (
          <Form.Item
            label="工作流ID"
            name="flowId"
            rules={[
              {
                required: true,
                message: '请输入工作流ID'
              },
              {
                pattern: /^[A-Za-z0-9_]+$/,
                message: '只能输入英文字母、数字、下划线'
              }
            ]}
          >
            <Input
              placeholder="请输入工作流ID"
              showCount
              maxLength={40}
              addonBefore={'workflow_'}
            />
          </Form.Item>
        )}
        <Form.Item
          label="描述"
          name="description"
          rules={[{ required: true, message: '请输入工作流描述' }, { validator: validateTrim }]}
        >
          <Input.TextArea placeholder="请输入工作流描述" showCount maxLength={200} />
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default AddModal
