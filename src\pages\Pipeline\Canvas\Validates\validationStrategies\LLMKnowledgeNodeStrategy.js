import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import { validateNodeWithRules } from './validateNodeWithRules'

export const LLMKnowledgeNodeStrategy = async (node, nodes, edges) => {
  // 大模型节点需要检查是否为官方（模版），官方的不需要校验 某些字段（prompt， systemPrompt没有）
  // const isOfficial = !!node?.data?.abilityOriginId
  // const rule = getRules(NODETYPES.KNOWLEDGE, { isOfficial })
  const rule = getRules(NODETYPES.KNOWLEDGE)
  const errors = await validateNodeWithRules(node, rule, nodes, edges)
  return errors
}
