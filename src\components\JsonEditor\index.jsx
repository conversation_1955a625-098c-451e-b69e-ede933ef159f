import { EditorState } from '@codemirror/state'
import { linter } from '@codemirror/lint'
import { json, jsonParseLinter, jsonLanguage } from '@codemirror/lang-json'

import { minimalSetup } from 'codemirror'
import { EditorView } from '@codemirror/view'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { But<PERSON>, Tooltip } from 'antd'
import styles from './style.module.scss'
import { ClearOutlined } from '@ant-design/icons'

const JsonEditor = ({ value, readOnly = false, onChange, showFormat = false }) => {
  const editorRef = useRef(null)

  const viewRef = useRef(null)

  // JSON 格式化工具函数
  const formatJson = (jsonString) => {
    try {
      const parsed = JSON.parse(jsonString)
      return JSON.stringify(parsed, null, 2) // 格式化为缩进为2的漂亮 JSON
    } catch (e) {
      console.warn('Invalid JSON:', e)
      return jsonString // 如果不是有效的 JSON，返回原字符串
    }
  }

  // 格式化编辑器内容的方法
  const handleFormat = useCallback(() => {
    if (viewRef.current) {
      const currentValue = viewRef.current.state.doc.toString()
      const formattedValue = formatJson(currentValue) // 格式化 JSON
      const transaction = viewRef.current.state.update({
        changes: { from: 0, to: viewRef.current.state.doc.length, insert: formattedValue }
      })
      viewRef.current.dispatch(transaction)
    }
  }, [])

  const updateListener = EditorView.updateListener.of((update) => {
    // if (update.changes || update.docChanged) {
    //   const newValue = update.state.doc.toString()
    //   try {
    //     // 验证 JSON 是否有效
    //     JSON.parse(newValue)

    //     if (onChange) {
    //       onChange(JSON.stringify(JSON.parse(newValue)))
    //     }
    //   } catch (e) {
    //     // JSON 无效时可以选择不调用 onChange
    //     // console.warn('Invalid JSON:', e);
    //   }
    // }
    if (update.docChanged) {
      console.log('Change detected')
      const content = update.state.doc.toString()
      // onChange && onChange(content)
    }
  })

  const extensions = [
    minimalSetup,
    json(), // JSON 语法支持

    linter(jsonParseLinter(), {
      delay: 300
    }),

    readOnly ? EditorView.editable.of(false) : null, // 添加只读模式
    updateListener
  ].filter(Boolean) // 移除任何可能为 null 的扩展,

  useEffect(() => {
    if (!editorRef.current) return

    const view = new EditorView({
      doc: String(value),
      parent: editorRef.current,
      extensions
    })

    viewRef.current = view

    return () => {
      view.destroy()
    }
  }, [])

  // useEffect(() => {
  //   if (!editorRef.current) return

  //   const formattedValue = formatJson(value) // 格式化传入的 JSON 字符串

  //   const state = EditorState.create({
  //     doc: formattedValue || '',
  //     extensions: [
  //       minimalSetup,
  //       json(), // JSON 语法支持

  //       linter(jsonParseLinter(), {
  //         delay: 300
  //       }),

  //       readOnly ? EditorView.editable.of(false) : null, // 添加只读模式
  //       EditorView.updateListener.of((update) => {
  //         if (update.changes || update.docChanged) {
  //           const newValue = update.state.doc.toString()
  //           try {
  //             // 验证 JSON 是否有效
  //             JSON.parse(newValue)

  //             if (onChange) {
  //               onChange(JSON.stringify(JSON.parse(newValue)))
  //             }
  //           } catch (e) {
  //             // JSON 无效时可以选择不调用 onChange
  //             // console.warn('Invalid JSON:', e);
  //           }
  //         }
  //       })
  //     ].filter(Boolean) // 移除任何可能为 null 的扩展,
  //   })

  //   viewRef.current = new EditorView({ state, parent: editorRef.current })
  //   return () => {
  //     viewRef.current && viewRef.current.destroy() // 清理资源
  //   }
  // }, [value])
  useEffect(() => {
    const view = viewRef.current
    if (view && value !== view.state.doc.toString()) {
      view.dispatch({
        changes: { from: 0, to: view.state.doc.length, insert: value }
      })
    }
  }, [value])

  const onEditorBlur = (e) => {
    const view = viewRef.current
    if (view) {
      onChange?.(view.state.doc.toString())
    }
    // setTimeout(()=>{setPopoverOpen(false)})
  }

  return (
    <>
      <div className={`${styles.editor}`}>
        <div className={`${styles.editorHeader}`}>
          <div>JSON</div>
          <Tooltip title="格式化">
            <ClearOutlined className="text-[var(--flow-desc-color)]" onClick={handleFormat} />
          </Tooltip>
        </div>
        <div ref={editorRef} onBlur={onEditorBlur} className={`${styles.editorBody}`}></div>
      </div>
    </>
  )
}

export default JsonEditor
