.debug {
  position: absolute;
  z-index: 1;
  top: calc(100% + 20px);
  width: calc(100% + 100px);
  left: -15px;
  padding: 10px;
  border: 1px solid #f5f6fb;
  border-radius: 14px;
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
  background-color: #fff;
  .debugTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .debugTitleLeft {
      display: flex;
      align-items: center;
    }
    .debugTitleRight {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding-right: 10px;
    }
  }
  .debugContent {
    border-radius: 4px;
    font-size: 12px;
    line-height: 22px;

    margin-top: 4px;
    max-height: 200px;
    overflow: auto;
    .debugCell {
      user-select: text;
      cursor: text;
      padding: 6px;
      background: #f5f6fb;
      word-break: break-word;
    }
    .debugCell + .debugCell {
      margin-top: 6px;
    }
  }
}

.label {
  font-size: 13px;
  margin-top: 10px;
  color: var(--flow-desc-color);
  display: flex;
  align-items: center;
}
