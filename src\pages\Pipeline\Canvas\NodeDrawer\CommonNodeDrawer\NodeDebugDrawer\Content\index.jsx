import React, { useState } from 'react'
import { Collapse, Form, Input, Button, message, Space, Empty } from 'antd'
import { useEffect } from 'react'
import { useNodesData } from '@xyflow/react'
import useInput from '@/hooks/useInput'
import genUUID from '@/utils/genUUID'
import handleLoginExpire from '@/utils/handleLoginExpire'
import MarkdownIt from 'markdown-it'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { useNodes, useReactFlow } from '@xyflow/react'
import styles from './style.module.scss'
import { DATA_TYPES_MAP, EXIT_SOURCE_ID } from '@/utils/constant'
import IconDebug from 'assets/svgs/debug.svg?react'
import eventBus from '@/utils/eventBus'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import KeyProp from '@/components/KeyProp'
import CustomEmpty from '@/components/CustomEmpty'

const md = new MarkdownIt({ breaks: true })

const Content = (props) => {
  const {
    node: { id, type, data },
    flowId
  } = props
  const nodeData = useNodesData(id)
  const originInput = useInput(nodeData.data?.input)

  // 只接收引用的变量测试，自输入变量值默认附带
  // const input = (originInput || []).filter((item) => item.value?.ref === 'ref')
  const input = originInput || []
  console.log('------------------------origin  input is ------', input)

  const [form] = Form.useForm()
  const [isReplying, setIsReplying] = useState(false)
  const [chatList, setChatList] = useState([])
  const { updateNodeData } = useReactFlow()
  const nodes = useNodes()

  useEffect(() => {
    form &&
      form.setFieldsValue({
        params: input.map((item) => ({
          [item.key]: item.value.ref === 'const' ? item.value.path : ''
        }))
      })
  }, [form, originInput])

  const handleMessage = (data) => {
    if (data.finish || data.nlpFinish) {
      setIsReplying(false)
    }
    setChatList((list) => {
      const index = list.findIndex((item) => item.logId === data.logId)
      if (index === -1) {
        return list.concat([data])
      } else {
        return list.map((item) => {
          return {
            ...item,
            text: item.logId === data.logId ? `${item.text || ''}${data.text || ''}` : item.text
          }
        })
      }
    })
    const targetNode = nodes.find((nd) => nd.id === id)
    if (targetNode) {
      eventBus.emit('DEBUG_DATA', { id: targetNode.id, debugData: data })
    }
  }

  const fetchDebugOutput = (inputData) => {
    // 这里替换为实际的接口调用

    const baseUrl = `${import.meta.env.VITE_API_BASE_URL}/user/chat`
    let formData = new FormData()
    formData.append('version', 'vflowtest')
    formData.append('expUid', genUUID())
    formData.append('flowId', flowId)
    formData.append('nodeId', id)
    formData.append('input', JSON.stringify(inputData))
    let headers = {}
    if (window.microApp?.getData()?.cookie) {
      headers['X-Auto-Token'] = window.microApp?.getData()?.cookie
    }
    fetchEventSource(baseUrl, {
      method: 'POST',
      openWhenHidden: true,
      body: formData,
      headers,
      async onopen(response) {
        if (response.ok) {
          console.log('连接了')
          setIsReplying(true)
        } else if (response.status === 401) {
          console.error('体验请求过期！！！！！！！！！！！！')
          handleLoginExpire()
        }
      },

      onmessage(event) {
        try {
          const result = JSON.parse(event.data || '{}')
          console.log(result)
          // 处理每一条信息
          if (result.code == '300001') {
            handleLoginExpire()
            setIsReplying(false)
          } else if (result.code == '0') {
            const data = result.data
            handleMessage(data)
          } else if (result.code == '300017') {
            // let targetNode
            // if (result.data?.error?.id === EXIT_SOURCE_ID) {
            //   targetNode = nodes.find((nd) => nd.id === result.data?.error?.id)
            // } else {
            //   targetNode = nodes.find(
            //     (nd) => `cbm_${nd.data?.abilityId}` === result.data?.error?.id
            //   )
            // }
            let targetNode = nodes.find(
              (nd) => `cbm_${nd.data?.abilityId}` === result.data?.error?.id
            )
            if (!targetNode) {
              targetNode = nodes.find((nd) => nd.id === result.data?.error?.id)
            }

            if (targetNode) {
              eventBus.emit('DEBUG_DATA', {
                id: targetNode.id,
                debugData: { nodeStatus: 'error', logId: 'error', text: result.data?.error?.msg }
              })
            }

            setIsReplying(false)
          } else {
            message.error(result.desc || '未知错误')
            // setChatList((chatList) => chatList.filter((it) => it.type === 'loading'))
            setIsReplying(false)
          }
        } catch (e) {
          console.log(e)
          setIsReplying(false)
        }
      },
      onclose() {
        console.info('断开了')
      },
      onerror(err) {
        // console.info('报错了')
        // throw new Error(err)
        console.log(err)
        setIsReplying(false)
        throw err
      }
    })
  }

  const transformArrayToObject = (arr) => {
    const result = {}

    arr.forEach((item) => {
      const key = item.key
      const value = item.value.path
      result[key] = value
    })

    return result
  }

  //   // Input data
  // const keyProps = [
  //   {
  //       "keyProp": {
  //           "name": "source",
  //           "location": "query",
  //           "type": "string",
  //           "required": true
  //       },
  //       "value": {
  //           "path": "moji",
  //           "ref": "const"
  //       },
  //       "key": "source",
  //       "type": "string"
  //   },
  //   {
  //       "keyProp": {
  //           "name": "city",
  //           "location": "query",
  //           "type": "string",
  //           "required": true
  //       },
  //       "value": {
  //           "path": "llm.nlp.slot1",
  //           "ref": "ref"
  //       },
  //       "key": "city",
  //       "type": "string"
  //   }
  // ];

  // const inputData = [
  //   { "source": "moji" },
  //   { "city": "11" }
  // ];

  // // Process the data
  // const result = processData(keyProps, inputData);
  // console.log(result);

  const processData = (keyProps, inputData) => {
    // Create a map for quick lookup of key properties
    const keyPropMap = {}
    keyProps.forEach((item) => {
      keyPropMap[item.key] = item.keyProp
    })

    // Process each item in the input data
    return inputData.map((item) => {
      const key = Object.keys(item)[0]
      const value = item[key]

      // Get the location from keyProp
      const keyProp = keyPropMap[key]
      if (!keyProp) return item // if no keyProp found, return as-is

      let newKey = `${key}`
      if (keyProp.location) {
        newKey = `${keyProp.location}#${key}`
      }
      return { [newKey]: value }
    })
  }

  const handleDebugStart = () => {
    // 先校验
    form.validateFields().then(() => {
      setChatList([])
      const targetNode = nodes.find((nd) => nd.id === id)
      if (targetNode) {
        eventBus.emit('DEBUG_DATA', { id: targetNode.id, debugData: null })
      }
      try {
        const inputData = ((form.getFieldsValue() || {}).params || []).map(({ ...rest }) => {
          return {
            ...rest
          }
        })

        const newInputData = processData(input, inputData)

        fetchDebugOutput(Object.assign({}, ...newInputData))
      } catch (error) {
        message.error('调试失败，请重试')
      } finally {
      }
    })
  }

  // Collapse 的 items 配置
  const collapseItems = [
    {
      key: '1',
      label: '调试输入',
      children: (
        <Form form={form} layout="vertical" requiredMark={false}>
          <Form.List name="params">
            {(fields) => {
              return (
                <>
                  {fields.map(({ key, name, ...restField }) => {
                    return (
                      <Form.Item
                        {...restField}
                        name={[name, input[name].key]}
                        label={
                          <>
                            <KeyProp
                              value={{
                                type: input[name].type,
                                name: input[name].key,
                                required: input[name].keyProp?.required
                              }}
                            />
                          </>
                        }
                        rules={
                          input[name].keyProp?.required
                            ? [{ required: true, message: `请输入${input[name].key}` }]
                            : []
                        }
                      >
                        <Input.TextArea
                          placeholder={`请输入${input[name].key}参数`}
                          rows={3}
                          style={{ width: '100%' }}
                          maxLength={1000}
                        />
                      </Form.Item>
                    )
                  })}
                </>
              )
            }}
          </Form.List>
        </Form>
      )
    },
    {
      key: '2',
      label: '调试输出',
      children:
        chatList.length > 0 ? (
          <div className={`${styles.debugContent}`}>
            {chatList.map((item) => {
              return (
                <div
                  className={styles.debugCell}
                  key={item.logId}
                  dangerouslySetInnerHTML={{ __html: md.render(item.text || '') }}
                ></div>
              )
            })}
          </div>
        ) : (
          <CustomEmpty description={<span>暂无输出</span>} />
        )
    }
  ]

  return (
    <>
      <Collapse
        items={collapseItems}
        defaultActiveKey={['1', '2']}
        ghost
        size="small"
        className="flex-1 pr-[20px] mr-[-24px] overflow-auto max-h-[calc(100vh-280px)]"
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
      />
      <div>
        <Button
          onClick={handleDebugStart}
          loading={isReplying}
          icon={<IconDebug />}
          style={{ marginTop: 16 }}
        >
          调试
        </Button>
      </div>
    </>
  )
}

export default Content
