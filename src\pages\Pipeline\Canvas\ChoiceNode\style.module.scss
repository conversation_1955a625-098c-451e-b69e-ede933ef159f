.choiceContainer {
  position: relative;
  width: 274px;
  margin-top: 8px;
  font-size: 12px;
  .choice {
    display: flex;
    align-items: center;
    position: relative;
    background: #f5f6fb;
    border-radius: 4px;
    padding: 0 10px;
    .label {
      // width: 70px;
      // min-width: 40px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--flow-content-color);
    }
    .rules {
      min-height: 30px;
      flex: 1;
      // border: 1px solid #eaecf0;
      // background: #f5f6fb;
      .rule {
        .ruleDetail {
          display: flex;
          align-items: center;
          padding: 6px 4px;
          justify-content: space-between;
          >div {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: var(--flow-desc-color);
          }
          .leftVal {
            // flex: 1;
            max-width: 120px;
            background: #ffffff;
            border-radius: 4px;
            text-align: left;
            font-size: 12px;
            padding: 0 4px;
            
          }
          .leftValWarn {
            max-width: 120px;
            background: rgba(255,188,133,0.2);
            color: rgba(255,161,84,1);
            border-radius: 4px;
            text-align: left;
            font-size: 12px;
            padding: 0 4px;
          }
          .symbol {
            // max-width: 100px;
            display: flex;
            // align-items: center;
            background: #ffffff;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
            padding: 0 4px;
          }
          .rightVal {
            // flex: 1;
            max-width: 50px;
            background: #ffffff;
            border-radius: 4px;
            text-align: left;
            font-size: 12px;
            padding: 0 4px;
          }
        }
      }
    }
  }
  .choice + .choice {
    margin-top: 2px;
  }

  .divider {
    border-color: #eaecf0;
    margin: 0;
    font-size: 12px;
    line-height: 12px;
    color: var(--flow-desc-color);
  }
}
