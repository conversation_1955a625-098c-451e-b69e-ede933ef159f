import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'

import { MarkerType, Position, getOutgoers } from '@xyflow/react'

// 1、后端给定的 大模型 节点模板
const passTemplate = {
  title: '转换器',
  description: '转换器',
  nodeType: 'pass',
  input: {
    text: {
      ref: '',
      path: ''
    }
  },
  output: { nlp: { desc: { format: 'plain', schema: { type: 'string' } } } }
}

/**
 * 将选择器后端模板转成前端data
 * @param {*} template
 */
const convertPassTemplateFromBack2Front = function (template = passTemplate) {
  let { input, nodeType, output, title, type, position, validated, ...rest } = template
  // TODO: output 没有数据，先取默认的数据显示
  const keys = Object.keys(output || {})
  if (keys.length === 1) {
    const k = keys[0]
    if (!(output || {})[k]?.desc) {
      // 没有值，搞个默认值
      output = { nlp: { desc: { format: 'plain', schema: { type: 'string' } } } }
    }
  }

  let param = {
    ...rest,

    input: handleInputFromBack2Canvas(input),
    type: nodeType,
    output: handleOutputFromBack2Canvas(output),
    name: title
    // 确认 type  task? 前端不需要这个task，转换回来的时候写死
  }

  return param
}

/**
 * 将前端data转成后端协议结构
 * @param {*} data
 */
const convertPassTemplateFromFront2Back = function (data = {}) {
  const newData = {}
  return newData
}

export class PassNodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    param = {
      ...param,
      data: {
        name: nd.title,
        description: nd.description,
        input: handleInputFromBack2Canvas(nd.input),
        output: handleOutputFromBack2Canvas(nd.output)
      }
    }
    return param
  }

  static fromFrontToBack(nd) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    param = {
      ...param,
      title: data?.name,
      description: data?.description,
      type: nd.type,
      input: handleInputFromCanvas2Back(data?.input),
      output: handleOutputFromCanvas2Back(data?.output)
    }
    return param
  }
}
