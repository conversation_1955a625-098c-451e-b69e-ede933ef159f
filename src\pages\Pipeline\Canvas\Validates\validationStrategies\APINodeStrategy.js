import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import Schema from 'async-validator'
import { getTreeData } from '@/hooks/getTreeData'
import { validateNameRecursive } from './validateNameRecursive'
import useInputDataValidate from '@/hooks/useInputDataValidate'

const validateNodeWithRules = async (node, rules, nodes, edges, isOfficial) => {
  const treeData = getTreeData(node, nodes, edges)
  const { validateInputData } = useInputDataValidate(treeData)

  const inputKeyValidate = (node) => {
    // 校验该变量是否被 提示词所使用

    const validateInputKey = (rule, value) => {
      // 官方大模型能力，没有提示词，不需要校验该项
      
      return Promise.resolve()
    }
    return validateInputKey
  }

  const validateInputKey = inputKeyValidate(node)

  let errors = []
  for (const field in rules) {
    // 针对 field  是否是output等特殊类型
    if (field === 'output') {
      // 这里为方便处理，且考虑项目中实际，只对name进行校验
      for (const [index, data] of node.data?.[field]?.entries()) {
        const dataErrors = await validateNameRecursive(data, index, node.data?.[field])
        errors.push(...dataErrors)
      }
    } else {
      let rule
      if (field === 'input') {
        // 区分是不是官方
        if (isOfficial) {
          rule = rules['input'](validateInputData, validateInputKey)
        } else {
          // 自定义openAPI
          rule = rules['input'](validateInputData)
        }
      } else {
        rule = rules[field]
      }

      const validator = new Schema({ [field]: rule })
      try {
        await validator.validate({ [field]: node.data?.[field] })
      } catch ({ errors: validationErrors }) {
        if (validationErrors) {
          errors.push(...validationErrors)
        }
      }
    }
  }
  return errors
}

export const APINodeStrategy = async (node, nodes, edges) => {
  // 大模型节点需要检查是否为官方（模版），官方的不需要校验 某些字段（prompt， systemPrompt没有）
  const isOfficial = node?.data?.api_type !== 'openapi'
  const rule = getRules(NODETYPES.API, { isOfficial, api_type: node.data?.api_type })
  const errors = await validateNodeWithRules(node, rule, nodes, edges, isOfficial)
  return errors
}
