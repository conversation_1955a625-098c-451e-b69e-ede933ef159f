import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'node:url';
// 获取当前脚本文件的路径
console.log('import.meta.url', import.meta.url)

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const distPath = path.resolve(__dirname, '../dist/assets')
const files = fs.readdirSync(distPath)
const remoteEntryFile = files.find(f => f.startsWith('remoteEntry-') && f.endsWith('.js'))

if (remoteEntryFile) {
  fs.writeFileSync(
    path.join(distPath, 'manifest.json'),
    JSON.stringify({ remoteEntry: remoteEntryFile }, null, 2)
  )
  console.log(`✅ manifest.json generated: ${remoteEntryFile}`)
} else {
  console.error('❌ remoteEntry.js not found!')
}
