import { NODETYPES } from '@/utils/constant'
import { getRules } from '@/utils/rule'
import Schema from 'async-validator'
import { validateNameRecursive } from './validateNameRecursive'

const validateNodeWithRules = async (node, rules) => {
  let errors = []
  for (const field in rules) {
    // 针对 field  是否是output等特殊类型
    if (field === 'input') {
      // 这里为方便处理，且考虑项目中实际，只对name进行校验
      for (const [index, data] of node.data?.[field]?.entries()) {
        const dataErrors = await validateNameRecursive(data, index, node.data?.[field])
        errors.push(...dataErrors)
      }
    } else {
      const rule = rules[field]
      const validator = new Schema({ [field]: rule })
      try {
        await validator.validate({ [field]: node.data?.[field] })
      } catch ({ errors: validationErrors }) {
        if (validationErrors) {
          errors.push(...validationErrors)
        }
      }
    }
  }
  return errors
}

export const EntryNodeStrategy = async (node) => {
  const rule = getRules(NODETYPES.ENTRY)
  const errors = await validateNodeWithRules(node, rule)
  return errors
}
