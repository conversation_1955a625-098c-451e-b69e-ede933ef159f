import { EditOutlined } from '@ant-design/icons'
import { Divider, Form, Input, InputNumber, Select, Tooltip } from 'antd'
import { useCallback, useRef, useState } from 'react'
import { useReactFlow } from '@xyflow/react'
import useStore from '@/store.js'
import { useEffect } from 'react'
import { useN<PERSON>, Handle, useNodesData } from '@xyflow/react'
import styles from './style.module.scss'
import { SYMBOL_OPTIONS } from '@/utils/constant'
import { getSymbolIcon } from '@/components/SymbolIcon'
import useRefTreeData from '@/hooks/useRefTreeData'
import useElements from '@/hooks/useElements'
import DebugData from '../Common/DebugData'


function ChoiceForm(props) {
  const { id, data, sourcePosition, isConnectable } = props

  const nodeData = useNodesData(id)
  const elements = useElements()

  const treeData = useRefTreeData(nodeData, elements, true)

  const nodes = useNodes()

  const isExisitInData = (value, data) => {
    if (!data) {
      return true
    }
    for (const node of data) {
      if (node.key === value) return true
      if (node.children) {
        const found = isExisitInData(value, node.children)
        if (found) return true
      }
    }
    return false
  }

  const choiceDom = (data?.choice || []).map((item, index) => {
    const labelTitle = index === 0 ? '如果' : '否则如果'
    return (
      <li className={styles.choice}>
        <div
          className={styles.label}
          title={labelTitle}
          style={{ minWidth: (data?.choice || []).length > 1 ? 52 : 32 }}
        >
          {labelTitle}
        </div>
        <ul className={styles.rules}>
          {(item.rule || []).map((r, rIndex) => {
            let display = ''
            let nodeName = ''
            let variableName = ''
            let symbolName = SYMBOL_OPTIONS.find((it) => it.value === r.func)?.label || r.func

            // 引用变量是否存在
            let isExist = true
            if (r && r.variable && typeof r.variable === 'string') {
              isExist = isExisitInData(r.variable, treeData)
              nodeName = nodes.find((n) => n.id === r.variable.split('.')[0])?.data?.name
              variableName = `${r.variable.split('.').slice(1).join('.')}`
              display = nodeName && variableName ? `${nodeName}.${variableName}` : ''
            } else {
            }
            const typeName = item.type === 'is' ? '且' : item.type === 'or' ? '或' : '-'
            return (
              <li className={styles.rule}>
                <div className={styles.ruleDetail}>
                  <Tooltip title={display}>
                    {isExist ? (
                      <div className={styles.leftVal}>{display}</div>
                    ) : (
                      <div className={styles.leftValWarn}>{display}</div>
                    )}
                  </Tooltip>
                  <Tooltip title={symbolName}>
                    <div className={styles.symbol}>{getSymbolIcon(r.func)}</div>
                  </Tooltip>
                  <Tooltip title={r.const}>
                    <div className={styles.rightVal}>{r.const}</div>
                  </Tooltip>
                </div>
                {rIndex !== (item.rule || []).length - 1 && (
                  <Divider className={styles.divider} plain>
                    {typeName}
                  </Divider>
                )}
              </li>
            )
          })}
        </ul>
        <Handle
          type="source"
          position={sourcePosition}
          className="handle mr-[-14px]"
          id={`source-if_${index}`}
          isConnectable={true}
        />
      </li>
    )
  })

  return (
    <div className={styles.choiceContainer}>
      <ul>
        {choiceDom}
        <li className={styles.choice}>
          <div className={styles.label}>否则</div>
          <ul className={styles.rules}></ul>
          <Handle
            type="source"
            position={sourcePosition}
            className="handle mr-[-14px]"
            id={`source-else`}
            isConnectable={true}
          />
        </li>
      </ul>
      <DebugData id={id} />
    </div>
  )
}
export default ChoiceForm
