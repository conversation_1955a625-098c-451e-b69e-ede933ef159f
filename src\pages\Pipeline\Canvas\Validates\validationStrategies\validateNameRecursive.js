import { outputRule } from '@/utils/rule'
import Schema from 'async-validator'

// 递归校验函数
export const validateNameRecursive = async (data, index, sameLevelVal) => {
  const errors = []

  // 对当前层级的 name 进行校验
  const validator = new Schema({
    name: [
      ...outputRule.name,
      {
        type: 'string',
        asyncValidator: (rule, value) => {
          return new Promise((resolve, reject) => {
            for (let i = 0; i < sameLevelVal.length; i++) {
              if (i === index) {
                continue
              }
              if (sameLevelVal[i]?.name === value) {
                reject('同层级变量名不可重复')
              }
            }
            resolve()
          })
        }
      }
    ]
  })
  try {
    await validator.validate({ name: data?.name })
  } catch (e) {
    errors.push(...e.errors)
  }

  // 如果有子节点，则递归对每个子节点进行校验
  if (data?.schema && Array.isArray(data?.schema)) {
    // const [index, data] of node.data?.[field]?.entries()
    for (const [index, child] of data?.schema?.entries()) {
      const childErrors = await validateNameRecursive(child, index, data?.schema)
      errors.push(...childErrors)
    }
  }

  return errors
}
