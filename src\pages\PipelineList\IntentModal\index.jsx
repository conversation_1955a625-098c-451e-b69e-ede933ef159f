import { useState, useEffect } from 'react'
import {
  Space,
  Table,
  Input,
  Button,
  Modal,
  message,
  Checkbox,
  Select,
  Pagination,
  Empty
} from 'antd'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import CustomEmpty from '@/components/CustomEmpty'
import { APP_ENV, autoFooterStyles, baseFooterStyles } from '@/utils/constant'

const { Option } = Select
const { Search } = Input

const IntentModal = ({ open, onCancel, flowId }) => {
  const [loading, setLoading] = useState(false)
  const [items, setItems] = useState([])

  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 1000
      // showSizeChanger: true
    },
    searchVal: ''
  })

  useEffect(() => {
    if (open) {
      fetchData()
    }
  }, [tableParams.pagination.current, tableParams.pagination.pageSize, tableParams.searchVal, open])

  const fetchData = () => {
    setLoading(true)

    ajax({
      url: '/workflow/getIntents',
      data: {
        pageSize: tableParams.pagination.pageSize,
        pageIndex: tableParams.pagination.current,
        id: flowId,
        search: tableParams.searchVal
      },
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        setItems(
          (res.data?.data?.data || []).map((item) => {
            const version = item.version ? item.version : item.versionList[0]?.version
            const versionObj = item.versionList.find((it) => it.version === version)
            return {
              ...item,
              version,

              intentName: versionObj.intentName,
              intentNameEn: versionObj.intentNameEn,
              intentDesc: versionObj.intentDesc,
              corpusCount: versionObj.corpusCount
            }
          })
        )
        setLoading(false)
        setTableParams((prev) => ({
          ...prev,
          pagination: {
            ...prev.pagination,
            current: res.data?.data?.pageIndex ?? 1,
            total: res.data?.data?.count ?? 0
          }
        }))
      }
    })
  }

  // 处理 Checkbox 变化
  const handleCheckboxChange = (intentId) => {
    setItems((prevItems) =>
      prevItems.map((item) => (item.intentId === intentId ? { ...item, quote: !item.quote } : item))
    )
  }

  const handleVersionChange = (intentId, val) => {
    setItems((prevItems) => {
      return prevItems.map((item) => {
        return item.intentId === intentId
          ? {
              ...item,
              version: val,
              corpusCount: item.versionList.find((it) => it.version === val)?.corpusCount
            }
          : item
      })
    })
  }

  // 处理搜索
  const onSearch = (value) => {
    setTableParams((prev) => ({
      ...prev,
      searchVal: value,
      pagination: { ...prev.pagination, current: 1 }
    }))
  }

  // 处理分页变化
  const handlePaginationChange = (page, pageSize) => {
    setTableParams((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, current: page, pageSize }
    }))
  }

  const handleOk = async () => {
    console.log('handleOk', items)
    let param = {
      id: flowId,
      intents: items.map((it) => {
        let obj = {
          intentId: it.intentId,
          operation: it.quote ? 'open' : 'close'
        }
        if (it.quote) {
          obj.version = it.version
        }
        return obj
      })
    }
    const result = await ajax({
      url: '/workflow/saveIntents',
      method: 'post',
      data: param
    })
    // console.log('新建结果', result)
    // setCreateLoading(false)

    if (result.data?.code === '0') {
      message.success('操作成功')
      onCancel?.()
    }
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Modal
      title="关联意图"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnClose
      width={762}
      cancelButtonProps={{ style: { display: 'none' } }}
      styles={APP_ENV === 'auto' ? autoFooterStyles : baseFooterStyles}
    >
      {/* <Search
        placeholder="搜索意图名称/描述"
        onSearch={onSearch}
        className={styles.search}
        allowClear
      /> */}

      {items.length > 0 ? (
        <>
          <div className={styles.container}>
            {items.map((item) => (
              <div key={item.intentId} className={styles.itemCard}>
                <Checkbox
                  checked={item.quote}
                  onChange={() => handleCheckboxChange(item.intentId)}
                  className={styles.checkbox}
                />
                <div className={styles.content}>
                  <div className={styles.title}>
                    {item.intentName} <span className={styles.subtitle}>{item.intentNameEn}</span>
                  </div>
                  <div className={styles.description} title={item.intentDesc}>
                    {item.intentDesc}
                  </div>
                </div>
                <Select
                  value={item.version}
                  className={styles.select}
                  style={{ width: 200 }}
                  placeholder="请选择版本"
                  onChange={(val) => handleVersionChange(item.intentId, val)}
                >
                  {item.versionList.map((it) => {
                    return (
                      <Option value={it.version}>
                        {it.version} <span>{it.remark}</span>
                      </Option>
                    )
                  })}
                </Select>

                {item.corpusCount && item.corpusCount > 0 ? (
                  <Button type="default" className={styles.button}>
                    {item.corpusCount}说法
                  </Button>
                ) : null}
              </div>
            ))}
          </div>
          {/* 分页器 */}

          <Pagination
            className={styles.pagination}
            current={tableParams.pagination.current}
            pageSize={tableParams.pagination.pageSize}
            total={tableParams.pagination.total}
            onChange={handlePaginationChange}
            hideOnSinglePage={true}
            // showSizeChanger
          />
        </>
      ) : (
        <CustomEmpty description={<span>暂无数据</span>} />
      )}
    </Modal>
  )
}

export default IntentModal
