import IconClose from 'assets/svgs/close.svg?react'
import styles from './style.module.scss'
import { useTimeStore } from '../store'
import { CheckCircleOutlined } from '@ant-design/icons'
import { Tag } from 'antd'

function Header(props) {
  const { onClose } = props
  const time = useTimeStore((state) => state.time)

  return (
    <>
      <div className={styles['drawer-header']}>
        <div className={styles['header-top']}>
          <div className={styles['top-left']}>
            <span className={`${styles['title']} mr-1`}>链路体验</span>
            {time && Number(time) > 0 ? (
              <>
                <CheckCircleOutlined className="text-[--flow-success-color] mr-1" />
                <Tag color="success" bordered={false}>
                  {time / 1000}s
                </Tag>
              </>
            ) : null}
          </div>
          <div className={styles['top-right']}>
            <div className={styles['button-left']}></div>
            <div className={styles['button-close']} onClick={onClose}>
              <div className={styles['icon-opt']}>
                <IconClose />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles['divider-big']}></div>
    </>
  )
}
export default Header
