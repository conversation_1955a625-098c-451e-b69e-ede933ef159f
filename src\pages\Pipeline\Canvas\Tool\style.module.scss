.pannel-wrapper {
  padding: 0 2px 0 10px;
  height: 36px;
  background: #ffffff;
  border: 1px solid #f2f4f7;
  border-radius: 6px;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  color: var(--flow-desc-color);
}
.zoom-number {
  cursor: pointer;
  display: block;
  width: 40px;
  text-align: center;
  font-size: 13px;
  height: 100%;
}

.vertical-divider {
  width: 1px;
  height: 14px;
  background: #eaecf0;
}

.button {
  height: 32px;
  line-height: 32px;
  text-align: center;
  // background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 0 15px;
  color: var(--flow-title-color2);
  display: flex;
  align-items: center;
  font-size: 12px;
  cursor: pointer;
  > span {
    margin-left: 8px;
  }
  &:hover {
    background: #f5f5ff;
    border: 1px solid #c7cdff;
    color: var(--sparkos-primary-color);
  }
  &.active {
    background: #dce3ff;
    border: 1px solid #c7cdff;
    color: var(--sparkos-primary-color);
  }
}

.node-plus {
  width: 16px;
  height: 16px;
  cursor: pointer;
  color: var(--flow-desc-color);
  &:hover {
    color: var(--sparkos-primary-color);
  }
}

:global {
  .sub-overlay {
    width: 420px !important;
    // max-height: 600px;
    overflow: auto;
    background: #ffffff;
    border: 1px solid #eaecf0;
    border-radius: 6px;
    box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
    .ant-popover-inner {
      padding: 0;
    }
  }
}

.inputContainer {
  padding: 16px 18px 12px 18px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.listContainer {
  // width: 100%;
  display: flex;
  flex-direction: column;
  // padding: 8px 8px 0 0;
  padding: 0 18px;
  max-height: 60vh;
  min-height: 380px;
  overflow: auto;
  // margin-right: -12px;
  padding-bottom: 10px;
}
.headTitle {
  display: flex;
  align-items: center;
  margin: 10px 0;
  line-height: 22px;
  > span {
    margin-left: 4px;
    font-size: 12px;
    color: #667085;
  }
}

.listWrap {
  display: grid;
  justify-content: space-between;
  grid-template-columns: repeat(2, calc(50% - 6px)); /* 每行放3个卡片，列宽均等 */
  grid-gap: 12px;
  padding: 0 1px;
}


.toggle-button {
  text-align: center;
  font-size: 12px;
  color: #98a2b2;
  cursor: pointer;
  margin-top: 10px;;
  // right: -50%;
  // transform: translateX(-50%);
  // margin-top: 10px;
}

.listItem {
  cursor: grab;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 20px;
  min-height: 36px;
  border-radius: 4px;
  background: linear-gradient(0deg, rgba(171, 104, 255, 0.02), rgba(41, 112, 255, 0.02)),
    linear-gradient(180deg, rgba(209, 233, 255, 0.1), rgba(255, 255, 255, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.3); /* 阴影 */
  &:hover {
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.08); /* 阴影 */
    .closeBtn {
      display: block;
    }
  }

  .tagOfficial {
    position: absolute;
    width: 42px;
    height: 15px;
    background: url(assets/images/bg-official.png) center/contain no-repeat;

    font-size: 9px;
    font-weight: normal;
    color: #275eff;
    line-height: 15px;
    top: -1px;
    right: -1px;
    padding-left: 16px;
  }
  .tagCustom {
    position: absolute;
    width: 42px;
    height: 15px;
    background: url(assets/images/bg-custom.png) center/contain no-repeat;
    font-size: 9px;
    font-weight: normal;
    color: #139453;
    line-height: 15px;
    top: -1px;
    right: -1px;
    padding-left: 16px;
  }

  .closeBtn {
    display: none;
    position: absolute;
    z-index: 2;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    background: #f5f6fb;
    border-radius: 4px;
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.08), 0px 0px 1px 0px rgba(0, 0, 0, 0.3);
    border-radius: 100%;
    color: var(--flow-content-color);
    cursor: pointer;
  }
}

// .listItem + .listItem {
//   margin-top: 6px;
// }

.leftContent {
  display: flex;
  align-items: center;
}

.itemTitle {
  font-size: 14px;
  color: var(--flow-content-color);
  max-width: 120px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.itemDesc {
  font-size: 12px;
  color: var(--flow-desc-color);
}

.tag {
  height: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  // line-height: 20px;
  text-align: center;
  font-size: 12px;
  color: #667085;
  margin-left: 6px;
  padding: 0 6px;
  display: inline-block;

  &.official {
    // background-color: #eef3fe;
    // color: #409eff;
  }

  &.custom {
    // background-color: #fef6e6;
    // color: #f39c12;
  }
}

.actionBtn {
  background-color: transparent;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #409eff;
  }

  &.activeBtn {
    color: #409eff;
    font-weight: bold;
  }
}

.minimap {
  position: absolute !important;
  bottom: 30px !important;
  pointer-events: all;
  left: -14px !important;
  right: unset !important;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}
