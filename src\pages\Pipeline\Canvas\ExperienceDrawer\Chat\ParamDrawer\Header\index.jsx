import IconClose from 'assets/svgs/close.svg?react'
import styles from './style.module.scss'

function Header(props) {
  const { onClose, node } = props

  return (
    <>
      <div className={styles['drawer-header']}>
        <div className={styles['header-top']}>
          <div className={styles['top-left']}>
            <span>入参配置</span>
          </div>
          <div className={styles['top-right']}>
            <div className={styles['button-left']}></div>
            <div className={styles['button-close']} onClick={onClose}>
              <div className={styles['icon-opt']}>
                <IconClose />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles['divider-big']}></div>
    </>
  )
}
export default Header
