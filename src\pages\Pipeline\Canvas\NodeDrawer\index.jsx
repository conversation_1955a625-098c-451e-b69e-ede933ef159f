import CommonNodeDrawer from './CommonNodeDrawer'

import { NODETYPES } from '@/utils/constant'
import { memo } from 'react'

function NodeDrawer({ node, ...rest }) {
  // console.log('nodeDrawer-----------', node.data.name)
  const type = node.type

  switch (type) {
    case NODETYPES.LLM:
    case NODETYPES.SEMANTIC:
    case NODETYPES.KNOWLEDGE:
    case NODETYPES.DENIAL:
    case NODETYPES.ARC:
    case NODETYPES.INTENT_DOMAIN:
    case NODETYPES.PK:
    case NODETYPES.FUNCTION:
    case NODETYPES.API:
    case NODETYPES.CODE:

    case NODETYPES.ENTRY:
    case NODETYPES.EXIT:

    case NODETYPES.CHOICE:
    case NODETYPES.PASS:
    case NODETYPES.FLOW:

    case NODETYPES.VARIABLE_SET:
    case NODETYPES.VARIABLE_GET:
      return <CommonNodeDrawer node={node} {...rest}></CommonNodeDrawer>

    default:
      return null
  }
}

export default memo(NodeDrawer)
