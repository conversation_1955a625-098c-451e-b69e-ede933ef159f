import IconApp from 'assets/svgs/app.svg?react'
import IconSkill from 'assets/svgs/skill.svg?react'
import IconArrow from 'assets/svgs/arrow.svg?react'
import { Avatar, Button, Dropdown, Layout, Menu, message, Space, theme } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { useUserStore } from '@/store/user'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import { SSOlogoutUrl } from '@/utils/getSSOUrl'
import React, { useState, useEffect } from 'react'

const { Header, Sider, Content } = Layout

function Headers() {
  const account = useUserStore((state) => state.account)
  const setAccount = useUserStore((state) => state.setAccount)
  const setLimitCount = useUserStore((state) => state.setLimitCount)
  useEffect(() => {
    ajax({
      url: '/auth/getUserInfo',
      baseURL: '/api/v1',
      data: {},
      method: 'get'
    }).then((res) => {
      setAccount(res.data.data)
    })

    ajax({
      url: '/user/getLimitCount',
      data: {},
      method: 'get'
    }).then((res) => {
      setLimitCount(res.data?.data)
    })
  }, [])

  const items = [
    {
      key: 'logout',
      label: <span>退出</span>
    }
  ]

  const onLogoutClick = ({ key }) => {
    ajax({
      url: '/auth/userLogout',
      baseURL: '/api/v1',
      data: {},
      method: 'get'
    }).then((res) => {
      console.log('logout res', res)
      message.success('退出成功')
      const isDomain = account.authType === 1
      if (isDomain) {
        window.location.href = SSOlogoutUrl + `${window.origin}/portal`
      } else {
        window.location.href = `${location.origin}/portal`
      }
    })
  }

  return (
    <Header className={styles.header}>
      <div className={styles.headerText}>交互开发定制平台</div>
      <Space size={26}>
        <Space size={4}>
          <IconSkill className="text-[16px]" style={{ fontSize: '32px' }} />
          <a
            className="cursor-pointer text-[var(--flow-title-color2)]"
            href="/console/aiui/"
            target="_blank"
          >
            技能工作室
          </a>
        </Space>
        <Space size={4}>
          <IconApp className="text-[16px]" />
          <a
            className="cursor-pointer text-[var(--flow-title-color2)]"
            href="/console/home/"
            target="_blank"
          >
            应用控制台
          </a>
        </Space>

        <Space size={5}>
          <Avatar icon={<UserOutlined />} size="small" />
          <Dropdown
            menu={{
              items,
              onClick: onLogoutClick
            }}
            placement="bottom"
          >
            <span className="cursor-pointer">{account?.accountName || ''}</span>
          </Dropdown>
          <IconArrow />
        </Space>
      </Space>
    </Header>
  )
}

export default Headers
