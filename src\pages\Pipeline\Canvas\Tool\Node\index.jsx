import { Input, Popover, Button, message, Empty, Tooltip } from 'antd'
import IconPlus from 'assets/svgs/plus2.svg?react'
import { SearchOutlined } from '@ant-design/icons'
import styles from '../style.module.scss'
import IconModel from 'assets/svgs/model-lite.svg?react'
import IconWorkflow from 'assets/svgs/workflow-lite.svg?react'
import { useEffect, useRef, useState } from 'react'
import { NODETYPES, nodesMap } from '@/utils/constant'
import eventBus from '@/utils/eventBus'
import { useTemplateStore } from '@/store/template'

import { NodeAdapterFactory } from '@/utils/adapter/NodeAdapterFactory'

import { useKnowledgeRepoStore } from '@/store/knowledgeRepo'

import { APP_ENV } from '@/utils/constant'
import { isReallyProduction } from '@/utils/getSSOUrl'
import getNodeIconComponent from '@/utils/getNodeIconComponent'

// const modelData = [
//   { title: '大模型', type: 'llm', description: '大模型' }
//   // { title: '工作流', type: 'flow', description: '工作流' }
// ]

const ListItem = (props) => {
  const { type, title } = props
  // const Component = type ? nodesMap[type].component : null
  const Component = getNodeIconComponent(type, props.ability?.originId || props.ability?.category)
  const template = useTemplateStore((state) => state.template)
  const { knowledgeRepos } = useKnowledgeRepoStore.getState()
  const onPlusClick = () => {
    // 仿照coze，将新节点插入在模板或者节点弹框的右侧
    const rect = props.buttonRef.current?.getBoundingClientRect()
    const position = {
      // 360为popover的宽度，20为取一个偏移值
      x: rect.left + rect.width / 2 + 420 / 2 + 20,
      y: Math.floor(Math.random() * 500)
    }
    // 如果是API工具，唤起弹框添加
    if (type === NODETYPES.API) {
      eventBus.emit('INSERT_API', { position })
      props.cancelPop?.()
      return
    }
    // 发布事件

    let param = {}
    const rest = (template.find((t) => t.id === props.tab)?.nodes || []).find(
      (it) => it.backendId === props.backendId
    )
    const adapter = NodeAdapterFactory.getAdapter(rest.nodeType)
    if (adapter) {
      param = adapter.fromBackToFront(rest)
    }

    eventBus.emit('INSERT_NODE', { type, position, data: param?.data })
  }

  const onDragStart = (event, props) => {
    const { type } = props
    let data = {}

    let param = {}
    const rest = (template.find((t) => t.id === props.tab)?.nodes || []).find(
      (it) => it.backendId === props.backendId
    )
    const adapter = NodeAdapterFactory.getAdapter(rest.nodeType)
    if (adapter) {
      param = adapter.fromBackToFront(rest)
    }

    const eventPageX = event.clientX || event.pageX
    const eventPageY = event.clientY || event.pageY
    const domRect = event.target.getBoundingClientRect()
    const pageX = domRect.x || domRect.left
    const pageY = domRect.y || domRect.top
    event.dataTransfer.setData(
      'application/reactflow',

      JSON.stringify({
        nodeType: type,
        delta: { x: eventPageX - pageX, y: eventPageY - pageY },
        data: param?.data
      })
    )
    event.dataTransfer.effectAllowed = 'move'
  }

  return (
    <Tooltip placement={props.index % 2 === 0 ? 'left' : 'right'} title={props.description}>
      <div
        className={`${styles.listItem}`}
        onDragStart={(event) => onDragStart(event, props)}
        draggable
        onClick={onPlusClick}
      >
        <div className={styles.leftContent}>
          {Component && <Component style={{ marginRight: 8 }} />}
          <span className={styles.itemTitle} title={title}>
            {title}
          </span>
        </div>
      </div>
    </Tooltip>
  )
}

const Node = () => {
  const buttonRef = useRef(null)
  const template = useTemplateStore((state) => state.template)

  const [open, setOpen] = useState(false)
  const hide = () => {
    setOpen(false)
  }
  const handleOpenChange = (newOpen) => {
    setOpen(newOpen)
  }

  let cb = (it) =>
    it.type === NODETYPES.API || it.type === NODETYPES.KNOWLEDGE || it.type === NODETYPES.CODE

  if (APP_ENV !== 'auto' && !isReallyProduction()) {
    cb = (it) => it
  }

  const modelData = (template.find((it) => it.id === 'llm')?.nodes || []).map((item) => {
    if (item.nodeType === NODETYPES.API && APP_ENV === 'auto') {
      return {
        ...item,
        title: item.title.replace(/插件/g, '智能体'),
        type: item.nodeType,
        description: item.description.replace(/插件/g, '智能体')
      }
    } else if (item.nodeType === NODETYPES.API && APP_ENV === 'aiui') {
      return {
        ...item,
        title: item.title.replace(/插件/g, '信源'),
        type: item.nodeType,
        description: item.description.replace(/插件/g, '信源')
      }
    } else {
      return {
        ...item,
        title: item.title,
        type: item.nodeType,
        description: item.description
      }
    }
  })

  const toolData = (template.find((it) => it.id === 'tool')?.nodes || []).map((item) => {
    return {
      ...item,
      title: item.title,
      type: item.nodeType,
      description: item.description
    }
  })
  // .filter(cb)

  const logicData = (template.find((it) => it.id === 'logic')?.nodes || [])
    .map((item) => {
      return {
        ...item,
        title: item.title,
        type: item.nodeType,
        description: item.description
      }
    })
    .filter((it) => it.type !== NODETYPES.PASS)
  const content = (
    <div>
      <div className={styles.listContainer} style={{ padding: '20px 18px' }}>
        <div className={styles.listWrap}>
          {modelData.map((item, index) => (
            <ListItem key={index} index={index} tab="llm" buttonRef={buttonRef} {...item} />
          ))}
        </div>
        <div className={styles.headTitle}>
          <span>逻辑</span>
        </div>
        <div className={styles.listWrap}>
          {logicData.map((item, index) => (
            <ListItem key={index} index={index} tab="logic" buttonRef={buttonRef} {...item} />
          ))}
        </div>
        <div className={styles.headTitle}>
          <span>工具/能力</span>
        </div>
        <div className={styles.listWrap}>
          {toolData.map((item, index) => (
            <ListItem
              key={index}
              index={index}
              tab="tool"
              buttonRef={buttonRef}
              cancelPop={hide}
              {...item}
            />
          ))}
        </div>
      </div>
    </div>
  )
  return (
    <Popover
      placement="top"
      arrow={false}
      trigger="click"
      title={null}
      content={content}
      overlayClassName="sub-overlay"
      open={open}
      onOpenChange={handleOpenChange}
    >
      <div className={styles['button']} ref={buttonRef}>
        <IconPlus />
        <span>节点</span>
      </div>
    </Popover>
  )
}
export default Node
