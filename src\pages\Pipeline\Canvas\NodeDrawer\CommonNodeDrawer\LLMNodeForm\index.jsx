import {
  But<PERSON>,
  <PERSON>lapse,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Slider,
  Space,
  Switch,
  Tooltip
} from 'antd'

import React, { memo, useCallback, useEffect, useState } from 'react'
import {
  CheckOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  QuestionCircleFilled
} from '@ant-design/icons'

import { useReactFlow, useNodesData, useNodeConnections } from '@xyflow/react'

import FormItemRenderer from '@/components/FormItemRender'
import useRefTreeData from '@/hooks/useRefTreeData'
import styles from './style.module.scss'
import IconCustomFlow from 'assets/svgs/cog.svg?react'
import { useModelStore } from '@/store/model'
import { useKnowledgeStore } from '@/store/knowledge'
import { DATA_TYPES_MAP, NODETYPES, ENTRY_SOURCE_ID } from '@/utils/constant'
import { getRules, outputRule, nameReservedRule } from '@/utils/rule'

import InputData from '@/components/InputData/input_data.jsx'
import useStore from '@/store.js'

import useRefUpdate from '@/hooks/useRefUpdate'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { BlurInput, BlurTextArea } from '@/components/BlurInput'
import HistorySwitch from '@/components/HistorySwitch'
import HistoryInputNumber from '@/components/HistoryInputNumber'

import NodeDebugDrawer from '../NodeDebugDrawer'
import eventBus from '@/utils/eventBus'
import withDividingLine from '@/components/WithDividingLine'

import IconArrow from 'assets/svgs/drop-arrow.svg?react'

import { COG_DOMAINS } from '@/utils/constant'

import BlurVariableEditor from './VariableEditor'
import useRefVariables from '@/hooks/useRefVariables'
import useRefVariable from '@/hooks/useRefVariable'

// const variables = {
//   user: {
//     name: { type: 'string', description: '用户名' },
//     age: { type: 'number', description: '年龄' }
//   },
//   order: {
//     items: {
//       type: 'array',
//       items: {
//         name: { type: 'string', description: '商品名' },
//         price: { type: 'number', description: '价格' }
//       }
//     }
//   }
// }

const genLabelTitle = (title) => {
  return <span className="text-[var(--flow-title-color2)] text-[14px] font-semibold">{title}</span>
}

export default memo((props) => {
  const { node, onClose, visible } = props
  const [form] = Form.useForm()
  const { updateNodeData } = useReactFlow()
  const { debouncedSetDirty } = useStore()
  const nodeData = useNodesData(node.id)
  const { updateRefName } = useRefUpdate(node)

  const { validateVariable } = useRefVariable()

  const hasExpand = (nodeData?.data?.output || [])
    .filter(Boolean)
    .some((val) => (val.schema || []).length > 0)
  console.log('++++++++++llmnode--------nodeData', nodeData)
  const treeData = useRefTreeData(nodeData, form, visible)
  // console.log('---------------------------------------------------------------treeData', treeData)
  const variables = useRefVariables(nodeData)
  // console.log('-----------variables------------', variables)
  const { validateInputData } = useInputDataValidate(treeData)

  const models = useModelStore((state) => state.model)
  const knowledges = useKnowledgeStore((state) => state.knowledge)

  const domains = node.type === NODETYPES.KNOWLEDGE ? knowledges : models
  // const rawDomains = node.type === NODETYPES.KNOWLEDGE ? knowledges : models
  // const domains = rawDomains.some((opt) => opt.value === nodeData?.data?.domain)
  //   ? rawDomains
  //   : [...rawDomains, { value: nodeData?.data?.domain, label: `${nodeData?.data?.domain}` }]

  console.log('llm node domains', domains)

  const ruleObj = getRules(node.type)

  // 官方的模版能力需要禁用某些
  const isOfficial = !!node.data?.abilityOriginId

  const connections = useNodeConnections({ id: node.id, type: 'target' })

  const preNodeIsEntry = !!connections?.find(
    (c) => c.source === ENTRY_SOURCE_ID && c.target === node.id
  )
  const [nodeDebugVisible, setNodeDebugVisible] = useState(false)
  useEffect(() => {
    // 订阅事件
    eventBus.on('DEBUG_NODE', handler)
    // 清理订阅
    return () => {
      eventBus.off('DEBUG_NODE', handler)
    }
  }, [form])

  const onCloseDebug = useCallback(() => {
    setNodeDebugVisible(false)
  }, [])

  const handler = () => {
    form
      .validateFields()
      .then(() => {
        setNodeDebugVisible(true)
        updateNodeData(node.id, { debug: false })
      })
      .catch(() => {
        updateNodeData(node.id, { debug: false })
      })
  }

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log('\n\n----onValuesChange---', allValues)
    //
    updateNodeData(node.id, allValues)
    debouncedSetDirty()
  }

  const setFields = () => {
    // console.log('useEffect trigged llm drawer', node.type, node?.data)
    form.setFieldsValue({
      domain: nodeData.data?.domain,
      abilityId: nodeData.data?.abilityId,
      abilityName: nodeData.data?.abilityName,
      prompt: nodeData.data?.prompt,
      systemPrompt: nodeData.data?.systemPrompt,
      audit: !!nodeData.data?.audit,
      dist: !!nodeData.data?.dist,
      memoryRound: nodeData.data?.memoryRound,
      flow_in: !!nodeData.data?.flow_in,
      flow_out: !!nodeData.data?.flow_out,
      input: nodeData.data?.input,
      output: nodeData.data?.output
    })
  }

  const onNameChange = (path, value) => {
    console.log('onNameChange', path, value)
    updateRefName(path, value)
  }

  useEffect(() => {
    setFields()
    //  校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      setTimeout(() => {
        form.validateFields()
      })
    }
  }, [nodeData, visible, form])

  useEffect(() => {
    if (nodeData.data.debug) {
      setTimeout(() => {
        handler()
      })
    }
  }, [nodeData.data.debug, form])

  let inputFormListAdd
  let outputFormListAdd

  const genExtra = (isInput) => {
    const showAdd = isOfficial ? false : isInput ? true : false
    return (
      <Space size={0} split={<Divider type="vertical" />}>
        {!isInput && (
          <Form.Item name="dist" style={{ marginBottom: 0 }}>
            <Switch
              checkedChildren={
                <span>
                  下发
                  <CheckOutlined />
                </span>
              }
              unCheckedChildren={
                <span>
                  下发
                  <CloseOutlined />
                </span>
              }
            />
          </Form.Item>
        )}
        <Form.Item
          name={isInput ? 'flow_in' : 'flow_out'}
          initialValue={true}
          style={{ marginBottom: 0 }}
        >
          <Switch
            disabled={true}
            key="flow"
            checkedChildren={
              <span>
                流式
                <CheckOutlined />
              </span>
            }
            unCheckedChildren={
              <span>
                流式
                <CloseOutlined />
              </span>
            }
          />
        </Form.Item>

        {showAdd && (
          <Button
            type="dashed"
            onClick={() => {
              if (isInput) {
                inputFormListAdd({})
              } else {
                outputFormListAdd()
              }
            }}
            size="small"
          >
            +
          </Button>
        )}
      </Space>
    )
  }

  const disabledVals = COG_DOMAINS.map((it) => `${it}_${it}_lite`)

  const allOptions = domains
    .filter(isOfficial ? Boolean : (it) => !disabledVals.includes(it.value))
    .map((d, index) => {
      return (
        <Select.Option value={d.value} key={index}>
          <div className="flex items-center">
            {d.icon ? (
              <div
                className={styles['model-icon']}
                style={{ backgroundImage: `url(${d.icon})` }}
              ></div>
            ) : (
              <IconCustomFlow style={{ width: 20, height: 20, marginRight: 6 }} />
            )}
            <span>{d.label}</span>
          </div>
        </Select.Option>
      )
    })

  const items = [
    node.type !== NODETYPES.ARC
      ? {
          key: '1',
          label: genLabelTitle('模型'),
          children: (
            <Form.Item name="domain" style={{ marginBottom: 0 }} rules={ruleObj['domain']}>
              <Select
                placeholder="请选择模型"
                disabled={isOfficial}
                showSearch
                optionFilterProp="label"
                filterOption={(input, option) => {
                  const label = domains.find((d) => d.value === option.value)?.label
                  return label.toLowerCase().includes(input.toLowerCase())
                }}
              >
                {allOptions}
              </Select>
            </Form.Item>
          )
        }
      : null,

    {
      key: '3',
      label: genLabelTitle('输入'),
      children: (
        <>
          <Form.List name="input">
            {(subFields, { add, remove }) => {
              inputFormListAdd = add
              return (
                <div className="relative">
                  {subFields.map((subField) => {
                    const key = form.getFieldValue(['input', subField.name, 'key'])

                    return (
                      <>
                        <Space
                          key={subField.key + 'ext'}
                          style={{ display: 'flex', marginBottom: 8 }}
                          align="middle"
                          size={4}
                        >
                          <Form.Item
                            name={[subField.name, 'key']}
                            rules={[
                              ...outputRule.name,
                              nameReservedRule,
                              ({ getFieldValue }) => ({
                                validator(_, value) {
                                  if (value) {
                                    const currentIndex = subField.name

                                    const sameLevelVal = getFieldValue(['input'])
                                    for (let i = 0; i < sameLevelVal.length; i++) {
                                      if (i === currentIndex) {
                                        continue
                                      }
                                      if (sameLevelVal[i]?.key === value) {
                                        return Promise.reject(new Error('变量名不可重复'))
                                      }
                                    }

                                    // 校验该变量是否被 提示词所使用
                                    const promptVal = getFieldValue(['prompt']) || ''
                                    const systemPromptVal = getFieldValue(['systemPrompt']) || ''

                                    if (!isOfficial) {
                                      // 构造这个输入key对应值的schema
                                      const valid = validateVariable(
                                        {
                                          key: sameLevelVal[currentIndex].key,
                                          value: sameLevelVal[currentIndex].value
                                        },
                                        `${promptVal}${systemPromptVal}`
                                      )
                                      if (!valid) {
                                        return Promise.reject(new Error('该变量未被提示词引用'))
                                      }
                                    }
                                  }

                                  return Promise.resolve()
                                }
                              })
                            ]}
                            style={{ marginBottom: 0 }}
                          >
                            <BlurInput
                              placeholder="参数名"
                              showCount={!isOfficial}
                              maxLength={32}
                              disabled={isOfficial}
                            />
                          </Form.Item>
                          <Form.Item
                            name={[subField.name, 'value']}
                            style={{ marginBottom: 0, width: 198 }}
                            rules={[
                              ({ getFieldValue }) => ({
                                validator(_, value) {
                                  const isRequired =
                                    getFieldValue(['input', subField.name, 'keyProp'])?.required !==
                                    false
                                  return validateInputData(_, value, !isRequired)
                                }
                              })
                            ]}
                          >
                            <InputData options={treeData} placeholder={'请输入或引用参数值'} />
                          </Form.Item>
                          {subFields.length > 1 && !isOfficial && (
                            <Form.Item style={{ marginBottom: 0, width: 10 }}>
                              <CloseCircleOutlined
                                onClick={() => remove(subField.name)}
                                className="text-[var(--flow-desc-color)]"
                              />
                            </Form.Item>
                          )}
                        </Space>
                      </>
                    )
                  })}
                </div>
              )
            }}
          </Form.List>
        </>
      ),
      extra: genExtra(true)
    },

    !isOfficial
      ? {
          key: 'memory',
          label: genLabelTitle('记忆'),
          children:
            nodeData.data?.memoryRound > 0 ? (
              <Form.Item
                name="memoryRound"
                layout="horizontal"
                className="p-[5px] relative"
                label={
                  <>
                    会话轮数 &nbsp;
                    <Tooltip title="设置带入模型上下文的对话历史轮数。轮数越多，多轮对话的相关性越高，但消耗的 Token 也越多。">
                      <QuestionCircleFilled className="text-[var(--flow-desc-color)]" />
                    </Tooltip>
                  </>
                }
                style={{ marginBottom: 0 }}
              >
                {/* <HistoryInputNumber /> */}
                <Slider
                  min={1}
                  max={10}
                  tooltip={{
                    open: true,
                    placement: 'right',
                    getPopupContainer: (triggerNode) => triggerNode.parentNode
                  }}
                />
              </Form.Item>
            ) : null,
          extra: (
            <Tooltip
              title={'开启记忆后，会自动把开始节点入参text对应的会话上下文信息，发送给模型。'}
            >
              <Form.Item name="memoryRound" style={{ marginBottom: 0 }}>
                <HistorySwitch
                  
                />
              </Form.Item>
            </Tooltip>
          )
        }
      : null,

    !isOfficial && typeof nodeData?.data?.systemPrompt !== 'undefined'
      ? {
          key: '4',
          label: genLabelTitle('系统提示词'),
          children: (
            <Form.Item
              name="systemPrompt"
              style={{ marginBottom: 0 }}
              rules={ruleObj['systemPrompt']}
            >
              <BlurVariableEditor
                variables={variables}
                placeholder="系统提示词，可以使用{变量名}、{变量名.子变量名}、{变量名[数组索引]}的方式引用输入参数中的变量"
              />
            </Form.Item>
          )
        }
      : null,
    !isOfficial && typeof nodeData?.data?.prompt !== 'undefined'
      ? {
          key: '5',
          label: genLabelTitle('用户提示词'),
          children: (
            <Form.Item name="prompt" style={{ marginBottom: 0 }} rules={ruleObj['prompt']}>
              <BlurVariableEditor
                variables={variables}
                placeholder="用户提示词，可以使用{变量名}、{变量名.子变量名}、{变量名[数组索引]}的方式引用输入参数中的变量"
              />
            </Form.Item>
          )
        }
      : null,

    {
      key: '11',
      label: genLabelTitle('输出'),
      children: (
        <Form.List name="output">
          {(subFields, { add, remove }) => {
            // console.log('****************subFields**************', subFields)
            outputFormListAdd = add

            return (
              <div className="relative">
                {subFields.map((subField) => (
                  <Space align="start" size={4} key={subField.key}>
                    <FormItemRenderer
                      field={subField}
                      remove={remove}
                      form={form}
                      path={['output', subField.name]}
                      closeable={subFields.length > 1}
                      hasExpand={hasExpand}
                      onNameChange={onNameChange}
                      isTop={true}
                    />
                    {/* <Form.Item style={{ marginBottom: 8, width: 20 }}>
                    <CloseCircleOutlined
                      onClick={() => remove(subField.name)}
                      className="text-[var(--flow-desc-color)]"
                    />
                  </Form.Item> */}
                  </Space>
                ))}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(false)
    }
  ]
    .filter(Boolean)
    .map((item) => {
      return {
        ...item,
        children: withDividingLine(item.children)
      }
    })

  return (
    <>
      <Form
        form={form}
        name="basic"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        labelWrap
        layout="vertical"
        onValuesChange={onValuesChange}
        requiredMark={false}
        className="flex-1 overflow-auto mr-[-24px] pr-0 "
        preserve={false}
      >
        <Collapse
          items={items}
          defaultActiveKey={[
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8',
            '9',
            '10',
            '11',
            '12',
            'memory'
          ]}
          size="small"
          collapsible={'icon'}
          ghost
          className="pr-[20px]"
          expandIcon={({ isActive }) => (
            <IconArrow
              style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
            ></IconArrow>
          )}
        />
      </Form>

      <NodeDebugDrawer {...props} nodeDebug={nodeDebugVisible} onCloseDebug={onCloseDebug} />
    </>
  )
})
