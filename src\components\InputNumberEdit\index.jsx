import { EditOutlined } from '@ant-design/icons'
import { Input, InputNumber } from 'antd'
import { useState } from 'react'
import { useReactFlow } from '@xyflow/react'
import useStore from '@/store.js'

function TextEdit({ id, type, k, value }) {
  const { setNodes, setEdges, updateNodeData, updateEdgeData } = useReactFlow()
  const [isEdit, setIsEdit] = useState(false)
  const { setIsDirty } = useStore()

  const onEditClick = () => {
    setIsEdit(true)
  }

  const onSubmit = () => {
    setIsEdit(false)
    setIsDirty(true)
  }

  const onChange = (val) => {
    if (type === 'node') {
      updateNodeData(id, { [k]: val })
    } else if (type === 'edge') {
      updateEdgeData(id, { [k]: val })
    }
  }
  return (
    <>
      {!isEdit ? (
        <div className="flex pl-1 items-center">
          <span className="mr-2" style={{ color: 'rgba(0, 0, 0, 0.88)', fontSize: 12 }}>
            {k}:
          </span>
          <span className="mr-2">{value}</span>
          <EditOutlined onClick={onEditClick} />
        </div>
      ) : (
        <div className="flex pl-1 items-center">
          <span className="mr-2" style={{ color: 'rgba(0, 0, 0, 0.88)', fontSize: 12 }}>
            {k}:
          </span>
          <InputNumber
            value={value}
            onChange={onChange}
            onBlur={onSubmit}
            size="small"
            onPressEnter={onSubmit}
            style={{ width: 200 }}
          />
        </div>
      )}
    </>
  )
}
export default TextEdit
