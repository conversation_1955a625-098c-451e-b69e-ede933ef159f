import { But<PERSON>, Collapse, Divider, Form, Input, Space, Switch } from 'antd'
import { memo, useCallback, useEffect } from 'react'
import { CloseCircleOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons'

import { useReactFlow } from '@xyflow/react'
import { debounce } from 'lodash'
import { DATA_TYPES_MAP, NODETYPES } from '@/utils/constant'
import useRefTreeData from '@/hooks/useRefTreeData'
import InputData from '@/components/InputData/input_data.jsx'
import useStore from '@/store.js'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { BlurInput, BlurTextArea } from '@/components/BlurInput'
import { getRules, outputRule } from '@/utils/rule'
import withDividingLine from '@/components/WithDividingLine'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'

const genLabelTitle = (title) => {
  return <span className="text-[var(--flow-title-color2)] text-[14px] font-semibold">{title}</span>
}

const moveNlpToFront = (arr = []) => {
  const index = arr.findIndex((item) => item.key === 'nlp')
  return index === -1 ? [...arr] : [arr[index], ...arr.slice(0, index), ...arr.slice(index + 1)]
}

export default memo((props) => {
  const { node, onClose, visible } = props
  const [form] = Form.useForm()
  const treeData = useRefTreeData(node, form, visible)
  const { validateInputData } = useInputDataValidate(treeData)
  const { updateNodeData } = useReactFlow()
  const { debouncedSetDirty } = useStore()

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onValuesChange = (changedValues, allValues) => {
    updateNodeData(node.id, allValues)
    debouncedSetDirty()
  }

  useEffect(() => {
    console.log('node.data', node.data)

    form.setFieldsValue({
      input: moveNlpToFront(node.data?.input),
      dist: node.data?.dist
    })
    //  校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      setTimeout(() => {
        form.validateFields()
      })
    }
  }, [node, visible, form])

  let inputFormListAdd

  const genExtra = (isInput) => (
    <Space size={0} split={<Divider type="vertical" />}>
      <Form.Item name="dist" style={{ marginBottom: 0 }}>
        <Switch
          checkedChildren={
            <span>
              下发
              <CheckOutlined />
            </span>
          }
          unCheckedChildren={
            <span>
              下发
              <CloseOutlined />
            </span>
          }
        />
      </Form.Item>
      {/* <Button
        type="dashed"
        onClick={(e) => {
          e.stopPropagation()
          inputFormListAdd && inputFormListAdd()
        }}
        size="small"
      >
        +
      </Button> */}
    </Space>
  )
  // const genExtra = (isInput) => (
  //   <Form.Item name="dist" style={{ marginBottom: 0 }}>
  //     <Switch
  //       checkedChildren={
  //         <span>
  //           下发
  //           <CheckOutlined />
  //         </span>
  //       }
  //       unCheckedChildren={
  //         <span>
  //           下发
  //           <CloseOutlined />
  //         </span>
  //       }
  //     />
  //   </Form.Item>
  // )

  const items = [
    {
      key: '9',
      label: genLabelTitle(node.type === NODETYPES.ENTRY ? '输入' : '输出'),
      children: (
        <Form.List name="input">
          {(subFields, { add, remove }) => {
            inputFormListAdd = add
            return (
              <>
                {subFields.map((subField, index) => {
                  // 获取当前subField对应的method值
                  // const method = subField?.value?.method
                  const method = form.getFieldValue(['input', subField.name, 'method'])
                  console.log('method', method)
                  return (
                    <Space
                      key={subField.key}
                      style={{ display: 'flex', marginBottom: 8 }}
                      align="start"
                      size={4}
                    >
                      <Form.Item
                        name={[subField.name, 'key']}
                        rules={[
                          ...outputRule.name,
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              if (value) {
                                const currentIndex = subField.name

                                const sameLevelVal = getFieldValue(['input'])
                                for (let i = 0; i < sameLevelVal.length; i++) {
                                  if (i === currentIndex) {
                                    continue
                                  }
                                  if (sameLevelVal[i]?.key === value) {
                                    return Promise.reject(new Error('变量名不可重复'))
                                  }
                                }
                              }

                              return Promise.resolve()
                            }
                          })
                        ]}
                        style={{ marginBottom: 8 }}
                      >
                        <BlurInput
                          placeholder="参数名"
                          showCount
                          maxLength={32}
                          disabled={index === 0}
                        />
                      </Form.Item>
                      <Form.Item
                        name={[subField.name, 'value']}
                        style={{ marginBottom: 0, width: 198 }}
                        rules={[{ validator: (rule, value) => validateInputData(rule, value) }]}
                      >
                        <InputData options={treeData} placeholder={'请输入或引用参数值'} />
                      </Form.Item>
                      {index > 0 && (
                        <Form.Item style={{ marginBottom: 8 }}>
                          <CloseCircleOutlined
                            onClick={() => remove(subField.name)}
                            className="text-[var(--flow-desc-color)]"
                          />
                        </Form.Item>
                      )}
                    </Space>
                  )
                })}
                {/* <Button type="dashed" onClick={() => add()} size="small">
                  +
                </Button> */}
              </>
            )
          }}
        </Form.List>
      ),
      extra: genExtra()
    }
  ]
    .filter(Boolean)
    .map((item) => {
      return {
        ...item,
        children: withDividingLine(item.children)
      }
    })

  return (
    <Form
      form={form}
      name="basic"
      initialValues={{ inputs: node?.data?.inputs, outputs: node?.data?.outputs }}
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      labelWrap
      layout="vertical"
      onValuesChange={onValuesChange}
      requiredMark={false}
      className="flex-1 overflow-auto mr-[-24px] pr-0"
      preserve={false}
    >
      <Collapse
        items={items}
        defaultActiveKey={['9']}
        size="small"
        collapsible={'icon'}
        ghost
        className="pr-[20px]"
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
      />
    </Form>
  )
})
