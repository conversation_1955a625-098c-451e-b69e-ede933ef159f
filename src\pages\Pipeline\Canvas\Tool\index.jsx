import { ZoomInOutlined, ZoomOutOutlined } from '@ant-design/icons'
import { Panel, useViewport, useStore, useReactFlow, useNodes, useEdges } from '@xyflow/react'
import { Slider, Button, Space, Tooltip, Dropdown } from 'antd'
import useLayout from '@/hooks/useLayout'
import styles from './style.module.scss'
import ajax from '@/utils/http'

import IconKeyboard from 'assets/svgs/keyboard.svg?react'
import IconLocation from 'assets/svgs/location.svg?react'
import IconClean from 'assets/svgs/clean.svg?react'
import IconRestore from 'assets/svgs/restore.svg?react'
import IconDuplicate from 'assets/svgs/duplicate.svg?react'
import IconThumbnail from 'assets/svgs/thumbnail.svg?react'
import IconFitview from 'assets/svgs/fitview.svg?react'
import IconLayout from 'assets/svgs/layout.svg?react'
import IconUndo from 'assets/svgs/undo.svg?react'
import IconRedo from 'assets/svgs/redo.svg?react'

import IconTemplate from 'assets/svgs/template.svg?react'
import IconPlus from 'assets/svgs/plus2.svg?react'

import useMyStore from '@/store.js'
import { useEffect, useRef } from 'react'

import Template from './Template'
import Node from './Node'

import { MiniMap } from '@xyflow/react'
import { useState } from 'react'

import { APP_ENV } from '@/utils/constant'

export default function Tool({ visible, ...props }) {
  const { zoom } = useViewport()
  const { zoomTo, zoomIn, zoomOut, fitView, setNodes, setEdges } = useReactFlow()
  const nodes = useNodes()
  const edges = useEdges()
  const [{ setLayout }] = useLayout(nodes, setNodes, edges, setEdges)
  const { setIsDirty } = useMyStore()

  const divRef = useRef(null)

  const [showMiniMap, setShowMiniMap] = useState(false)

  useEffect(() => {
    if (visible) {
      const drawerWidth = 428

      // divRef.current.style.transition = 'transform 0.3s ease-in-out'
      divRef.current.style.transform = `translateX(calc(-50% - ${drawerWidth / 2}px)`
    } else {
      // divRef.current.style.transition = 'transform 0.3s ease-in-out'
      divRef.current.style.transform = 'translateX(-50%)'
    }
  }, [visible])

  const { minZoom, maxZoom } = useStore(
    (state) => ({
      minZoom: state.minZoom,
      maxZoom: state.maxZoom
    }),
    (a, b) => a.minZoom !== b.minZoom || a.maxZoom !== b.maxZoom
  )

  const layout = () => {
    setLayout(true)
    setTimeout(() => {
      fitView()
      setIsDirty(true)
    })
  }

  const toggle = () => {
    setShowMiniMap((val) => !val)
  }

  const items = [
    {
      key: 'fitview',
      label: <span>自适应</span>
    },
    {
      key: '20',
      label: <span>缩放到20%</span>
    },
    {
      key: '50',
      label: <span>缩放到50%</span>
    },
    // {
    //   key: '75',
    //   label: <span>75%</span>
    // },
    {
      key: '100',
      label: <span>缩放到100%</span>
    },
    
    {
      key: '200',
      label: <span>缩放到200%</span>
    }
  ]

  const onClick = ({ key }) => {
    if (key === 'fitview') {
      fitView({ duration: 300 })
    } else {
      zoomTo(key / 100, { duration: 300 })
    }
  }

  const onIntentClick = () => {
    window.parent?.postMessage({ type: 'INTENT_CLICK' }, '*')
  }

  return (
    <Panel className="positive" {...props} position="bottom-center" ref={divRef}>
      <Space size={10}>
        <Space className={styles['pannel-wrapper']} size={4} align="center">
          <Tooltip title="缩小">
            <Button
              type="text"
              icon={<ZoomOutOutlined className="text-[var(--flow-desc-color)]" />}
              size="small"
              onClick={() => zoomOut({ duration: 300 })}
            />
          </Tooltip>

          <Dropdown
            menu={{
              items,
              onClick
            }}
            placement="top"
          >
            <span className={styles['zoom-number']}>{parseInt(zoom * 100)}%</span>
          </Dropdown>
          <Tooltip title="放大">
            <Button
              icon={<ZoomInOutlined className="text-[var(--flow-desc-color)]" />}
              size="small"
              onClick={() => zoomIn({ duration: 300 })}
              type="text"
            />
          </Tooltip>
        </Space>
        <Space className={styles['pannel-wrapper']} size={12}>
          {/* <Tooltip title="定位初始节点">
            <Button type="text" icon={<IconLocation />} size="small" />
          </Tooltip>
          <Tooltip title="清空画布">
            <Button type="text" icon={<IconClean />} size="small" />
          </Tooltip>
          <Tooltip title="还原线上版本">
            <Button type="text" icon={<IconRestore />} size="small" />
          </Tooltip>

          <Tooltip title="创建副本">
            <Button type="text" icon={<IconDuplicate />} size="small" />
          </Tooltip> */}
          {/* <div className={styles['vertical-divider']}></div> */}

          <Tooltip title="缩略图">
            <Button
              type="text"
              icon={<IconThumbnail className="text-[var(--flow-desc-color)]" />}
              size="small"
              onClick={toggle}
            />
          </Tooltip>
          {/* <Tooltip title="适配当前视图">
            <Button
              type="text"
              icon={<IconFitview className="text-[var(--flow-desc-color)]" />}
              size="small"
              onClick={() => fitView({ duration: 300 })}
            />
          </Tooltip> */}

          <Tooltip title="优化布局">
            <Button
              type="text"
              icon={<IconLayout className="text-[var(--flow-desc-color)]" />}
              size="small"
              onClick={layout}
            />
          </Tooltip>
          {/* <Tooltip title="撤销">
            <Button type="text" icon={<IconUndo />} size="small" onClick={layout} />
          </Tooltip>
          <Tooltip title="重做">
            <Button type="text" icon={<IconRedo />} size="small" onClick={layout} />
          </Tooltip> */}
          <div className={styles['vertical-divider']}></div>
          <Space size={2}>
            {/* <div className={styles['button']}><IconTemplate/><span>添加模板</span></div> */}
            <Template />
            <Node />
            {APP_ENV === 'aiui' && (
              <Button type="primary" onClick={onIntentClick}>
                意图管理
              </Button>
            )}
          </Space>
        </Space>
      </Space>
      {showMiniMap && <MiniMap className={styles['minimap']} pannable />}
    </Panel>
  )
}
