.container {
  // background-color: #f1f7ff;
}
.body {
  flex: 1;
  overflow: auto;
}
.header {
  background-color: #f1f7ff;
  height: 56px;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .headerText {
    font-size: 16px;
    font-weight: 600;
    color: var(--flow-title-color2);
    line-height: 19px;
  }
}
.banner {
  width: 1200px;
  height: 160px;
  background: url(assets/images/<EMAIL>) center/contain no-repeat;
  padding-top: 30px;
  padding-left: 42px;
  border-radius: 8px;
  margin: 24px auto 0;

  .bannerLeftTitle {
    width: 96px;
    height: 45px;
    font-size: 32px;
    font-weight: 500;
    line-height: 38px;

    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-image: linear-gradient(180deg, #ffffff, #ffffff 100%, rgba(255, 255, 255, 0.3) 100%);
    // color: #dae2f4;
  }
  .bannerLeftDesc {
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #dae2f4;
    line-height: 16px;
    margin-top: 12px;
  }
}
.content {
}
.layout {
  height: 100vh;
  // width: 1280px;
  margin: 0 auto;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}
