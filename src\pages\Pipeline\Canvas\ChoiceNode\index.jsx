import React, { memo, useCallback } from 'react'

import { useN<PERSON>, <PERSON>le } from '@xyflow/react'
import cls from 'classnames'
import Icon from 'assets/svg/nd-switch.svg?react'
import { Tooltip, Popover } from 'antd'
import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons'
import TextEdit from '@/components/TextEdit'
import Form from './Form'
import NodeHeader from '@/components/NodeHeader'
import { NODETYPES } from '@/utils/constant'

export default memo((props) => {
  const { data, sourcePosition, targetPosition, isConnectable, id } = props
  const nodes = useNodes()
  
  return (
    <div>
      <NodeHeader id={id} type={NODETYPES.CHOICE} data={data} />
      <Form {...props} />
      <Handle
        type="target"
        position={targetPosition}
        className="handle"
        id="target"
        isConnectable={isConnectable}
        
      />
    </div>
  )
})
