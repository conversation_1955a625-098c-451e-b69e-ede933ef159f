import { useReactFlow, useNodesData, getOutgoers } from '@xyflow/react'
import useStore from '@/store.js'
import { NODETYPES } from '@/utils/constant'

function useRefUpdate(node) {
  const { updateNodeData, getNodes, getEdges } = useReactFlow()
  const nodeData = useNodesData(node.id)
  const { debouncedSetDirty } = useStore()

  const getResultString = (data, path) => {
    let current = data
    let result = []

    for (let i = 0; i < path.length; i++) {
      const key = path[i]

      if (typeof key === 'number') {
        // 如果是数字索引，表示访问数组
        current = current[key]
      } else {
        // 如果是字符串键名，表示访问对象
        current = current[key]
      }

      // 如果当前对象有 name 属性，将其加入结果数组
      if (current && current.name) {
        result.push(current.name)
      }
    }

    return result.join('.')
  }

  const replaceLastPart = (input, newPart) => {
    // 找到最后一个 . 的位置
    const lastDotIndex = input.lastIndexOf('.')

    // 如果不存在 .，直接返回新字符串
    if (lastDotIndex === -1) {
      return newPart
    }

    // 截取最后一个 . 之前的部分
    const prefix = input.substring(0, lastDotIndex + 1)

    // 拼接新的部分
    return prefix + newPart
  }

  // 递归获取所有后继节点
  const getAllSuccessors = (node, nodes, edges, visited = new Set()) => {
    visited.add(node.id)

    // 获取直接后继节点
    const outgoers = getOutgoers(node, nodes, edges)

    let allSuccessors = [...outgoers]

    outgoers.forEach((outgoer) => {
      // 递归获取后继节点的后继节点
      allSuccessors = [...allSuccessors, ...getAllSuccessors(outgoer, nodes, edges, visited)]
    })

    // 去重并过滤有效节点
    let finalAllSuccessors = Array.from(new Set(allSuccessors))
    // const hasExitNode = finalAllSuccessors.find((nd) => nd.type === NODETYPES.EXIT)
    // if (!hasExitNode) {
    //   const exitNode = nodes.find((nd) => nd.type === NODETYPES.EXIT)
    //   finalAllSuccessors = [...finalAllSuccessors, exitNode]
    // }
    // return [node, ...finalAllSuccessors]
    return [...finalAllSuccessors]
  }

  const updateRefName = (path, value) => {
    console.log('onNameChange', path, value)
    const oldName = getResultString(nodeData.data, path)
    if(!oldName) {
      return
    }
    const oldFullName = `${node.id}.${oldName}`
    // console.log('getResultString', getResultString(nodeData.data, path))
    const newName = replaceLastPart(oldName, value)
    const newFullName = `${node.id}.${newName}`
    // 获取所有后继节点，将引用进行替换更新
    const nodes = getNodes()
    const edges = getEdges()
    const successors = getAllSuccessors(node, nodes, edges)
    let flag = false
    successors.forEach((nd) => {
      // 需判断是否是分支节点，
      if (nd.type === NODETYPES.CHOICE) {
        const newChoice = (nd.data?.choice || []).map((choice) => {
          return {
            ...choice,
            rule: (choice.rule || []).map((r) => {
              let v = r.variable
              if (v?.includes(oldFullName)) {
                flag = true
                v = v?.replace(oldFullName, newFullName)
              }
              return {
                ...r,
                variable: v
              }
            })
          }
        })
        updateNodeData(nd.id, {
          choice: newChoice
        })
      } else {
        const newInput = (nd.data?.input || []).map((input) => {
          if (input?.value?.ref === 'ref' && input?.value?.path?.includes(oldFullName)) {
            flag = true
            return {
              ...input,
              value: { ref: 'ref', path: input?.value?.path?.replace(oldFullName, newFullName) }
            }
          } else {
            return { ...input }
          }
        })
        updateNodeData(nd.id, {
          input: newInput
        })
      }
    })
    if (flag) {
      debouncedSetDirty()
    }
  }
  return {
    updateRefName
  }
}
export default useRefUpdate
