import { ConfigProvider } from 'antd'
import { APP_ENV, token, cssVariables } from '@/utils/constant'
import zhCN from 'antd/locale/zh_CN'
import List from './index'
import ajax from '@/utils/http'
import { useUserStore } from '@/store/user'
import { useEffect } from 'react'

function Wrapper() {
  const setLimitCount = useUserStore((state) => state.setLimitCount)

  useEffect(() => {
    ajax({
      url: '/user/getLimitCount',
      data: {},
      method: 'get'
    }).then((res) => {
      setLimitCount(res.data?.data)
    })
  }, [])
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: token[APP_ENV],
        components: {
          Collapse: {
            // contentPadding: '16px 0',
            headerPadding: '10px 0 !important'
          },
          Button: {
            primaryShadow: '',
            colorPrimary: cssVariables[APP_ENV]['--sparkos-button-color'],
            algorithm: true
          }
        }
      }}
    >
      {/* <RouterProvider router={router} /> */}
      <List />
    </ConfigProvider>
  )
}
export default Wrapper
