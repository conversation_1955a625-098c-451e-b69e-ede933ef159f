import styles from '../form.module.scss'
import { useKnowledgeStore } from '@/store/knowledge'
import { useModelStore } from '@/store/model'
import { NODETYPES, DATA_TYPES_MAP } from '@/utils/constant'
import IconCustomFlow from 'assets/svgs/cog.svg?react'
import Input from '../Common/Input'
import Output from '../Common/Output'
import DebugData from '../Common/DebugData'

function LLMNodeForm(props) {
  const { id, type, data, sourcePosition, isConnectable } = props

  const knowledges = useKnowledgeStore((state) => state.knowledge)
  const models = useModelStore((state) => state.model)

  const domains = type === NODETYPES.KNOWLEDGE ? knowledges : models
  // const rawDomains = type === NODETYPES.KNOWLEDGE ? knowledges : models
  // const domains = rawDomains.some((opt) => opt.value === data?.domain)
  //   ? rawDomains
  //   : [...rawDomains, { value: data?.domain, label: `${data?.domain}` }]

  const d = domains.find((it) => it.value === data?.domain) || {}
  //  NODETYPES.ARC 不需要配置
  const dom = (
    <div className="flex items-center justify-end">
      {type !== NODETYPES.KNOWLEDGE && d.icon && (
        <div className={styles['model-icon']} style={{ backgroundImage: `url(${d.icon})` }}></div>
      )}
      {type !== NODETYPES.KNOWLEDGE && d.label && !d.icon && (
        <IconCustomFlow style={{ width: 20, height: 20, marginRight: 6 }} />
      )}

      {d.label ? (
        <span
          className="inline-block max-w-[200px] whitespace-nowrap overflow-hidden text-ellipsis"
          title={d.label}
        >
          {d.label}
        </span>
      ) : (
        <span className="text-[var(--flow-desc-color)]">未配置模型</span>
      )}
    </div>
  )

  return (
    <div className={styles.container}>
      <Input inputOrOutput={data?.input} id={id} />
      <Output inputOrOutput={data?.output} keyProp="name" />
      {type !== NODETYPES.KNOWLEDGE &&
        type !== NODETYPES.API &&
        type !== NODETYPES.CODE &&
        type !== NODETYPES.ARC &&
        type !== NODETYPES.PK &&
        type !== NODETYPES.FUNCTION &&
        type !== NODETYPES.SEMANTIC && (
          <div className={styles.item}>
            <div className={styles.label}>模型</div>
            <div className={styles['values-model']}>{dom}</div>
          </div>
        )}
      <DebugData id={id} />
    </div>
  )
}
export default LLMNodeForm
