import { message } from 'antd'
import { APP_ENV } from '@/utils/constant'

const key = 'outdated'


const handleBaseExpire = () => {
  let withOutParamUrl = location.href.split('?')[0]
  let loginPath = ''
  if (location.hostname === 'localhost') {
    loginPath = `${location.origin}/sparkos/login`
  } else {
    loginPath = `${location.origin}/login?redirect=${withOutParamUrl}`
  }
  // message.error('登录过期，请重新登录..')
  message.error({
    key,
    type: 'error',
    content: '登录过期，请重新登录'
  })
  setTimeout(() => {
    location.href = loginPath
  }, 1000)
}

const handleAutoExpire = () => {

  // 处理登录认证过期
  window.microApp?.dispatch({ type: 'TOKEN_EXPIRED' })
}

export default function () {
  if(APP_ENV === 'base') {
    handleBaseExpire()
  } else if(APP_ENV === 'auto') {
    handleAutoExpire()
  }
}
