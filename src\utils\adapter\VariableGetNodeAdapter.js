import { NodeAdapter } from './NodeAdapter'
import {
  handleInputFromBack2Canvas,
  handleOutputFromBack2Canvas,
  handleInputFromCanvas2Back,
  handleOutputFromCanvas2Back
} from './inputOutputTransform'
import { MarkerType, Position, getOutgoers } from '@xyflow/react'

const convertTemplateFromBack2Front = function (template) {
  // validated 前端字段，在这里过滤
  let { ability, input, inputAlias, nodeType, output, title, type, position, validated, ...rest } =
    template

  const keys = Object.keys(output || {})
  if (keys.length === 1) {
    const k = keys[0]
    if (!(output || {})[k]?.desc) {
      // 没有值，搞个默认值
      output = { text: { desc: { format: 'plain', schema: { type: undefined } } } }
    }
  }

  let param = {
    ...rest,
    input: handleInputFromBack2Canvas(input),
    output: handleOutputFromBack2Canvas(output),

    type: nodeType,
    name: title
  }
  if (ability) {
    let aid = ability.id
    if (aid) {
      if (aid.startsWith('cbm_')) {
        aid = aid.substring(4)
      }
    }

    let acategory = ability.category
    if (acategory) {
      if (acategory.startsWith('cbm_')) {
        acategory = acategory.substring(4)
      }
    } else {
      acategory = aid
    }

    param = {
      ...param,
      abilityId: aid,
      abilityCategory: acategory,
      abilityName: ability.name,
      abilityType: ability.type,
      abilityOriginId: ability.originId
    }
  }
  return param
}

const convertTemplateFromFront2Back = function (data = {}, nodes) {
  const {
    abilityId,
    abilityName,
    abilityType,
    abilityOriginId,
    abilityCategory,

    input,
    output,
    name,
    // 前端字段，在这里去除
    validated,
    debug,
    ...rest
  } = data

  let newData = {
    ...rest,
    title: name,
    type: 'task',
    input: handleInputFromCanvas2Back(data?.input, nodes),
    output: handleOutputFromCanvas2Back(output),
    ability: {
      id: abilityId ? `cbm_${abilityId}` : '',
      category: abilityCategory ? `cbm_${abilityCategory}` : abilityId ? `cbm_${abilityId}` : '',
      originId: abilityOriginId,
      // ability.name 接收页面 的title
      name,
      type: abilityType || 'LLM'
    }
  }

  return newData
}

export class VariableGetNodeAdapter extends NodeAdapter {
  static fromBackToFront(nd) {
    let param = {
      id: nd.id,
      type: nd.nodeType,
      position: {
        x: parseInt(nd.position?.x ?? 0),
        y: parseInt(nd.position?.y ?? 0)
      },
      sourcePosition: Position.Right,
      targetPosition: Position.Left
    }
    if (nd.parentId) {
      param = {
        ...param,
        parentId: nd.parentId,
        extent: nd.extent
      }
    }
    param = {
      ...param,
      data: convertTemplateFromBack2Front(nd)
    }
    return param
  }

  static fromFrontToBack(nd, nodes) {
    const { id, type, position, data } = nd
    let param = {
      id,
      nodeType: type,
      position: {
        x: parseInt(position?.x ?? 0),
        y: parseInt(position?.y ?? 0)
      }
    }
    if (nd.parentId) {
      ;(param.parentId = nd.parentId), (param.extent = nd.extent)
    }
    const newData = convertTemplateFromFront2Back(data, nodes)
    param = {
      ...newData,
      ...param
    }
    return param
  }
}
