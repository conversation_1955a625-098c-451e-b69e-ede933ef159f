import React, { useState } from 'react'
import { Button, Form, Input, Select, Switch, InputNumber } from 'antd'
const { Option } = Select
const HistoryInputNumber = (props) => {
  const { id, value, onChange, ...rest } = props
  const [val, setVal] = useState(1)

  //   const onSwitchChange = (val) => {
  //     setVal(val ? 1 : -1)
  //     onChange && onChange(val ? 1 : -1)
  //   }

  const handleChange = (newValue) => {
    // 当值为 null 或 undefined 时（用户删除所有内容），设置为最小值 1
    if (newValue === null || newValue === undefined) {
      setVal(1)
      onChange && onChange(1)
    } else {
      setVal(newValue)
      onChange && onChange(newValue)
    }
  }

  return (
    <span id={id}>
      {/* <Switch onChange={onSwitchChange} value={Number(value) > 0 || Number(val) > 0} {...rest} /> */}
      <InputNumber min={1} max={10} step={1} value={value || val} onChange={handleChange} {...rest} />
    </span>
  )
}

export default HistoryInputNumber
