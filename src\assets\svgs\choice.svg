<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_4217_32826)">
<rect width="26" height="26" rx="8" fill="#F7840C"/>
<rect width="26" height="26" rx="8" fill="url(#paint0_radial_4217_32826)" fill-opacity="0.5"/>
<path d="M16.3368 10.3369L19.0034 13.0036L16.3368 15.6702M7.00342 17.0036L6.99675 17.0036M8.99675 17.0036L8.99008 17.0036M10.9901 16.3369L10.9834 16.3369M12.3168 14.3369L12.3101 14.3369" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M19.0034 13.0034L15.4074 13.0034C14.668 13.0033 13.94 12.8207 13.2881 12.4718C12.6362 12.1229 12.0804 11.6186 11.6701 11.0034C11.2598 10.3883 10.704 9.8839 10.0521 9.53502C9.40014 9.18614 8.67217 9.00354 7.93275 9.00342L7.00342 9.00342" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_i_4217_32826" x="-1" y="-2" width="27" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_4217_32826"/>
</filter>
<radialGradient id="paint0_radial_4217_32826" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(8.31148 5.11475) rotate(55.9679) scale(19.8013 19.8013)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>
