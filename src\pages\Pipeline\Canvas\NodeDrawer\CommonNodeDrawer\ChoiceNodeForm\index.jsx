import { <PERSON><PERSON>, Button, Space, Form } from 'antd'
import { memo, useCallback, useEffect } from 'react'
import { PlusOutlined } from '@ant-design/icons'

import { useReactFlow, useNodesData, useUpdateNodeInternals } from '@xyflow/react'

import styles from './style.module.scss'
import useStore from '@/store.js'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'

import IfCard from './IfCard'

export default (props) => {
  const { node, onClose, visible } = props
  const [form] = Form.useForm()
  const { updateNodeData } = useReactFlow()
  const updateNodeInternals = useUpdateNodeInternals()
  const nodeData = useNodesData(node.id)
  const { debouncedSetDirty } = useStore()

  useEffect(() => {
    console.log('choice node drawer， node.data is::', node.data)

    form.setFieldsValue({
      choice: nodeData.data?.choice || []
    })
    //  校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      setTimeout(() => {
        form.validateFields()
      })
    }
  }, [nodeData?.data?.choice, visible, form])

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const handleMethodChange = (subField, value) => {
    form.setFieldsValue({ [subField.name]: { method: value } })
  }

  const handleUpdateNodeInternals = () => {
    updateNodeInternals(node.id)
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log('------onValuesChange allValues-----', allValues)
    let newAllValues = {
      ...allValues,
      choice: (allValues.choice || []).map((item) => {
        if (item) {
          return {
            ...item,
            rule: (item.rule || []).map((r) => {
              if (r) {
                return r
              } else {
                return {
                  variable: '',
                  const: '',
                  func: ''
                }
              }
            })
          }
        } else {
          return {
            type: 'is',
            rule: [
              {
                variable: '',
                const: '',
                func: ''
              }
            ],
            next: ''
          }
        }
      })
    }
    updateNodeData(node.id, newAllValues)
    debouncedSetDirty()
  }


  return (
    <DndProvider backend={HTML5Backend}>
      <Form
        form={form}
        name="basic"
        initialValues={{}}
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        labelWrap
        layout="vertical"
        onValuesChange={onValuesChange}
        requiredMark={false}
        className="flex-1 overflow-auto mr-[-24px] pr-1"
        preserve={false}
      >
        {/* 第一层 Form.List */}
        <Form.List name="choice">
          {(fields, { add, remove, move }) => {
            // const ruleList = form.getFieldValue(['choice', 'rule']) || []

            const cardMove = (from , to) => {
              console.log('cardMove', from, to)
              move(from, to)
              handleUpdateNodeInternals()
            }

            return (
              <>
                <Space className="justify-between w-full mt-1 mb-2">
                  <div className={styles.title}>条件分支</div>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    icon={<PlusOutlined />}
                    size="small"
                  ></Button>
                </Space>

                {fields.map(({ key, name }, index) => {
                  return (
                    <IfCard
                      form={form}
                      fields={fields}
                      add={add}
                      remove={remove}
                      index={index}
                      key={key}
                      name={name}
                      node={node}
                      visible={visible}
                      moveCard={cardMove}
                    />
                  )
                })}
              </>
            )
          }}
        </Form.List>

        {/* Else 条件 */}
        <div className={styles.elseCard}>否则</div>
      </Form>
    </DndProvider>
  )
}
