import React, { useEffect, useRef, useState } from 'react'
import { message, Popconfirm, Flex, Space, Tag } from 'antd'
import { useNavigate } from 'react-router-dom'
import ajax from '@/utils/http'
import IconEdit from '@/assets/svgs/icon-edit.svg?react'
import IconCopy from '@/assets/svgs/icon-copy.svg?react'
import IconContect from '@/assets/svgs/icon-contect.svg?react'
import IconDelete from '@/assets/svgs/icon-delete.svg?react'
import IconDeleteRed from '@/assets/svgs/icon-delete-red.svg?react'

import CardList from '@/components/CardList'

import styles from './style.module.scss'
import { APP_ENV } from '@/utils/constant'
import CopyModal from './CopyModal'
import IntentModal from './IntentModal'
import AddModal from './AddModal'
import { TagOutlined } from '@ant-design/icons'

import dayjs from 'dayjs'

const List = () => {
  const navigate = useNavigate()
  const currentFlow = useRef(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isCopyModalOpen, setIsCopyModalOpen] = useState(false)
  const [isIntentModalOpen, setIsIntentModalOpen] = useState(false)

  const cardListRef = useRef(null) // 用于获取 CardList 的 ref

  const handleCopyModalCopySuccess = () => {
    setIsCopyModalOpen(false)
    cardListRef?.current.reload()
  }

  const handleCopyModalCancel = () => {
    setIsCopyModalOpen(false)
  }

  const handleIntentModalCancel = () => {
    setIsIntentModalOpen(false)
  }

  // modal 相关

  const onClick = () => {
    setIsModalOpen(true)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const [isHovered, setIsHovered] = useState(false)

  const optItems = [
    {
      key: 'edit',
      label: (
        <Space>
          <IconEdit />
          <div>编辑</div>
        </Space>
      )
    },
    {
      key: 'relate-intent',
      label: (
        <Space>
          <IconContect />
          <div>关联意图</div>
        </Space>
      )
    },

    {
      key: 'copy',
      label: (
        <Space>
          <IconCopy />
          <div>{APP_ENV === 'auto' ? '复制' : '复刻'}</div>
        </Space>
      )
    },
    {
      key: 'delete',
      label: (
        <Space
          className={'delete-menu-item'}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {isHovered ? <IconDeleteRed /> : <IconDelete />}
          <Popconfirm
            title="删除工作流"
            description={
              APP_ENV === 'auto'
                ? '确认删除该工作流？'
                : '删除后，已绑定appid发布的API将不可再调用该工作流。确认删除该工作流？'
            }
            onConfirm={(e) => onDeleteClick()}
            okText="是"
            okButtonProps={{ loading: false }}
            cancelText="否"
            onPopupClick={(e) => e.stopPropagation()}
          >
            <div>删除</div>
          </Popconfirm>
        </Space>
      ),
      // danger: true,
      style: { backgroundColor: isHovered ? '#fee2e2' : '#fff' }
    }
  ]

  const onCardClick = (item) => {
    navigateToDetail(item)
  }

  const onEditClick = (item) => {
    navigateToDetail(item)
  }

  const navigateToDetail = (item) => {
    //
    // const baseUrl = import.meta.env.BASE_URL || '/sparkos/flow' // 适配不同环境
    if (APP_ENV === 'auto') {
      navigate(`/${item.id}`, '_blank')
    } else {
      // console.log('location', location)
      // /sparkos/flow/
      // /sparkos/workspace/flow
      const pathname = window.location.pathname
      console.log('pathname is', pathname)
      if (pathname.includes('workspace')) {
        // navigate(`/flow/${item.id}`, '_blank')
        window.location.href = `${import.meta.env.VITE_ROUTER_BASE_URL}/flow/${item.id}`
      } else {
        navigate(`/${item.id}`, '_blank')
      }
    }
  }

  const onCopyClick = async (item) => {
    currentFlow.current = item
    // 判断name字符数，小于等于27，预留5位（副本XXX）
    // e.stopPropagation()
    if (item.name.length > 27) {
      setIsCopyModalOpen(true)
    } else {
      const result = await ajax({
        url: `/workflow/copy?id=${item.id}`,
        method: 'get'
      })
      if (result.data?.code === '0') {
        message.success('复刻成功')
        cardListRef?.current.reload()
      }
    }
  }
  const onRelateIntentClick = (item) => {
    currentFlow.current = item
    setIsIntentModalOpen(true)
  }
  const onDeleteClick = async () => {
    const result = await ajax({
      url: `/workflow/delete?id=${currentFlow.current.id}`,
      method: 'get'
    })
    if (result.data?.code === '0') {
      message.success('删除成功')
      // fetch(1)
      cardListRef?.current.reload()
    }
  }

  const onDropItemsClick = (e, l) => {
    console.log('------------onDropItemsClick----------', e, l)
    const key = e.key
    e.domEvent.stopPropagation()
    switch (key) {
      case 'edit':
        onEditClick(l)
        break
      case 'relate-intent':
        onRelateIntentClick(l)
        break
      case 'copy':
        onCopyClick(l)
        break
      case 'delete':
        currentFlow.current = l
        break
    }
  }

  const renderFooterTag = (record) => {
    if (record?.orgName) {
      return (
        <Tag color="orange" icon={<TagOutlined />}>
          {record?.orgName}
        </Tag>
      )
    } else {
      return null
    }
  }

  return (
    <div className="h-full">
      <CardList
        title="工作流"
        ref={cardListRef}
        searchConfig={{
          url: '/workflow/list',
          method: 'get',
          searchKey: 'search',
          pagination: {
            page: 'pageIndex',
            pageSize: 'pageSize'
          },
          dataFormatter: (data) => {
            return {
              list: (data?.data?.flows || []).map((item) => {
                return {
                  ...item,
                  updateTime: dayjs(item.updateTime).format('YYYY-MM-DD HH:mm:ss')
                }
              }),
              total: data?.data?.count
            }
          }
        }}
        cardConfig={{
          title: 'name',
          description: 'description',
          updateTime: 'updateTime',
          logo: 'logo'
        }}
        dropdown={{
          optItems,
          onDropItemsClick
        }}
        events={{
          onAddClick: onClick,
          onCardClick: onCardClick
        }}
        renderFooterTag={renderFooterTag}
      />

      <AddModal isModalOpen={isModalOpen} handleCancel={handleCancel} />

      <CopyModal
        isModalOpen={isCopyModalOpen}
        currentFlow={currentFlow}
        handleCancel={handleCopyModalCancel}
        copySuccess={handleCopyModalCopySuccess}
      />

      <IntentModal
        open={isIntentModalOpen}
        flowId={currentFlow.current?.id}
        onCancel={handleIntentModalCancel}
      />
    </div>
  )
}
export default List
