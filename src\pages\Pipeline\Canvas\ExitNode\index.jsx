import React, { memo, useCallback } from 'react'

import { use<PERSON><PERSON>, <PERSON>le } from '@xyflow/react'

import NodeHeader from '@/components/NodeHeader'
import { NODETYPES } from '@/utils/constant'
import ExitNodeForm from './Form'

export default memo((props) => {
  const { data, sourcePosition, targetPosition, isConnectable, id } = props
  const nodes = useNodes()
  
  return (
    <div className="">
      <NodeHeader id={id} type={NODETYPES.EXIT} data={data} />
      <ExitNodeForm {...props}/>
      <Handle
        type="target"
        position={targetPosition}
        className="handle"
        id="target"
        
        isConnectable={isConnectable}
        
      />
    </div>
  )
})
