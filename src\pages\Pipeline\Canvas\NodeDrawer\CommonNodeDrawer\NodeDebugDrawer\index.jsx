import { Drawer } from 'antd'
import Header from './Header'
import Content from './Content'

function NodeDebugDrawer(props) {
  const { onClose, nodeDebug, onCloseDebug } = props

  const drawerStyles = {
    mask: {
      // backdropFilter: 'blur(10px)',
    },
    content: {
      // boxShadow: '-10px 0 10px #666',
    },
    header: {
      borderBottom: `1px solid #EAECF0`
    },
    body: {
      // fontSize: token.fontSizeLG,
      userSelect: 'text'
    },
    footer: {
      // borderTop: `1px solid ${token.colorBorder}`,
    }
  }
  return (
    <Drawer
      title={null}
      footer={null}
      height={'calc(100% - 70px)'}
      placement="bottom"
      closable={false}
      onClose={onClose}
      open={nodeDebug}
      getContainer={false}
      destroyOnClose
      mask={false}
      rootStyle={{ position: 'absolute' }}
      width={420}
      styles={drawerStyles}
    >
      <div className="flex flex-col h-full">
        <Header {...props} onClose={onCloseDebug}/>
        <Content {...props}/>
      </div>
    </Drawer>
  )
}
export default NodeDebugDrawer
