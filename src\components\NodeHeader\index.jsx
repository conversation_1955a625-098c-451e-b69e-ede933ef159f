import {
  EditOutlined,
  DeleteOutlined,
  PlusSquareOutlined,
  InfoCircleOutlined,
  EllipsisOutlined
} from '@ant-design/icons'
import IconDebug from 'assets/svgs/debug.svg?react'

import React, { DragEvent } from 'react'
import { Input, InputNumber, Select, Popover, Dropdown, message } from 'antd'
import { useRef, useState, useCallback } from 'react'
import { useReactFlow, useNodes, useNodesData } from '@xyflow/react'
import useStore from '@/store.js'
import { nodesMap, NODETYPES, CONCURRENT_DIMENSION } from '@/utils/constant'
import { genNewNodeIdAndTitle, genNewAbilityId } from '@/utils/utils'
import { isLLMRelatedNode } from '@/utils/isLLMRelatedNode'
import { NodeAdapterFactory } from '@/utils/adapter/NodeAdapterFactory'
import ajax from '@/utils/http'
import eventBus from '@/utils/eventBus'
import getNodeIconComponent from '@/utils/getNodeIconComponent'

import { APP_ENV } from '@/utils/constant'

function NodeHeader({ id, type, data, parentId }) {
  const { setNodes, setEdges, updateNodeData, updateEdgeData, deleteElements } = useReactFlow()
  const [isEdit, setIsEdit] = useState(false)
  const { setIsDirty } = useStore()
  const nodes = useNodes()
  const nodeData = useNodesData(id)
  // console.log('insert node header type is**************************', type)
  const Component = getNodeIconComponent(
    type,
    type === NODETYPES.FUNCTION
      ? `${
          nodeData?.data.subType || nodeData?.data.abilityOriginId || nodeData?.data.abilityCategory
        }`
      : `${nodeData?.data.abilityOriginId || nodeData?.data.abilityCategory}`
  )

  const showDebug =
    type !== NODETYPES.ENTRY &&
    type !== NODETYPES.EXIT &&
    type !== NODETYPES.CHOICE &&
    type !== NODETYPES.CONCURRENT &&
    type !== NODETYPES.PASS &&
    type !== NODETYPES.FLOW &&
    type !== NODETYPES.VARIABLE_SET &&
    type !== NODETYPES.VARIABLE_GET

  const onEditClick = useCallback(() => {
    setIsEdit(true)
  }, [isEdit])

  const onDeleteClick = () => {
    deleteElements({ nodes: nodes.filter((nd) => nd.id === id) })
  }

  const onBlur = (event) => {
    setIsEdit(false)
    const nameReg = /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/
    if (!nameReg.test(event.target.value)) {
      return message.warning('名称只支持中文/英文/数字/下划线格式')
    }
    updateNodeData(id, { name: event.target.value })
    setTimeout(() => {
      setIsDirty(true)
    })
  }

  const copyNode = (sourceNode, option = {}) => {
    const { id, name } = genNewNodeIdAndTitle(nodes, sourceNode.type, sourceNode.data)

    let targetNode = {
      ...sourceNode,
      id,
      position: {
        x: sourceNode.position.x + 50,
        y: sourceNode.position.y + 50
      },
      selected: false,
      data: {
        ...(sourceNode.data || {}),
        name
      }
    }
    if (sourceNode.type === NODETYPES.CONCURRENT) {
      targetNode = {
        ...targetNode,
        style: {
          width: CONCURRENT_DIMENSION.width,
          height: CONCURRENT_DIMENSION.height
        }
      }
    }

    // 大模型相关节点初始化abilityId
    if (
      isLLMRelatedNode(sourceNode.type) ||
      sourceNode.type === NODETYPES.FLOW ||
      sourceNode.type === NODETYPES.KNOWLEDGE ||
      sourceNode.type === NODETYPES.CODE ||
      sourceNode.type === NODETYPES.API ||
      sourceNode.type === NODETYPES.FUNCTION ||
      sourceNode.type === NODETYPES.VARIABLE_SET ||
      sourceNode.type === NODETYPES.VARIABLE_GET
    ) {
      targetNode = {
        ...targetNode,
        data: {
          ...targetNode.data,
          abilityId: genNewAbilityId(
            nodes,
            sourceNode.type,
            sourceNode.data,
            sourceNode.type === NODETYPES.KNOWLEDGE
          ),
          abilityCategory: genNewAbilityId(
            nodes,
            sourceNode.type,
            sourceNode.data,
            sourceNode.type === NODETYPES.KNOWLEDGE
          )
        }
      }
    }
    setNodes((nds) => nds.concat(targetNode))
  }

  const onCopyClick = () => {
    const sourceNode = nodes.find((nd) => nd.id === id)
    const sourceNodeType = sourceNode.type
    // 分支判断、map、async节点复制需要单独处理
    copyNode(sourceNode)
    setTimeout(() => {
      setIsDirty(true)
    })
  }

  const onPublishClick = async () => {
    // 发布为模板
    let obj = {}
    // 通过适配器转换数据
    const adapter = NodeAdapterFactory.getAdapter(type)
    if (adapter) {
      obj = adapter.fromFrontToBack(nodeData)
    }
    const param = { ...obj, nodeType: type }
    const result = await ajax({
      url: '/workflow/node/template/save',
      method: 'post',
      data: JSON.stringify(param)
    })
    if (result.data.code === '0') {
      message.success('保存为模板成功')
      eventBus.emit('REFRESH_TEMPLATE')
    }
  }

  const items = [
    {
      key: 'rename',
      label: <span className="nodrag">重命名</span>
    },
    {
      key: 'duplicate',
      label: <span className="nodrag">{APP_ENV === 'auto' ? '复制' : '创建副本'}</span>
    },
    !data?.abilityOriginId && isLLMRelatedNode(type)
      ? {
          key: 'publish',
          label: <span className="nodrag">保存模板</span>
        }
      : null,
    {
      key: 'delete',
      label: <span className="nodrag">删除</span>
    }
  ].filter(Boolean)

  const onClick = (e) => {
    const key = e.key
    e.domEvent.stopPropagation()
    switch (key) {
      case 'rename':
        onEditClick()
        break
      case 'duplicate':
        onCopyClick()
        break
      case 'publish':
        onPublishClick()
        break
      case 'delete':
        onDeleteClick()
        break
    }
  }

  const onNameDbClick = (e) => {
    e.stopPropagation()
    onEditClick()
  }

  const onDebug = () => {
    updateNodeData(id, { debug: true })
  }

  const normal = type !== NODETYPES.ENTRY && type !== NODETYPES.EXIT

  return (
    <div>
      <div className="flex justify-between">
        <div className="flex items-center">
          <Component className="w-[26px] h-[26px]" />
          <div className="ml-2 flex items-center">
            {isEdit ? (
              <Input
                size="small"
                className="text-[13px] text-[var(--flow-title-color)]"
                defaultValue={data?.name}
                autoFocus
                onBlur={onBlur}
              />
            ) : (
              <span
                onDoubleClick={onNameDbClick}
                className="text-[13px] text-[var(--flow-title-color)] overflow-hidden text-ellipsis max-w-60 font-semibold"
                title={data?.name}
              >
                {data?.name}
              </span>
            )}
            {/* <span
              className="ml-2 text-[12px] text-[#98A2B2] overflow-hidden text-ellipsis max-w-40"
              title={data?.id}
            >
              {id}
            </span> */}
          </div>
        </div>
        <div className="flex items-center">
          {showDebug && (
            <div className="w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer mr-[2px]">
              <IconDebug className="text-[#101828] " onClick={onDebug} />
            </div>
          )}
          {normal && (
            <Dropdown
              menu={{
                items,
                onClick
              }}
              placement="bottom"
              className="nodrag w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer"
              onClick={(e) => e.stopPropagation()}
            >
              <div>
                <EllipsisOutlined className="text-[var(--flow-title-color)]" />
              </div>
            </Dropdown>
          )}
        </div>
      </div>
      {/* {normal && (
        <div className="text-[12px] text-[var(--flow-desc-color)]">这里是节点功能描述</div>
      )} */}
    </div>
  )
}
export default NodeHeader
