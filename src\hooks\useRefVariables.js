import { useEdges, useNodes } from '@xyflow/react'
import useRefVariable from '@/hooks/useRefVariable'

const useRefVariables = (node) => {
  const nodes = useNodes()
  const edges = useEdges()
  const { getRefVariable } = useRefVariable()

  const currentInput = node.data.input || []

  // 变量input中的每个key，溯源在源节点中的schema，拼成新的树形结构
  let variableTree = []
  currentInput
    .filter(Boolean)
    .filter((i) => i.key && i.value)
    .forEach(({ key, value }) => {
      variableTree.push(...getRefVariable(key, value, nodes))
    })

  return variableTree
}

export default useRefVariables
