import React, { memo, useCallback } from 'react'

import { use<PERSON><PERSON>, Handle } from '@xyflow/react'

import NodeHeader from '@/components/NodeHeader'
import { NODETYPES } from '@/utils/constant'

import Form from './Form'

export default memo((props) => {
  const { data, sourcePosition, targetPosition, isConnectable, id } = props
  const nodes = useNodes()
 
  return (
    <div className="">
      <NodeHeader id={id} type={NODETYPES.ENTRY} data={data} />
      <Form {...props}/>
      <Handle
        type="source"
        position={sourcePosition}
        className="handle"
        id="source"
        
        isConnectable={isConnectable}
        
      />
    </div>
  )
})
