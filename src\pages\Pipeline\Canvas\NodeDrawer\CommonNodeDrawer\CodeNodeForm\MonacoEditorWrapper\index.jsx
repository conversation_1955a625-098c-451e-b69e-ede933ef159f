import MonacoEditor from '@/components/MonacoEditor'
import { VerticalLeftOutlined } from '@ant-design/icons'
import styles from './style.module.scss'
import { createPortal } from 'react-dom'

function MonacoEditorWrapper({ value, onChange, onClose }) {
  return createPortal(
    <div className={styles.monacoEditorWrapper}>
      <div className={styles.monacoEditorTitle}>
        <div>代码(python)</div>
        <div className="w-6 h-6 rounded-lg flex items-center justify-center hover:bg-[#F0EFF1] cursor-pointer mr-[2px]">
          <VerticalLeftOutlined className="text-[#101828] " onClick={onClose} />
        </div>
      </div>
      <div className={styles.monacoEditor}>
        <MonacoEditor value={value} onChange={onChange} height={'100%'} />
      </div>
    </div>,
    document.getElementById('root')
  )
}
export default MonacoEditorWrapper
