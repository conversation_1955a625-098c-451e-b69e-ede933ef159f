// validate.worker.js
import { NODETYPES } from '@/utils/constant'
import { validationStrategies } from '@/pages/Pipeline/Canvas/Validates/validationStrategies'
import { getOutgoers } from '@xyflow/react'

// // function getOutgoers(node, nodes, edges) {
// //   const nodeId = node.id
// //   const connectedEdges = edges.filter((e) => e.source === nodeId)
// //   const targets = connectedEdges.map((e) => e.target)
// //   return nodes.filter((n) => targets.includes(n.id))
// // }

function getAllSuccessors(node, nodes, edges, visited = new Set()) {
  visited.add(node.id)
  const outgoers = getOutgoers(node, nodes, edges)
  let allSuccessors = [...outgoers]
  outgoers.forEach((out) => {
    allSuccessors.push(...getAllSuccessors(out, nodes, edges, visited))
  })
  const result = Array.from(new Set(allSuccessors))
  const hasExit = result.find((n) => n.type === NODETYPES.EXIT)
  if (!hasExit) {
    const exitNode = nodes.find((n) => n.type === NODETYPES.EXIT)
    result.push(exitNode)
  }
  return [node, ...result]
}

self.onmessage = async (e) => {
  console.log('log in webworker', e)
  const { nodes, edges } = e.data
  const entryNode = nodes.find((n) => n.type === NODETYPES.ENTRY)
  if (!entryNode) {
    self.postMessage({ issues: [] })
    return
  }

  const toValidate = getAllSuccessors(entryNode, nodes, edges)
  const issues = []

  for (const node of toValidate) {
    const validateStrategy = validationStrategies[node.type]
    if (validateStrategy) {
      const errors = await validateStrategy(node, nodes, edges)
      if (errors.length > 0) {
        issues.push({
          node,
          errors
        })
      }
    }
  }

  self.postMessage({ issues })
}
