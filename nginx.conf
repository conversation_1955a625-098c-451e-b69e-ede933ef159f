daemon off;
user  root;
worker_processes  4;
error_log  /var/log/nginx/error.log error;
pid        /var/run/nginx.pid;
events {
    worker_connections  65535;
}
http {
    include   /etc/nginx/mime.types;
    types { 
        application/javascript mjs; 
    }
    default_type  application/octet-stream;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    sendfile        on;
    keepalive_timeout  65;
    gzip on;
    gzip_http_version 1.0;
    gzip_disable "MSIE [1-6].";
    gzip_types text/plain application/x-javascript text/css text/javascript;
    include /etc/nginx/conf.d/*.conf;
}
