import { Alert, Form, Input, message, Modal } from 'antd'
import ajax from '@/utils/http'

function CopyModal({ isModalOpen, currentFlow, handleCancel, copySuccess }) {
  const [form] = Form.useForm()

  const handleOk = () => {
    form.validateFields().then(async (values) => {
      // saveData(values)
      const result = await ajax({
        url: `/workflow/copy?id=${currentFlow.current.id}&name=${values.name}`,
        method: 'get'
      })
      if (result.data?.code === '0') {
        message.success('复刻成功')
        copySuccess()
        // setIsModalOpen(false)
        // fetch(1)
      }
    })
  }
  return (
    <Modal
      title="复刻工作流"
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnClose={true}
    >
      <span className="text-[var(--flow-desc-color)] mb-[10px] inline-block">
        *保存成功将复刻一个当前工作流，支持单独编辑使用
      </span>
      <Alert
        message="副本名称超过字数限制，请重新命名"
        type="warning"
        size="small"
        className="mb-[10px]"
        style={{ marginBottom: 10 }}
      />
      <Form
        form={form}
        name="basic"
        layout="vertical"
        initialValues={{}}
        // onFinish={onFinish}
        // onFinishFailed={onFinishFailed}
        autoComplete="off"
        preserve={false}
      >
        <Form.Item
          label="名称"
          name="name"
          rules={[
            {
              required: true,
              message: '请输入名称'
            },
            {
              pattern: /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/,
              message: '只支持中文/英文/数字/下划线格式'
            }
          ]}
        >
          <Input placeholder="请输入工作流名称" showCount maxLength={32} />
        </Form.Item>
      </Form>
    </Modal>
  )
}
export default CopyModal
