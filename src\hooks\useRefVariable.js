import { useReactFlow, useNodesData, useNodes } from '@xyflow/react'
import generateTreeData from './generateTreeData'
import { NODETYPES } from '@/utils/constant'

const getNodeByValue = (value, data) => {
  for (const node of data) {
    if (node.key === value) return node
    if (node.children) {
      const found = getNodeByValue(value, node.children)
      if (found) return found
    }
  }
  return null
}
const transformTreeNode = (key, treeNode, isRoot) => {
  // 处理每个节点
  let newTreeNode = {
    key,
    title: isRoot ? key : treeNode.title,
    type: treeNode.type
  }
  if (treeNode.children && treeNode.children.length > 0) {
    newTreeNode.children = treeNode.children.map((child) => {
      const childKey = treeNode.type?.startsWith('array')
        ? `${key}[0].${child.title}`
        : `${key}.${child.title}`
      return transformTreeNode(childKey, child, false)
    })
  }
  return newTreeNode
}

/**
 * 校验模板插值路径是否合法
 * @param schema - schema数组
 * @param expression - 插值表达式，如 `{abc[0].sub.field1}`
 * @returns boolean 是否有效路径
 */
const validatePath = (schema, rawPath) => {
  // if (!expression.startsWith('{') || !expression.endsWith('}')) return false

  // 去除花括号，转为路径数组
  // const rawPath = expression.slice(1, -1)
  const segments = rawPath.replace(/\[(\d+)\]/g, '[$1]').split('.')

  // 递归匹配
  function match(nodes, index) {
    if (index >= segments.length) return true

    const currentKey = segments[index]

    for (const node of nodes) {
      const { title, type, children } = node

      // 匹配数组字段，如  abc[0]
      if (/\[\d+\]$/.test(currentKey)) {
        const name = currentKey.replace(/\[\d+\]$/, '')
        if (title === name && type === 'array<object>' && children) {
          return match(children, index + 1)
        }
      }

      // 匹配普通字段
      if (title === currentKey) {
        if ((type === 'object' || type === 'array<object>') && children) {
          return match(children, index + 1)
        }
        // 如果不是object类型，且已经是最后一个字段
        return index === segments.length - 1
      }
    }

    return false
  }

  return match(schema, 0)
}

const extractInterpolations = (str) => {
  const regex = /\{([^}]+)\}/g
  const matches = []
  let match

  while ((match = regex.exec(str)) !== null) {
    matches.push(match[1])
  }

  return matches
}

// 自输入类型或者引用类型value
export const getRefVariable = (key, value, nodes) => {
  let variableTree = []
  if (value?.ref === 'ref') {
    // 引用类型
    const path = value.path
    // 
    let outputTree = []
    if (path) {
      const nodeId = path.split('.')?.[0]
      if (nodeId) {
        const nd = nodes.find((n) => n.id === nodeId)
        
        if(nd) {
          // j加上if是为了兼容节点被删除的情况
          const output = nd.type === NODETYPES.ENTRY ? nd.data?.input : nd.data?.output
          outputTree = generateTreeData(nodeId, output || [])
        }
       
      }
    }
    const treeNode = getNodeByValue(path, outputTree)
    if (treeNode) {
      const newTreeNode = transformTreeNode(key, treeNode, true)
      variableTree.push(newTreeNode)
    }
  } else {
    variableTree.push({
      title: key,
      key: `${key}`,
      type: 'string'
    })
  }
  return variableTree
}

function useRefVariable() {
  //   const { updateNodeData, getNodes, getEdges } = useReactFlow()
  //   const nodeData = useNodesData(node.id)
  const nodes = useNodes()

  const validateVariable = ({ key, value }, str = '') => {
    const variableTree = getRefVariable(key, value, nodes)

    // 从str中提取所有{}中间的expression
    const result = extractInterpolations(str)
    const valid = result.some((item) => validatePath(variableTree, item))
    return valid
  }
  return {
    getRefVariable,
    validateVariable
  }
}

export const validateVariableUtil = ({ key, value }, str = '', nodes) => {
  const validateVariable = ({ key, value }, str, nodes) => {
    const variableTree = getRefVariable(key, value, nodes)

    // 从str中提取所有{}中间的expression
    const result = extractInterpolations(str)
    const valid = result.some((item) => validatePath(variableTree, item))
    return valid
  }
  return validateVariable({ key, value }, str, nodes)
}
export default useRefVariable
