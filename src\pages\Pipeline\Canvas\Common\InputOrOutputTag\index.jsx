import styles from '../../form.module.scss'
import { DATA_TYPES_MAP } from '@/utils/constant'
import IconExclamation from 'assets/svgs/exclamation.svg?react'

const isExisitInData = (value, data) => {
  for (const node of data) {
    if (node.key === value) return true
    if (node.children) {
      const found = isExisitInData(value, node.children)
      if (found) return true
    }
  }
  return false
}

function InputOrOutputTag({ inputOrOutput, keyProp = 'key', treeData }) {
  const Component = DATA_TYPES_MAP[inputOrOutput.type]?.component
  // 对于 treeData有值的情形，需要判断 引用值是否在 treeData中
  const val = inputOrOutput[keyProp]
  if (!val) {
    return (
      <div className={`${styles['value-item']} !bg-[rgba(255,188,133,0.2)]`}>
        {Component && <Component className={'text-[rgba(255,161,84,0.56)]'} />}
        <span className={`${styles['property-name']} !text-[rgba(255,161,84,1)]`}>{'未定义'}</span>
      </div>
    )
  } else {
    if (treeData) {
      if (inputOrOutput?.value?.ref === 'ref') {
        const isExist = isExisitInData(inputOrOutput?.value?.path, treeData)
        if (!isExist) {
          return (
            <div className={`${styles['value-item']} !bg-[rgba(255,188,133,0.2)]`}>
              {<IconExclamation className={'text-[rgba(255,161,84,0.56)]'} />}
              <span className={`${styles['property-name']} !text-[rgba(255,161,84,1)]`}>
                {inputOrOutput[keyProp]}
              </span>
            </div>
          )
        }
      }
    }
    return (
      <div className={styles['value-item']}>
        {Component && <Component className={'text-[var(--sparkos-primary-color)]'} />}
        <span className={styles['property-name']}>{inputOrOutput[keyProp]}</span>
      </div>
    )
  }
}
export default InputOrOutputTag
