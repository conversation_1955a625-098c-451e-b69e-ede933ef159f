import { NODETYPES } from '@/utils/constant'
import generateTreeData from './generateTreeData'
import { getIncomers, getOutgoers } from '@xyflow/react'

export const getTreeData = (node, nodes, edges) => {
  // // 获取所有分支起点（有多个出边的节点）
  // const getBranchStartNodes = (nodes, edges) => {
  //   return nodes.filter((node) => getOutgoers(node, nodes, edges).length > 1)
  // }

  // // 获取所有汇合节点（有多个入边的节点）
  // const getMergeNodes = (nodes, edges) => {
  //   return nodes.filter((node) => getIncomers(node, nodes, edges).length > 1)
  // }

  // 计算 A 到 D 的所有可达路径数
  // const countPaths = (startNode, targetNode, nodes, edges, visited = new Set()) => {
  //   if (startNode.id === targetNode.id) return 1 // 到达终点，计数+1
  //   if (visited.has(startNode.id)) return 0 // 防止循环

  //   visited.add(startNode.id)

  //   let pathCount = 0
  //   const outgoers = getOutgoers(startNode, nodes, edges)

  //   outgoers.forEach((nextNode) => {
  //     pathCount += countPaths(nextNode, targetNode, nodes, edges, new Set(visited))
  //   })

  //   return pathCount
  // }

  // 递归找到从 startNode 到 mergeNode 之间的所有子流程节点
  // const findSubProcessNodes = (startNode, mergeNode, nodes, edges, visited = new Set()) => {
  //   if (!startNode || visited.has(startNode.id) || startNode.id === mergeNode.id) return

  //   visited.add(startNode.id)

  //   const outgoers = getOutgoers(startNode, nodes, edges)
  //   outgoers.forEach((nextNode) => findSubProcessNodes(nextNode, mergeNode, nodes, edges, visited))

  //   return visited
  // }

  // 计算所有子流程节点
  // const getSubProcessNodes = (nodes, edges) => {
  //   const branchStartNodes = getBranchStartNodes(nodes, edges) // 找到所有分支起点
  //   const mergeNodes = getMergeNodes(nodes, edges) // 找到所有汇合点

  //   const subProcessNodes = new Set()

  //   branchStartNodes.forEach((startNode) => {
  //     mergeNodes.forEach((mergeNode) => {
  //       const pathCount = countPaths(startNode, mergeNode, nodes, edges)

  //       if (pathCount >= 2) {
  //         // 只有当 A->D 至少有 2 条路径时，才记录子流程节点
  //         const subProcess = findSubProcessNodes(startNode, mergeNode, nodes, edges, new Set())
  //         if (subProcess) {
  //           // 该方法会包含第一个startNode，所以在这里删除
  //           subProcess.delete(startNode.id);
  //           subProcess.forEach((nodeId) => subProcessNodes.add(nodeId))
  //         }
  //       }
  //     })
  //   })

  //   return subProcessNodes
  // }

  // 判断某个节点是否属于子流程
  // const isInSubProcess = (node, subProcessNodes) => {
  //   return subProcessNodes.has(node.id) || !!node.parentId
  // }

  const isInConcurrentSub = (node) => {
    return !!node.parentId
  }

  // const subProcessNodes = getSubProcessNodes(nodes, edges); // 计算所有子流程节点

  // 假设 shouldStopAtNode 是判断条件的函数
  const shouldStopAtNode = (currentNode, predecessor) => {
    // 这里根据 currentNode 和 predecessor 的节点属性来判断是否满足条件
    // 返回 true 表示满足条件，停止递归
    // 返回 false 表示不满足条件，继续递归

    // 1、 并发器场景 A-->|B-->C|-->D 递归结束在A
    const condition1 = currentNode?.parentId && !predecessor?.parentId
    // 2、 分支器场景 A-->B  B为分支器，递归结束在A
    const condition2 = currentNode?.type === NODETYPES.CHOICE
    // 3、大模型分裂成多个的场景 A-->B  A-->C （A不为选择器）
    const condition3 =
      predecessor &&
      predecessor.type !== NODETYPES.CHOICE &&
      getOutgoers(predecessor, nodes, edges).length > 1
    // return condition1 || condition2 || condition3
    return condition1
  }
  // 递归获取所有前置节点
  const getStopPredecessors = (node, nodes, edges, visited = new Set()) => {
    if (visited.has(node.id)) return []
    visited.add(node.id)

    // 获取直接前置节点
    const incomers = getIncomers(node, nodes, edges) || []

    let allPredecessors = []

    for (const incomer of incomers) {
      if (shouldStopAtNode(node, incomer)) {
        // 终止条件，包含当前 incomer，不再向前递归
        allPredecessors.push(incomer)
      } else {
        // 继续递归
        allPredecessors.push(...getStopPredecessors(incomer, nodes, edges, visited), incomer)
      }
    }

    return allPredecessors
  }

  const getAllPredecessors = (node, nodes, edges, visited = new Set()) => {
    if (visited.has(node.id)) return []
    visited.add(node.id)

    // 获取直接前置节点
    const incomers = getIncomers(node, nodes, edges) || []

    let allPredecessors = []

    for (const incomer of incomers) {
      allPredecessors.push(...getAllPredecessors(incomer, nodes, edges, visited), incomer)
    }

    return allPredecessors
  }

  const getPredecessors = (nd, nodes, edges) => {
    const node = nodes.find((n) => n.id === nd.id)
    if (isInConcurrentSub(node)) {
      return getStopPredecessors(node, nodes, edges)
    } else {
      return getAllPredecessors(node, nodes, edges)
    }
  }

  const tempAllPredecessors = getPredecessors(node, nodes, edges)
  const allPredecessors = Array.from(new Set(tempAllPredecessors)).filter((nd) => {
    if (nd.type === NODETYPES.ENTRY) {
      return (nd.data?.input || []).length > 0
    } else {
      return (nd.data?.output || []).length > 0
    }
  })

  const newRefList = allPredecessors
    .filter((n) => n.type !== NODETYPES.CHOICE) // 排除 CHOICE 类型节点
    .map((n) => {
      let inputOutput
      // let inputOutput =
      //   n.type === NODETYPES.ENTRY || n.type === NODETYPES.VARIABLE_GET
      //     ? n.data.input
      //     : n.data.output

      //
      if (n.type === NODETYPES.ENTRY) {
        inputOutput = n.data.input
      }
      // else if(n.type === NODETYPES.VARIABLE_GET) {
      //   inputOutput = (n.data.input || []).map(item => {
      //     return {
      //       name: item?.key || '',
      //       type: 'string'
      //     }
      //   })
      // }
      else {
        inputOutput = n.data.output
      }

      return {
        key: n.id,
        name: n.data?.name,
        children: inputOutput
      }
    })

  const treeData = (newRefList || []).map((data) => {
    return {
      title: data.name,
      key: data.key,
      selectable: false, // 顶层节点不可选
      children: generateTreeData(data.key, data.children) // 用 schema 转换为树形数据
    }
  })

  return treeData
}
