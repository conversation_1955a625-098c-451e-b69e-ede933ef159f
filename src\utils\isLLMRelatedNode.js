import { NODETYPES } from '@/utils/constant'

export const isLLMRelatedNode = (type) => {
  return (
    type === NODETYPES.LLM ||
    type === NODETYPES.SEMANTIC ||
    // type === NODETYPES.KNOWLEDGE ||
    type === NODETYPES.DENIAL ||
    type === NODETYPES.PK ||
    // type === NODETYPES.FUNCTION ||
    type === NODETYPES.ARC ||
    type === NODETYPES.INTENT_DOMAIN
    // ||
    // type === NODETYPES.API
  )
}
