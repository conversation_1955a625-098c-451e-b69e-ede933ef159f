.editor {
  position: relative;
  border: 1px solid #dcdcdf;
  border-radius: 6px;
  transition: border 0.3s ease;
  &:focus-within {
    border: 1px solid var(--sparkos-primary-color);
  }

  :global {
    .cm-content {
      min-height: 150px !important;
    }
    .cm-decoration-interpolation-valid {
      color: var(--sparkos-primary-color)
    }
  }
}

:global {
  .ant-form-item-has-error .variable-ref-editor {
    border-color: var(--ant-color-error) !important;
    // box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    
  }
}

.treeWrap {
  // &:focus {
  //   background-color: red;
  // }
  outline: none;
  // &:focus-within {
  //   border: 1px solid var(--sparkos-primary-color);
  // }
}
