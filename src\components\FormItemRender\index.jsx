import { Drawer, Button, Space, Form, Input, message, Select, Card, InputNumber } from 'antd'
import {
  CloseCircleOutlined,
  CloseOutlined,
  PlusCircleOutlined,
  CaretRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons'
import { createPortal } from 'react-dom'
import { useCallback, useEffect, useRef, useState } from 'react'
const { Option } = Select
import { DATA_TYPES_MAP } from '@/utils/constant'
import { outputRule } from '@/utils/rule'
import { BlurInput } from '@/components/BlurInput'

// 渲染表单项的组件
export default function FormItemRenderer({
  field,
  remove,
  form,
  path,
  closeable,
  hasExpand,
  onNameChange,
  isTop,
  disabled
}) {
  // console.log('--------------------FormItemRenderer field path', field, path)

  const [expand, setExpand] = useState(true)
  const fieldType = form.getFieldValue([...path, 'type']) // 使用 path 获取正确的字段类型
  // if (!fieldType) {
  //   form.setFieldValue([...path, 'type'], 'string')
  // }

  // console.log('---schema-', form.getFieldValue([...path, 'schema']))
  const schema = form.getFieldValue([...path, 'schema'])

  const handleTypeChange = (value) => {
    form.setFieldsValue({ [field.name]: { type: value } })
  }

  const handleNameChange = (e) => {
    // console.log('hanleNameChange', e.target.value)
    onNameChange && onNameChange(path, e.target.value)
  }

  const renderFormItem = () => {
    switch (fieldType) {
      case 'array<object>':
      case 'object':
        return (
          <div className="pl-[14px] ">
            <Form.List name={[field.name, 'schema']}>
              {(subFields, subOpt) => {
                add = subOpt.add
                return (
                  <>
                    {subFields.map((subField) => (
                      <Space
                        key={`${field.key}_${subField.key}`} // 确保子项 key 唯一
                        size={4}
                        className="relative"
                      >
                        <FormItemRenderer
                          field={subField}
                          remove={subOpt.remove}
                          form={form}
                          path={[...path, 'schema', subField.name]}
                          closeable={true}
                          hasExpand={true}
                          onNameChange={onNameChange}
                          disabled={disabled}
                        />
                        {/* <div className="absolute z-10 h-[1px] w-[8px] top-[50%] left-[-8px] bg-[#dcdcdf]"></div> */}
                      </Space>
                    ))}
                  </>
                )
              }}
            </Form.List>
          </div>
        )

      default:
        return null
    }
  }
  let add = null

  const FormItemDom = renderFormItem()

  const toggleExpand = useCallback(() => {
    setExpand((e) => !e)
  }, [expand])

  return (
    <div>
      <Space size={4} align="start">
        {Array.isArray(schema) && schema.length > 0 ? (
          <Form.Item style={{ marginBottom: 2 }} onClick={toggleExpand} className="cursor-pointer">
            {expand ? <CaretDownOutlined /> : <CaretRightOutlined />}
          </Form.Item>
        ) : hasExpand ? (
          <div className="w-[14px] h-[14px]"></div>
        ) : null}
        <Form.Item
          name={[field.name, 'name']}
          style={{ marginBottom: 8 }}
          rules={[
            ...outputRule.name,
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (value) {
                  const currentIndex = path.slice(-1)[0]
                  const prePath = path.slice(0, -1)
                  console.log(getFieldValue([...prePath]), currentIndex)
                  const sameLevelVal = getFieldValue([...prePath])
                  for (let i = 0; i < sameLevelVal.length; i++) {
                    if (i === currentIndex) {
                      continue
                    }
                    if (sameLevelVal[i]?.name === value) {
                      return Promise.reject(new Error('同层级变量名不可重复'))
                    }
                  }
                }

                return Promise.resolve()
              }
            })
          ]}
        >
          <BlurInput
            disabled={!!isTop || disabled}
            showCount={!isTop && !disabled}
            placeholder="变量名"
            maxLength={32}
            onChange={handleNameChange}
          />
        </Form.Item>
        <Form.Item name={[field.name, 'type']} style={{ marginBottom: 2 }} initialValue="string">
          <Select
            onChange={handleTypeChange}
            placeholder="选择类型"
            disabled={disabled}
            style={{ width: 150 }}
          >
            {Object.keys(DATA_TYPES_MAP).map((k) => {
              const Component = DATA_TYPES_MAP[k].component
              return (
                <Option value={k} key={k}>
                  <div className="flex items-center flex-nowrap">
                    {Component && <Component className="text-[var(--flow-desc-color)]" />}
                    <span className="ml-1">{DATA_TYPES_MAP[k].label}</span>
                  </div>
                </Option>
              )
            })}
          </Select>
        </Form.Item>
        {(fieldType === 'object' || fieldType === 'array<object>') && !disabled && (
          <Form.Item style={{ marginBottom: 2 }}>
            <PlusCircleOutlined
              onClick={() => add && add()}
              className="text-[var(--flow-desc-color)]"
            />
          </Form.Item>
        )}

        {closeable && !disabled && (
          <Form.Item style={{ marginBottom: 2 }}>
            <CloseCircleOutlined
              onClick={() => remove(field.name)}
              className="text-[var(--flow-desc-color)]"
            />
          </Form.Item>
        )}
      </Space>

      <div style={{ display: expand ? 'block' : 'none' }} className="relative">
        {/* <div
          className="absolute z-10 w-[1px]  top-0 left-[6px] bg-[#dcdcdf]"
          style={{ height: Array.isArray(schema) ? 10 * schema.length + 10 : 0 }}
        ></div> */}
        {FormItemDom}
      </div>
    </div>
  )
}
