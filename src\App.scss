p {
  margin-bottom: 2px;
}

// 节点编辑
.dynamic-delete-button {
  position: relative;
  top: 4px;
  margin: 0 2px;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
}
.dynamic-delete-button:hover {
  color: #777;
}
.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.node-warning {
  color: #ff4d4f;
  margin-left: 5px;
}

.absolute-tr {
  position: absolute;
  z-index: 1;
  top: 50%;
  right: -20px;
  transform: translateY(-50%);
}

.error {
  color: rgb(255, 77, 79);
}

#root {
  height: 100%;
  position: relative;
}

iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* 整体滚动条宽度和高度 */
::-webkit-scrollbar {
  width: 8px; /* 纵向滚动条宽度 */
  height: 8px; /* 横向滚动条高度 */
  // display: none;
}

/* 滚动条轨道（背景） */
::-webkit-scrollbar-track {
  background: #f9fafb; /* 更浅的灰色背景 */
  border-radius: 4px;
  // display: none;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #e6e9ef; /* 更浅的滑块颜色 */
  border-radius: 4px; /* 圆角滑块 */
  // display: none;
}

/* 鼠标悬停时的滚动条滑块 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25); /* 鼠标悬停时略深的滑块颜色 */
}

/* 水平滚动条样式调整 */
::-webkit-scrollbar-horizontal {
  height: 6px; /* 更窄的横向滚动条 */
}

::-webkit-scrollbar-thumb:horizontal {
  border-radius: 3px; /* 横向滑块更小的圆角 */
}

/* 禁用滚动条轨道边框（可选） */
::-webkit-scrollbar-track-piece {
  border: none;
}
.subPipelineWrap {
  .ant-modal {
    width: 100vw !important;
    max-width: 100vw !important;
    top: 0;
    padding-bottom: 0;
  }

  .ant-modal-body {
    height: 100vh;
    overflow-y: auto;
  }
  .ant-modal-content {
    padding: 0;
  }
}

.ant-form-item .ant-form-item-label > label {
  font-size: 12px;
}

.ant-drawer {
  outline: none !important;
}

// 修改节点抽屉弹框样式
.ant-drawer-right > .ant-drawer-content-wrapper {
  top: 8px;
  right: 8px;
  bottom: 8px;
  border: 1px solid rgba(17, 17, 17, 0.08);
  border-radius: 8px;
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  overflow: hidden;
  transition: none !important;
  transform: translateX(0) !important;
  .ant-drawer-content {
    background: linear-gradient(to bottom, #efedf8 0%, #ffffff 10%, #ffffff 100%);
  }
}

// collapse design token 未生效，在这里覆盖
.ant-collapse-header {
  padding: 8px 0 !important;
}
.ant-collapse-content {
  .ant-collapse-content-box {
    padding: 0 !important;
  }
}

.cm-editor {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
  color: var(--sparkos-text-color) !important;
  border: none !important;
  font-size: 13px !important;
  padding: 4px 0;

  line-height: 1.5715;
  
  caret-color: red !important; /* 光标颜色 */
  min-height: 100px;
  border: 1px solid #e5e7eb;
  &:focus {
    border-color: var(--sparkos-primary-color);
  }
}
.cm-focused {
  outline: none !important
}
// .cm-dropCursor {
//   border-left: 1px solid rgba(0, 0, 0, 0.85) !important; /* 模拟光标 */
//   margin-left: -0.5px; /* 可选：让光标更居中对齐字符 */
// }
