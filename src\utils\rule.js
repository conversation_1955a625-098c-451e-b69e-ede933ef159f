import { NODETYPES } from './constant'

export const repoRule = [
  {
    validator: (rule, value) => {
      if (value && Array.isArray(value) && value.filter((it) => it.used).length > 0) {
        return Promise.resolve()
      } else {
        return Promise.reject('至少开启一个知识库')
      }
    }
  }
]

// output 中的name 规则
export const outputRule = {
  name: [
    {
      required: true,
      message: '请填写参数名'
    },
    {
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
      message: '只能包含字母、数字或下划线，并且以字母或下划线开头'
    }
  ]
}

// 工作流节点入参系统保留字校验  user  system app local

export const nameReservedRule = {
  validator: (rule, value) => {
    if (value && ['user', 'system', 'app', 'local'].includes(value)) {
      return Promise.reject('变量名不能是系统保留关键字')
    } else {
      return Promise.resolve()
    }
  }
}

export const choiceRule = (validateChoiceInputData) => [
  {
    type: 'array',
    required: true,
    defaultField: {
      type: 'object',
      required: true,
      fields: {
        type: { type: 'string', required: true, message: '不可为空' },
        // next:
        rule: {
          type: 'array',
          required: true,
          defaultField: {
            type: 'object',
            required: true,
            fields: {
              // variable: { type: 'string', required: true, message: '参数值不可为空' },
              variable: { type: 'string', required: true, asyncValidator: validateChoiceInputData },
              func: { type: 'string', required: true, message: '条件不可为空' },
              const: {
                type: 'string',
                // required: true,
                validator: (rule, value, callback, source) => {
                  const { func } = source
                  // 假设当 func 为 'someValue' 时，const 可以为空
                  if (func && func.includes('Exist')) {
                    callback()
                  } else {
                    if (!value) {
                      callback('参数值不可为空')
                    } else {
                      callback()
                    }
                  }
                }
                // message: '参数值不可为空'
              }
            }
          }
        }
      }
    }
  }
]

export const inputRule = (validateInputData, validateInputKey) => [
  {
    type: 'array',
    required: true,
    // message: '输入不能为空',
    defaultField: {
      type: 'object',
      required: true,
      fields: {
        key: [
          {
            required: true,
            message: '请填写参数名'
          },
          {
            pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
            message: '只能包含字母、数字或下划线，并且以字母或下划线开头'
          },
          {
            asyncValidator: validateInputKey
          }
        ],
        value: {
          type: 'object',
          // required: true,
          // validator: (rule, value) => validateInputData(rule, value)
          // 校验输入项的必填与否
          asyncValidator: (_, value, cb, source) => {
            const isRequired = source.keyProp?.required !== false
            return validateInputData(_, value, !isRequired)
          }
        }
      }
    },
    validator: (rule, value, callback) => {
      if (!Array.isArray(value)) {
        callback()
        return
      }

      const keys = value.map((item) => item?.key)
      const duplicateKeys = keys.filter((key, index) => keys.indexOf(key) !== index)

      if (duplicateKeys.length > 0) {
        // 将错误信息关联到具体的 key 字段
        const errors = duplicateKeys.map((key) => ({
          message: `参数名 "${key}" 不能重复`
          // field: `input[${keys.indexOf(key)}].key`
        }))
        callback(errors)
      } else {
        callback()
      }
    }
  }
]

export const exitNodeInputRule = (validateInputData, validateInputKey) => [
  {
    type: 'array',
    required: true,
    // message: '输入不能为空',
    defaultField: {
      type: 'object',
      required: true,
      fields: {
        key: [
          {
            required: true,
            message: '请填写参数名'
          },
          {
            pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
            message: '只能包含字母、数字或下划线，并且以字母或下划线开头'
          },
          {
            asyncValidator: validateInputKey
          }
        ],
        value: {
          type: 'object',
          required: true,

          validator: (rule, value) => validateInputData(rule, value)
          // fields: {
          //   path: { type: 'string', required: true, message: '引用或输入不可为空' }
          // }
        }
      }
    },
    validator: (rule, value, callback) => {
      if (!value) {
        callback('输出不能为空')
        return
      }
      if (Array.isArray(value) && value.length === 0) {
        callback('输出不能为空')
        return
      }
      // if (!Array.isArray(value)) {
      //   callback()
      //   return
      // }

      const keys = value.map((item) => item?.key)
      const duplicateKeys = keys.filter((key, index) => keys.indexOf(key) !== index)

      if (duplicateKeys.length > 0) {
        // 将错误信息关联到具体的 key 字段
        const errors = duplicateKeys.map((key) => ({
          message: `参数名 "${key}" 不能重复`
          // field: `input[${keys.indexOf(key)}].key`
        }))
        callback(errors)
      } else {
        callback()
      }
    }
  }
]

const APIInputRule = (validateInputData) => [
  {
    type: 'array',
    // required: true,
    defaultField: {
      type: 'object',
      // required: true,
      fields: {
        value: {
          type: 'object',
          // required: true,
          asyncValidator: (_, value, cb, source) => {
            return validateInputData(_, value, !source.keyProp?.required)
          }
        }
      }
    }
  }
]

export const getRules = (type, option = {}) => {
  const { isOfficial } = option
  let LLMNodeRules = {
    // domain: [
    //   {
    //     required: true,
    //     message: '请选择模型'
    //   }
    // ],

    input: inputRule,
    output: outputRule
  }
  if (!isOfficial) {
    LLMNodeRules = {
      ...LLMNodeRules,
      domain: [
        {
          required: true,
          message: '请选择模型'
        }
      ],
      prompt: [
        {
          required: true,
          message: '请填写用户提示词'
        },
        {
          max: 2000,
          message: '内容不能超过2000个字符'
        }
      ],
      systemPrompt: [
        // {
        //   required: true,
        //   message: '请填写系统提示词'
        // },
        {
          max: 2000,
          message: '内容不能超过2000个字符'
        }
      ]
    }
  }

  // API node rule
  let APINodeRules = {}
  if (isOfficial) {
    if (option?.api_type === 'auto_template') {
      APINodeRules = {
        input: APIInputRule,
        output: outputRule,
        url: [
          {
            required: true,
            message: '请填写url信息'
          }
        ],
        method: [
          {
            required: true,
            message: '请选择请求方式'
          }
        ],
        auth_type: [
          {
            required: true,
            message: '请选择授权类型'
          }
        ],
        auth_key: [
          {
            required: true,
            message: '请填写授权key'
          }
        ]
      }
    } else {
      APINodeRules = {
        input: inputRule,
        output: outputRule,
        token: [
          {
            required: true,
            message: '请填写授权token信息'
          }
        ],
        work_id: [
          {
            required: true,
            message: '请填写API唯一ID（工作流ID或智能体ID）'
          }
        ],
        work_type: [
          {
            required: true,
            message: '请选择API类型'
          }
        ]
      }
    }
  } else {
    APINodeRules = {
      input: APIInputRule
    }
  }

  const LLMKnowledgeNodeRules = {
    input: inputRule,
    output: outputRule,
    repos: repoRule
  }

  const CodeNodeRules = {
    input: inputRule,
    output: outputRule,
    code: [
      {
        required: true,
        message: '请编写代码'
      }
    ]
  }

  const PKNodeRules = {
    input: inputRule,
    output: outputRule
  }

  const FunctionNodeRules = {
    input: inputRule,
    output: outputRule
  }

  const SemanticNodeRules = {
    input: inputRule,
    output: outputRule
  }

  const EntryNodeRules = {
    input: outputRule
  }

  const ExitNodeRules = {
    input: exitNodeInputRule
  }

  const VariableSetNodeRules = {
    input: exitNodeInputRule,
    output: outputRule
  }

  const VariableGetNodeRules = {
    input: exitNodeInputRule,
    output: outputRule
  }

  const PassNodeRules = {
    input: inputRule,
    output: outputRule
  }

  const FlowNodeRules = {
    input: inputRule,
    output: outputRule
  }

  // 选择器校验---主要字段choice
  const ChoiceNodeRules = {
    choice: choiceRule
  }

  switch (type) {
    case NODETYPES.LLM:

    case NODETYPES.DENIAL:
    case NODETYPES.ARC:
    case NODETYPES.INTENT_DOMAIN:
      return LLMNodeRules
    case NODETYPES.PK:
      return PKNodeRules
    case NODETYPES.FUNCTION:
      return FunctionNodeRules
    case NODETYPES.SEMANTIC:
      return SemanticNodeRules
    case NODETYPES.API:
      return APINodeRules
    case NODETYPES.KNOWLEDGE:
      return LLMKnowledgeNodeRules
    case NODETYPES.CODE:
      return CodeNodeRules
    case NODETYPES.ENTRY:
      return EntryNodeRules
    case NODETYPES.PASS:
      return PassNodeRules
    case NODETYPES.FLOW:
      return FlowNodeRules
    case NODETYPES.CHOICE:
      return ChoiceNodeRules
    case NODETYPES.EXIT:
      return ExitNodeRules

    case NODETYPES.VARIABLE_SET:
      return VariableSetNodeRules
    case NODETYPES.VARIABLE_GET:
      return VariableGetNodeRules

    default:
      return LLMNodeRules
  }
}
