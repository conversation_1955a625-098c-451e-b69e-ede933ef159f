.handle,
.handle-circle {
  width: 8px !important;
  height: 8px !important;
  border-radius: 100%;
  background: var(--sparkos-primary-color);
  transition: width height 1s ease;
  border: none;
  &:hover {
    width: 12px !important;
    height: 12px !important;
    transition: width height 1s ease;
  }
}
/**
edge
**/
.edgebutton {
  width: 18px;
  height: 18px;
  background: #eee;
  border: 1px solid #fff;
  cursor: pointer;
  border-radius: 50%;
  font-size: 12px;
  line-height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
}

.edgebutton:hover {
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.08);
}

.edgebutton-foreignobject div.edgebutton-wrap {
  background: transparent;
  // width: 40px;
  // height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  // min-height: 40px;
}

.edgebutton-foreignobject div.edgebutton-label {
  white-space: nowrap;
  overflow: visible;
  text-overflow: ellipsis;
  font-size: 12px;
  background-color: #fff;
  border-radius: 2px;
  padding: 4px;
  display: inline-block;
  color: #333;
  cursor: pointer;
  position: relative;
}

.react-flow__edge-path {
  stroke-width: 2px !important;
}
.react-flow__controls-button {
  border-bottom: unset !important;
  width: 28px !important;
  height: 28px !important;
  padding: 0;
  &:hover {
    background-color: unset !important;
  }
}

.react-flow__controls-button svg {
  max-height: 16px !important;
  max-width: 16px !important;
}
.react-flow__controls-button + .react-flow__controls-button {
  margin-left: 5px;
}

/**
* 修复子节点中连线的层级问题
*/
// .react-flow .react-flow__edges {
//   z-index: 10000;
// }
// .react-flow .react-flow__edgelabel-renderer {
//   z-index: 10000;
// }

.react-flow__node {
  padding: 13px;
  border: 1px solid #f5f6fb;
  border-radius: 14px;
  background: linear-gradient(to bottom, #f6f5fb 0%, #ffffff 25%, #ffffff 100%);
  box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
  &:hover {
    box-shadow: 0 2px 6px rgba(16, 24, 40, 0.08), /* 投影 1 */ 0 8px 16px rgba(0, 0, 0, 0.14); /* 投影 2 */
  }
}

.react-flow__node.selected {
  border: 2px solid var(--sparkos-primary-color);
}

.react-flow__edge-path {
  stroke-width: 2px !important;
}

// concurrent 节点有点特殊，是父节点，所以宽度设置的大一点，
.react-flow__node-concurrent {
  background: transparent;
}
