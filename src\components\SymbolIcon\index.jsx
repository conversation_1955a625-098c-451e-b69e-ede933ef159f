// const SYMBOL_OPTIONS = [
//     { label: '字符串相等', value: 'StringEquals' },
//     { label: '布尔值相等', value: 'BooleanEquals' },
//     { label: '字符串小于等于', value: 'StringLessThanEquals' },
//     { label: '字符串大于等于', value: 'StringGreaterThanEquals' },
//     { label: '字符串存在', value: 'StringExist' },
//     { label: '数值相等', value: 'NumericEquals' },
//     { label: '数值小于', value: 'NumericLessThan' },
//     { label: '数值大于', value: 'NumericGreaterThan' },
//     { label: '数值小于等于', value: 'NumericLessThanEquals' },
//     { label: '数值大于等于', value: 'NumericGreaterThanEquals' },
//     { label: '数值存在', value: 'NumericExist' },
//     { label: '布尔值存在', value: 'BooleanExist' },
//     { label: '时间戳相等', value: 'TimestampEquals' },
//     { label: '时间戳小于', value: 'TimestampLessThan' },
//     { label: '时间戳大于', value: 'TimestampGreaterThan' },
//     { label: '时间戳小于等于', value: 'TimestampLessThanEquals' },
//     { label: '时间戳大于等于', value: 'TimestampGreaterThanEquals' },
//     { label: '时间戳存在', value: 'TimestampExist' }
//   ]
import IconString from 'assets/svgs/string.svg?react'
import IconBoolean from 'assets/svgs/boolean.svg?react'
import IconNumber from 'assets/svgs/number.svg?react'
import IconObject from 'assets/svgs/object.svg?react'

import IconArrayString from 'assets/svgs/array-string.svg?react'
import IconArrayBoolean from 'assets/svgs/array-boolean.svg?react'
import IconArrayNumber from 'assets/svgs/array-number.svg?react'
import IconArrayObject from 'assets/svgs/array-object.svg?react'

export const getSymbolIcon = (type) => {
  switch (type) {
    case 'BooleanEquals':
      return (
        <>
          <IconBoolean className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">=</span>
        </>
      )
    case 'BooleanExist':
      return (
        <>
          <IconBoolean className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">true</span>
        </>
      )
    case 'StringEquals':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">=</span>
        </>
      )

    case 'StringLessThanEquals':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">≤</span>
        </>
      )
    case 'StringGreaterThanEquals':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">≥</span>
        </>
      )

    case 'StringExist':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">true</span>
        </>
      )

    case 'StringLengthEquals':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">len.=</span>
        </>
      )
    case 'StringLengthLessThan':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">len.&lt;</span>
        </>
      )
    case 'StringLengthGreaterThan':
      return (
        <>
          <IconString className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">len.&gt;</span>
        </>
      )

    case 'NumericEquals':
      return (
        <>
          <IconNumber className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">=</span>
        </>
      )
    case 'NumericLessThan':
      return (
        <>
          <IconNumber className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">&lt;</span>
        </>
      )
    case 'NumericGreaterThan':
      return (
        <>
          <IconNumber className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">&gt;</span>
        </>
      )
    case 'NumericLessThanEquals':
      return (
        <>
          <IconNumber className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">≤</span>
        </>
      )
    case 'NumericGreaterThanEquals':
      return (
        <>
          <IconNumber className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">≥</span>
        </>
      )

    case 'NumericExist':
      return (
        <>
          <IconNumber className="text-[var(--flow-desc-color)] text-[18px] mt-[1px]" />
          <span className="text-[12px]">true</span>
        </>
      )

    case 'ArrayLengthEquals':
      return (
        <>
          <span className="text-[12px]">[].</span>
          <span className="text-[12px]">len.=</span>
        </>
      )
    case 'ArrayLengthLessThan':
      return (
        <>
          <span className="text-[12px]">[].</span>
          <span className="text-[12px]">len.&lt;</span>
        </>
      )
    case 'ArrayLengthGreaterThan':
      return (
        <>
          <span className="text-[12px]">[].</span>
          <span className="text-[12px]">len.&gt;</span>
        </>
      )
    case 'ArrayLengthLessThanEquals':
      return (
        <>
          <span className="text-[12px]">[].</span>
          <span className="text-[12px]">len.≤</span>
        </>
      )
    case 'ArrayLengthGreaterThanEquals':
      return (
        <>
          <span className="text-[12px]">[].</span>
          <span className="text-[12px]">len.≥</span>
        </>
      )
    case 'ArrayExist':
      return (
        <>
          <span className="text-[12px]">[].</span>
          <span className="text-[12px]">true</span>
        </>
      )
    default:
      return null
  }
}

function SymbolIcon({ type }) {
  return (
    <div className="flex items-center text-[var(--flow-desc-color)] text-[18px] min-w-[80px]">
      {getSymbolIcon(type)}
    </div>
  )
}
export default SymbolIcon
