import Axios from 'axios'
import { message } from 'antd'
import handleLoginExpire from '@/utils/handleLoginExpire'
import { APP_ENV } from '@/utils/constant'

if (window.microApp?.getData()) {
  window.__DATA_FROM_PARENT__ = window.microApp?.getData()
  console.log('--------data come from parent-----------', window.window.microApp?.getData())
}

const BASEURL = import.meta.env.VITE_API_BASE_URL || '/iflycloud/api'

const timeout = 60000
Axios.defaults.timeout = timeout
Axios.defaults.validateStatus = function (status) {
  return status >= 200 && status < 400 //默认
}

let headers = {}
console.log('http请求前获取的cookie', window.__DATA_FROM_PARENT__?.cookie)
if (window.__DATA_FROM_PARENT__) {
  headers['X-Auto-Token'] = window.__DATA_FROM_PARENT__.cookie
}

let axios = Axios.create({
  headers,
  baseURL: BASEURL,
  validateStatus: function (status) {
    return status >= 200 && status < 400 //默认
  },
  withCredentials: true
})

axios.interceptors.request.use(
  function (config) {
    if (config.params) {
      config.params.ts = new Date().getTime()
    } else {
      config.params = {
        ts: new Date().getTime()
      }
    }
    if (localStorage.getItem('AIUI_GLOBAL_VARIABLE') && APP_ENV === 'aiui') {
      config.headers['X-Csrf-Token'] = localStorage.getItem('AIUI_GLOBAL_VARIABLE')
    }
    return config
  },
  function (error) {
    return Promise.reject(error)
  }
)

axios.interceptors.response.use(
  (response) => {
    let data = response.data
    if (
      (data.code != undefined && (data.code == -3 || data.code == 100)) ||
      response.status === 401
    ) {
      handleLoginExpire()
    }
    return response
  },
  (error) => {
    console.log(error, '--------------拦截器中的error')
    if (error.status === 401 || error.response.status === 401 || error.response.data.code === 100) {
      handleLoginExpire()
    }
    return Promise.reject(error)
  }
)

const http = ({ baseURL, url, data = {}, method = 'get', timeout = 80000, config }) => {
  method = method.toLowerCase()
  let option = {
    url,
    method,
    headers: {
      'Content-Type': 'application/json'
    },
    timeout, // 默认超时时间
    baseURL: baseURL || BASEURL
  }
  option[method === 'get' ? 'params' : 'data'] = data
  if (method === 'get') {
    option.headers['Content-Type'] = 'application/x-www-form-urlencoded'
  }

  option = Object.assign({}, option, config)
  return new Promise((resolve, reject) => {
    axios(option)
      .then((res) => {
        if (res.status === 200) {
          if (Number(res.data.code) === 0) {
            resolve(res)
          } else if (Number(res.data.code) === -3) {
            // 
            // message.error(res.data.desc)
            reject(res)
          } else {
            reject(res)
            message.error(res.data.desc)
          }
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        console.log('网络异常，请重试...')
        reject(error)
      })
  })
}

export default ({ baseURL, url, data = {}, method = 'get', timeout = 80000, config }) => {
  return http({ baseURL, url, data, method, timeout, config })
}
