import { useState, useEffect, useMemo } from 'react'
import { useEdges, useNodes } from '@xyflow/react'
import { getTreeData } from './getTreeData'

/**
 * 自定义 Hook：管理和计算 refTreeData
 * @param {Object} node - 当前节点
 * @param {boolean} visible - 是否可见（触发条件）
 * @returns {Array} refTreeData - 计算后的引用列表-treedata结构
 */

const useRefTreeData = (node, instance, visible) => {
  const nodes = useNodes()
  const edges = useEdges()
  const treeData = useMemo(() => {
    if (!visible) return null
    return getTreeData(node, nodes, edges)
  }, [node, nodes, edges, visible])
  return treeData
}

export default useRefTreeData
