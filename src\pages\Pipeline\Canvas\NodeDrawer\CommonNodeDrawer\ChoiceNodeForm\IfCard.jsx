import {
  Drawer,
  Button,
  Space,
  Form,
  Switch,
  Select,
  Input,
  message,
  Card,
  Divider,
  Tag,
  Collapse,
  Cascader,
  Row,
  Col,
  TreeSelect
} from 'antd'
import { memo, useCallback, useEffect, useRef } from 'react'
// import Header from '../Header'
import {
  MinusCircleOutlined,
  PlusOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  HolderOutlined
} from '@ant-design/icons'
import SymbolIcon from '@/components/SymbolIcon'

import { NODETYPES, SYMBOL_OPTIONS } from '@/utils/constant'
import { DATA_TYPES_MAP } from '@/utils/constant'

import useRefTreeData from '@/hooks/useRefTreeData'
import { BlurInput, BlurTextArea } from '@/components/BlurInput'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { useDrag, useDrop } from 'react-dnd'
import ItemTypes from './ItemTypes'
import styles from './style.module.scss'

function IfCard({ form, fields, add, remove, index, id, name, node, visible, moveCard }) {
  const treeData = useRefTreeData(node, form, visible)
  const { validateChoiceInputData } = useInputDataValidate(treeData)

  const ref = useRef(null)
  const dragRef = useRef(null)
  const [, drop] = useDrop({
    accept: ItemTypes.CARD,
    hover(item, monitor) {
      if (!ref.current) {
        return
      }
      const dragIndex = item.index
      const hoverIndex = index
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return
      }
      // Determine rectangle on screen
      const hoverBoundingRect = ref.current.getBoundingClientRect()
      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
      // Determine mouse position
      const clientOffset = monitor.getClientOffset()
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return
      }
      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex)
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex
    }
  })
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.CARD, // ✅ 必填
    item: { id, index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })
  const opacity = isDragging ? 0 : 1
  drop(ref)
  drag(dragRef)

  const title = (
    <>
      {fields.length > 1 && <HolderOutlined className={styles.holder} ref={dragRef} />}
      {index === 0 ? (
        <>
          <span>如果</span> <span className={styles.tag}>优先级 {index + 1}</span>
        </>
      ) : (
        <>
          <span>否则如果</span>
          <span className={styles.tag}>优先级 {index + 1}</span>
        </>
      )}
    </>
  )

  const conditionOptionRender = (option) => {
    return (
      <div className="flex items-center">
        <SymbolIcon type={option.value} />
        <span>{option.label}</span>
      </div>
    )
  }

  const treeTitleRender = (nodeData) => {
    const Component = nodeData?.type ? DATA_TYPES_MAP[nodeData?.type]?.component : null
    return (
      <div className="flex items-center flex-nowrap">
        {Component && <Component className="text-[var(--flow-desc-color)] mt-[3px] text-[18px]" />}
        <span className="ml-1">{nodeData?.title}</span>
      </div>
    )
  }

  const getTypeByValue = (value, data) => {
    if (!data) {
      return ''
    }
    for (const node of data) {
      if (node.key === value) return node.type
      if (node.children) {
        const found = getTypeByValue(value, node.children)
        if (found) return found
      }
    }
    return ''
  }

  const onRefVaribleChange = (name, ruleName, val) => {
    // console.log('onRefVaribleChange', name, ruleName, val)
    // console.log('onRefVaribleChange--treeData', treeData)
    const type = getTypeByValue(val, treeData)
    const variableObj = form.getFieldValue(['choice', name, 'rule', ruleName]) || {}
    const { func } = variableObj
    if (func) {
      const funcType = SYMBOL_OPTIONS.find((opt) => opt.value === func)?.type
      if (type !== funcType) {
        form.setFieldValue(['choice', name, 'rule', ruleName], { ...variableObj, func: undefined })
        onValuesChange(form.getFieldValue('choice'), form.getFieldsValue())
      }
    }
  }

  const onRefFuncChange = (name, ruleName, val) => {
    if (val && val.includes('Exist')) {
      const variableObj = form.getFieldValue(['choice', name, 'rule', ruleName]) || {}
      const { const: cst } = variableObj
      if (cst) {
        form.setFieldValue(['choice', name, 'rule', ruleName], { ...variableObj, const: '' })
        onValuesChange(form.getFieldValue('choice'), form.getFieldsValue())
      }
    }
  }

  const conditionLabelRender = (props) => {
    return <SymbolIcon type={props.value} />
  }

  return (
    <div key={id} className={styles.choiceCard} ref={ref} style={{ opacity }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 10
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center'
          }}
        >
          {title}
        </div>
        {fields.length > 1 && (
          <MinusCircleOutlined
            onClick={() => remove(name)}
            size="small"
            className="text-[var(--flow-desc-color)]"
          />
        )}
      </div>
      <div className={styles.ruleLogic}>
        <Form.Item noStyle shouldUpdate>
          {() => {
            return (
              <Form.Item
                initialValue="is"
                name={[name, 'type']}
                rules={[{ required: true, message: '不可为空' }]}
                style={{ marginBottom: 0 }}
              >
                {(form.getFieldValue(['choice', name, 'rule']) || []).length > 1 && (
                  <Select
                    style={{ width: 54 }}
                    placeholder="请选择"
                    variant="borderless"
                    size="small"
                  >
                    <Option value="is">且</Option>
                    <Option value="or">或</Option>
                  </Select>
                )}
              </Form.Item>
            )
          }}
        </Form.Item>
      </div>

      {/* 第二层 Form.List */}
      <Form.List
        name={[name, 'rule']}
        initialValue={[
          {
            variable: undefined,
            const: undefined,
            func: undefined
          }
        ]}
      >
        {(ruleFields, { add: addRule, remove: removeRule }) => {
          const showType = ruleFields.length > 1 // Show "规则关系" dropdown only if rule count > 1

          return (
            <>
              <Row align="middle" style={{ marginBottom: 8 }} className="relative">
                {showType && (
                  <Col span={3}>
                    {/* <Form.Item
                          name={[name, 'type']}
                          rules={[{ required: true, message: '请选择规则关系' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <Select
                            style={{ width: 60 }}
                            placeholder="请选择"
                            variant="borderless"
                          >
                            <Option value="is">且</Option>
                            <Option value="or">或</Option>
                          </Select>
                        </Form.Item> */}
                  </Col>
                )}
                {showType && (
                  <Col span={3} className={styles.branchLine}>
                    <div className={styles.branchLineInner}>
                      <div className={styles.branchLineInnerTop}></div>
                      <div className={styles.branchLineInnerBottom}></div>
                    </div>
                  </Col>
                )}
                <Col span={showType ? 21 : 24} style={{ position: 'relative' }}>
                  {ruleFields.map(({ key: ruleKey, name: ruleName }) => {
                    const refVariable = form.getFieldValue(['choice', name, 'rule', ruleName])
                    // console.log('refVariable---------------------', refVariable)
                    const { variable, func } = refVariable || {}
                    let symbolOptions = []
                    if (variable) {
                      const type = getTypeByValue(variable, treeData)
                      // console.log('ref Variable---------------------', type)
                      symbolOptions = SYMBOL_OPTIONS.filter((it) => it.type === type)
                    }

                    return (
                      <Space key={ruleKey} align="start" style={{ display: 'flex' }}>
                        <Form.Item
                          name={[ruleName, 'variable']}
                          rules={[{ validator: validateChoiceInputData }]}
                          style={{ marginBottom: 4 }}
                        >
                          <TreeSelect
                            fieldNames={{
                              label: 'title',
                              value: 'key',
                              children: 'children'
                            }}
                            style={{
                              width: '100%'
                            }}
                            dropdownStyle={{
                              maxHeight: 400,
                              overflow: 'auto'
                            }}
                            treeData={treeData}
                            placeholder="选择"
                            treeDefaultExpandAll
                            treeLine
                            popupMatchSelectWidth={false}
                            treeTitleRender={treeTitleRender}
                            onChange={(val) => onRefVaribleChange(name, ruleName, val)}
                          />
                        </Form.Item>
                        <Form.Item
                          name={[ruleName, 'func']}
                          rules={[{ required: true, message: '条件不可为空' }]}
                          style={{ marginBottom: 4, textAlign: 'center' }}
                        >
                          <Select
                            style={{ width: 80 }}
                            placeholder="选择条件"
                            popupMatchSelectWidth={false}
                            labelRender={conditionLabelRender}
                            optionRender={conditionOptionRender}
                            onChange={(val) => onRefFuncChange(name, ruleName, val)}
                          >
                            {symbolOptions.map((item) => (
                              <Option key={item.label} value={item.value}>
                                {item.label}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>

                        {func && func.includes('Exist') ? null : (
                          <Form.Item
                            name={[ruleName, 'const']}
                            rules={[{ required: true, message: '参数值不可为空' }]}
                            style={{ marginBottom: 4 }}
                          >
                            <BlurInput placeholder="输入参数值" />
                          </Form.Item>
                        )}

                        {/* Remove Rule Button */}
                        {ruleFields.length > 1 && (
                          <div style={{ marginTop: 5 }}>
                            <MinusCircleOutlined
                              onClick={() => removeRule(ruleName)}
                              size="small"
                              className="text-[var(--flow-desc-color)] "
                            />
                          </div>
                        )}
                      </Space>
                    )
                  })}
                  <Button
                    type="dashed"
                    style={{
                      position: 'absolute',
                      zIndex: 1,
                      marginTop: 8,
                      left: 0
                    }}
                    onClick={() => addRule()}
                    icon={<PlusOutlined />}
                    size="small"
                  ></Button>
                </Col>
              </Row>
            </>
          )
        }}
      </Form.List>
    </div>
  )
}

export default IfCard
