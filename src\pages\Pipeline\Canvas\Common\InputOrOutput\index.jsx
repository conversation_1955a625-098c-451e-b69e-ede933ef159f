import { EllipsisOutlined } from '@ant-design/icons'
import { Popover } from 'antd'
import { useState } from 'react'
import { useLayoutEffect, useRef } from 'react'
import styles from '../../form.module.scss'
import InputOrOutputTag from '../InputOrOutputTag'

function InputOrOutput({ inputOrOutput, keyProp = 'key', label = '', treeData }) {
  const inputOrOutputRef = useRef(null)

  const validInputOrOutput = (inputOrOutput || []).filter(Boolean)
  const inputOrOutputInnerDom =
    validInputOrOutput.length > 0 ? (
      validInputOrOutput.map((o, i) => {
        return <InputOrOutputTag key={i} inputOrOutput={o} keyProp={keyProp} treeData={treeData} />
      })
    ) : (
      <span className="text-[var(--flow-desc-color)]">{`未配置${label}`}</span>
    )
  const inputOrOutputContent = (
    <div className={styles['values-popper']}>{inputOrOutputInnerDom}</div>
  )

  const [showOverlay, setShowOverlay] = useState(false)

  useLayoutEffect(() => {
    if (inputOrOutputRef.current) {
      const clientWidth = inputOrOutputRef.current.clientWidth
      let childTotalWidth = 0
      Array.from(inputOrOutputRef.current.children).forEach((child) => {
        childTotalWidth = childTotalWidth + child.clientWidth + 4
      })
      // console.log('totalWidth', inputOrOutputRef.current.children, childTotalWidth)
      if (childTotalWidth >= clientWidth) {
        setShowOverlay(true)
      }
    } else {
      // setShowOverlay(false)
    }
  })

  return (
    <div className={styles.item}>
      <div className={styles.label}>{label}</div>
      <div
        className={styles.values}
        ref={inputOrOutputRef}
        style={{ justifyContent: showOverlay ? 'flex-start' : 'flex-end' }}
      >
        {inputOrOutputInnerDom}
        {(inputOrOutput || []).length > 1 && showOverlay && (
          <div className={styles.overlay}>
            <div className={styles.mask}></div>
            <div className={styles['value-item']}>
              <Popover
                placement="top"
                arrow={true}
                trigger="hover"
                title={null}
                content={inputOrOutputContent}
              >
                <EllipsisOutlined className="cursor-pointer" />
              </Popover>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
export default InputOrOutput
