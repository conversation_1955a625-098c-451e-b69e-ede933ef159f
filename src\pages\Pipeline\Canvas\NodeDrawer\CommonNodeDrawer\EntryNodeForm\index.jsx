import {
  Drawer,
  Button,
  Space,
  Form,
  Switch,
  Select,
  Input,
  message,
  Card,
  Divider,
  Tag,
  Collapse,
  Cascader,
  Tooltip
} from 'antd'
import { memo, useCallback, useEffect } from 'react'

import { useReactFlow, useNodesData, getOutgoers } from '@xyflow/react'
import { NODETYPES } from '@/utils/constant'
import FormItemRenderer from '@/components/FormItemRender'

import useStore from '@/store.js'
import useRefUpdate from '@/hooks/useRefUpdate'
import withDividingLine from '@/components/WithDividingLine'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import { QuestionCircleFilled, SignatureOutlined } from '@ant-design/icons'

const genLabelTitle = (title) => {
  return <span className="text-[var(--flow-title-color2)] text-[14px] font-semibold">{title}</span>
}

const moveTextToFront = (arr) => {
  const index = arr.findIndex((item) => item.name === 'text')
  return index === -1 ? [...arr] : [arr[index], ...arr.slice(0, index), ...arr.slice(index + 1)]
}
export default memo((props) => {
  const { node, onClose, visible, setNodes } = props
  const [form] = Form.useForm()
  const { updateNodeData } = useReactFlow()
  const { debouncedSetDirty } = useStore()
  const nodeData = useNodesData(node.id)
  const { updateRefName } = useRefUpdate(node)
  const hasExpand = (nodeData?.data?.input || [])
    .filter(Boolean)
    .some((val) => (val.schema || []).length > 0)

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log('onValuescChang', allValues)
    updateNodeData(node.id, allValues)
    debouncedSetDirty()
  }

  const onNameChange = (path, value) => {
    console.log('onNameChange', path, value)
    updateRefName(path, value)
  }

  const onParamFillClick = () => {
    const inputVal = [
      {
        name: 'text',
        type: 'string'
      },
      {
        type: 'object',
        name: 'intent',
        schema: [
          {
            type: 'object',
            name: 'pk_source',
            schema: [
              {
                type: 'string',
                name: 'intent'
              }
            ]
          },
          {
            type: 'string',
            name: 'original_query'
          },
          {
            type: 'string',
            name: 'query'
          },
          {
            type: 'string',
            name: 'next_type'
          },
          {
            type: 'string',
            name: 'nlp_value'
          },
          {
            type: 'string',
            name: 'quick_reply'
          }
        ]
      }
    ]

    form.setFieldsValue({
      input: moveTextToFront(inputVal)
    })
    updateNodeData(node.id, {
      input: moveTextToFront(inputVal)
    })
    debouncedSetDirty()
  }

  useEffect(() => {
    console.log('node.data', node.data)

    form.setFieldsValue({
      input: moveTextToFront(node.data?.input || [])
    })
    //  校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      setTimeout(() => {
        form.validateFields()
      })
    }
  }, [node, visible, form])

  let inputFormListAdd

  const genExtra = (isInput) => (
    <Space style={{ display: 'flex', marginBottom: 0 }} align="start" size={8}>
      <Tooltip title="一键设置参数">
        <SignatureOutlined
          className={'text-[var(--flow-desc-color)]'}
          onClick={(e) => {
            e.stopPropagation()
            onParamFillClick()
          }}
        />
      </Tooltip>

      <Button
        type="dashed"
        onClick={(e) => {
          e.stopPropagation()
          inputFormListAdd && inputFormListAdd()
        }}
        size="small"
      >
        +
      </Button>
    </Space>
  )

  const items = [
    {
      key: '9',
      // label: genLabelTitle(node.type === NODETYPES.ENTRY ? '输入' : '输出'),
      label: (
        <>
          输入 &nbsp;
          <Tooltip title="单独发布api支持添加自定义参数且是cbm开头；用于链路中只支持额外参数cbm_agent、intent。">
            <QuestionCircleFilled className="text-[var(--flow-desc-color)]" />
          </Tooltip>
        </>
      ),
      children: (
        <Form.List name="input">
          {(subFields, { add, remove }) => {
            inputFormListAdd = add

            return (
              <div className="relative">
                {subFields.map((subField, index) => (
                  <Space align="start" size={4} key={subField.name}>
                    <FormItemRenderer
                      field={subField}
                      remove={remove}
                      form={form}
                      path={['input', subField.name]}
                      closeable={index > 0}
                      disabled={index === 0}
                      hasExpand={hasExpand}
                      onNameChange={onNameChange}
                    />
                  </Space>
                ))}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(false)
    }
  ]
    .filter(Boolean)
    .map((item) => {
      return {
        ...item,
        children: withDividingLine(item.children)
      }
    })

  return (
    <Form
      form={form}
      name="basic"
      onFinish={onFinish}
      onFinishFailed={onFinishFailed}
      autoComplete="off"
      labelWrap
      layout="vertical"
      onValuesChange={onValuesChange}
      requiredMark={false}
      className="flex-1 overflow-auto mr-[-24px] pr-0"
      preserve={false}
    >
      <Collapse
        items={items}
        defaultActiveKey={['9']}
        size="small"
        ghost
        className="pr-[20px]"
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
      />
    </Form>
  )
})
