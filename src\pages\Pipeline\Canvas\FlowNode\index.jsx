import React, { memo, useCallback } from 'react'

import { useN<PERSON>, Handle } from '@xyflow/react'
import cls from 'classnames'
import Icon from 'assets/svg/nd-switch.svg?react'
import { Tooltip, Popover } from 'antd'
import { ExclamationCircleOutlined, EditOutlined } from '@ant-design/icons'
import TextEdit from '@/components/TextEdit'
import Form from './Form'
import NodeHeader from '@/components/NodeHeader'
import { NODETYPES } from '@/utils/constant'

export default memo((props) => {
  const { data, sourcePosition, targetPosition, isConnectable, id, type } = props
  const nodes = useNodes()

  return (
    <div>
      <NodeHeader id={id} type={type} data={data} />
      <Form {...props} />
      <Handle
        type="source"
        position={sourcePosition}
        className="handle"
        id="source"
        isConnectable={isConnectable}
      />
      <Handle
        type="target"
        position={targetPosition}
        className="handle"
        id="target"
        isConnectable={isConnectable}
      />
    </div>
  )
})
