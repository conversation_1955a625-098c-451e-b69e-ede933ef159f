import { Mo<PERSON>, List, Button, Table, Space, Flex, Input, Popover } from 'antd'
import { useState, useEffect } from 'react'
import styles from './style.module.scss'
import ajax from '@/utils/http'
import classNames from 'classnames'
import eventBus from '@/utils/eventBus'
import { NODETYPES } from '@/utils/constant'
import KeyProp from '@/components/KeyProp'
import { NodeAdapterFactory } from '@/utils/adapter/NodeAdapterFactory'
import { APP_ENV } from '@/utils/constant'
import { useNavigate } from 'react-router-dom'
import microAppRouter from '@/hooks/useMicroAppRouter.js'

const { Search } = Input

const namesMap = {
  aiui: '信源',
  auto: '智能体',
  base: '插件'
}

const urlsMap = {
  aiui: '/aiui-agent/openPlatform/source/apiAll',
  // aiui: '/aiui-agent/plugin/published/all?containSchema=true',
  auto: '/aiui-agent/plugin/published/all?containSchema=true',
  base: '/aiui-agent/plugin/published/all?containSchema=true'
}

const transformSchemaData = (data = []) => {
  return data.map((item) => {
    const type = item.type === 'array' ? `array<${item?.children?.[0]?.type}>` : item.type
    let obj = {
      type,
      name: item.name
    }
    if (type === 'object') {
      obj.schema = transformSchemaData(item.children)
    }
    if (type === 'array<object>') {
      obj.schema = transformSchemaData(item.children?.[0]?.children)
    }
    return obj
  })
}

function InsertApiModal({ open, onCancel }) {
  const { baseRouter } = microAppRouter()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState()
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 5,
      pageSizeOptions: [5, 10, 50, 100],
      showSizeChanger: true
    }
  })

  const [searchVal, setSearchVal] = useState('')

  const [toolSquare, setToolSquare] = useState(false)

  const handleTableChange = (pagination, filters, sorter) => {
    setTableParams({
      pagination
    })
  }

  const onAdd = (record) => {
    if (APP_ENV === 'aiui') {
      onAddAiui(record)
    } else {
      onAddNormal(record)
    }
  }

  const onAddNormal = (record) => {
    console.log('onAdd-------------', record)
    console.log('onAdd------------- json record.extraParam', JSON.parse(record.extraParam))

    const { pluginType, toolConfig, pluginName, toolName, pluginDesc } = record
    // 根据record构造 APITemlate
    // TODO:
    let config = {}
    let api_type

    if (pluginType === 1) {
      // openapi
      // config.template = toolConfig?.schemaFormat
      config['openapi'] = {
        template: toolConfig?.schemaFormat
      }
      api_type = 'openapi'
    } else {
      // coze xingcheng 汽车等
      console.log('record.extraParam', record.extraParam)
      config = JSON.parse(record.extraParam)
      api_type = Object.keys(config)?.[0]
      // 
    }

    let template = {
      ability: {
        api_type,
        name: '',
        id: '',
        type: 'PLUGIN',
        config
      },
      official: true,
      audit: true,
      description: pluginDesc,
      flow_in: false,
      flow_out: false,
      input: {
        text: {}
      },
      nodeType: 'plugin',
      // output 待定
      output: { nlp: { desc: { format: 'plain', schema: { type: 'string' } } } },
      title: pluginName,
      type: 'task',
      dist: true
    }
    // 汽车的pluginType为0
    // if (pluginType === 0) {
    //   // template.input['cbm_domain'] = { ref: '', path: '{"intent":"PLAY"}' }
    //   template.input['cbm_domain'] = {}
    //   template.input['cbm_agent'] = {}
    // }
    let data = {}
    const type = NODETYPES.API

    let param = {}
    // 通过适配器转换数据
    const adapter = NodeAdapterFactory.getAdapter(type)
    if (adapter) {
      param = adapter.fromBackToFront(template)
    }

    if (pluginType === 1) {
      // openAPI
      const parsedWebSchema = JSON.parse(toolConfig?.webSchema || '{}')

      const input = (parsedWebSchema.toolRequestInput || []).map((item) => {
        const type = item.type === 'array' ? `array<${item?.children?.[0]?.type}>` : item.type
        return {
          // 冗余字段
          key: item.name,
          keyProp: {
            name: item.name,
            required: item.required,
            location: item.location,
            type
          },
          value: {
            ref: 'const',
            path: item.defaultValue
          }
        }
      })
      const output = transformSchemaData(parsedWebSchema.toolRequestOutput || [])

      data = {
        ...param?.data,
        input,
        output: [{ name: 'nlp', type: 'object', schema: output }]
      }
    } else if (pluginType === 0) {
      data = {
        ...param?.data,
        input: [
          {
            key: 'text',
            keyProp: {
              name: 'text',
              required: true,
              type: 'string'
            },
            value: {
              ref: 'const',
              path: ''
            }
          },
          {
            key: 'cbm_domain',
            keyProp: {
              name: 'cbm_domain',
              required: true,
              type: 'string'
            },
            value: {
              ref: 'const',
              path: ''
            }
          },
          {
            key: 'cbm_agent',
            keyProp: {
              name: 'cbm_agent',
              required: false,
              type: 'string'
            },
            value: {
              ref: 'const',
              path: ''
            }
          }
        ]
      }
    } else {
      data = {
        ...param?.data
      }
    }

    eventBus.emit('INSERT_NODE', { type, position: null, data })

    onCancel?.()
  }

  const onAddAiui = (record) => {
    const { webSchema, toolName, pluginDesc, schemaFormat } = record
    // 根据record构造 APITemlate
    // TODO:
    let config = {}
    let api_type

    config['openapi'] = {
      template: schemaFormat
    }
    api_type = 'openapi'

    let template = {
      ability: {
        api_type,
        name: '',
        id: '',
        type: 'PLUGIN',
        config
      },
      official: true,
      audit: true,
      description: '',
      flow_in: false,
      flow_out: false,
      input: {
        text: {}
      },
      nodeType: 'plugin',
      // output 待定
      output: { nlp: { desc: { format: 'plain', schema: { type: 'string' } } } },
      title: toolName,
      type: 'task',
      dist: true
    }

    let data = {}
    const type = NODETYPES.API

    let param = {}
    // 通过适配器转换数据
    const adapter = NodeAdapterFactory.getAdapter(type)
    if (adapter) {
      param = adapter.fromBackToFront(template)
    }

    if (true) {
      // openAPI
      const parsedWebSchema = JSON.parse(webSchema || '{}')

      const input = (parsedWebSchema.toolRequestInput || []).map((item) => {
        const type = item.type === 'array' ? `array<${item?.children?.[0]?.type}>` : item.type
        return {
          // 冗余字段
          key: item.name,
          keyProp: {
            name: item.name,
            required: item.required,
            location: item.location,
            type
          },
          value: {
            ref: 'const',
            path: item.defaultValue
          }
        }
      })
      const output = transformSchemaData(parsedWebSchema.toolRequestOutput || [])

      data = {
        ...param?.data,
        input,
        output: [{ name: 'nlp', type: 'object', schema: output }]
      }
    }

    eventBus.emit('INSERT_NODE', { type, position: null, data })

    onCancel?.()
  }

  const columns =
    APP_ENV === 'aiui'
      ? [
          {
            title: namesMap[APP_ENV],
            dataIndex: 'pluginName',
            key: 'pluginName',
            width: 360,
            render: (_, record) => (
              <div>
                <p className="text-[var(--flow-title-color)]">{record.toolName}</p>
                {record.pluginDesc && (
                  <p className="text-[var(--flow-desc-color)] " style={{ wordBreak: 'break-word' }}>
                    {record.pluginDesc}
                  </p>
                )}
              </div>
            )
          },
          {
            title: '发布时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            width: 150,
            render: (_, record) => <div>{record.updateTime || record.createTime}</div>
          },

          {
            title: '操作',
            key: 'action',
            width: 100,
            render: (_, record) => {
              const parsedWebSchema = JSON.parse(record?.webSchema || '{}')
              const input = (parsedWebSchema.toolRequestInput || []).map((item) => {
                const type =
                  item.type === 'array' ? `array<${item?.children?.[0]?.type}>` : item.type
                return {
                  // 冗余字段
                  key: item.name,
                  keyProp: {
                    name: item.name,
                    required: item.required,
                    type,
                    description: item.description
                  },
                  value: {
                    ref: 'const',
                    path: item.defaultValue
                  }
                }
              })
              const content = (
                <div className="p-[5px] w-[400px] max-h-[320px] overflow-auto">
                  <div className="text-[var(--flow-title-color)]">{record.toolName}</div>
                  {/* <div
                    className="text-[var(--flow-desc-color)] mb-[10px] "
                    style={{ wordBreak: 'break-word' }}
                  >
                    {record.pluginDesc}
                  </div> */}
                  <ul className={styles.paramWrap}>
                    {input.map((it, i) => {
                      return (
                        <li key={i}>
                          <KeyProp value={it.keyProp} />
                          <div className="text-[var(--flow-desc-color)]">
                            {it.keyProp.description}
                          </div>
                        </li>
                      )
                    })}
                  </ul>
                </div>
              )
              return (
                <Space>
                  <Button onClick={() => onAdd(record)}>添加</Button>
                  {input.length > 0 && (
                    <Popover trigger="hover" title={null} content={content}>
                      <Button type="link">参数</Button>
                    </Popover>
                  )}
                </Space>
              )
            }
          }
        ]
      : [
          {
            title: namesMap[APP_ENV],
            dataIndex: 'pluginName',
            key: 'pluginName',
            width: 360,
            render: (_, record) => (
              <div>
                <p className="text-[var(--flow-title-color)]">{record.pluginName}</p>
                {record.pluginDesc && (
                  <p className="text-[var(--flow-desc-color)] " style={{ wordBreak: 'break-word' }}>
                    {record.pluginDesc}
                  </p>
                )}
              </div>
            )
          },
          {
            title: '发布时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            width: 120,
            render: (_, record) => <div>{record.updateTime || record.createTime}</div>
          },

          {
            title: '操作',
            key: 'action',
            width: 100,
            render: (_, record) => {
              const parsedWebSchema = JSON.parse(record.toolConfig?.webSchema || '{}')
              const input = (parsedWebSchema.toolRequestInput || []).map((item) => {
                const type =
                  item.type === 'array' ? `array<${item?.children?.[0]?.type}>` : item.type
                return {
                  // 冗余字段
                  key: item.name,
                  keyProp: {
                    name: item.name,
                    required: item.required,
                    type,
                    description: item.description
                  },
                  value: {
                    ref: 'const',
                    path: item.defaultValue
                  }
                }
              })
              const content = (
                <div className="p-[5px] w-[400px] max-h-[320px] overflow-auto">
                  <div className="text-[var(--flow-title-color)]">{record.pluginName}</div>
                  <div
                    className="text-[var(--flow-desc-color)] mb-[10px] "
                    style={{ wordBreak: 'break-word' }}
                  >
                    {record.pluginDesc}
                  </div>
                  <ul className={styles.paramWrap}>
                    {input.map((it, i) => {
                      return (
                        <li key={i}>
                          <KeyProp value={it.keyProp} />
                          <div className="text-[var(--flow-desc-color)]">
                            {it.keyProp.description}
                          </div>
                        </li>
                      )
                    })}
                  </ul>
                </div>
              )
              return (
                <Space>
                  <Button onClick={() => onAdd(record)}>添加</Button>
                  {input.length > 0 && (
                    <Popover trigger="hover" title={null} content={content}>
                      <Button type="link">参数</Button>
                    </Popover>
                  )}
                </Space>
              )
            }
          }
        ]

  useEffect(() => {
    if (open) {
      fetchData({})
    } else {
      setSearchVal('')
    }
  }, [open, toolSquare])

  useEffect(() => {
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1
      }
    })
    setSearchVal('')
  }, [toolSquare])

  const onSearch = (value, _e, info) => {
    console.log('onSearch', value, _e, info)
    setTableParams({
      ...tableParams,
      pagination: {
        ...tableParams.pagination,
        current: 1
      }
    })
    fetchData({ searchKey: value || '' })
  }

  const fetchData = ({ searchKey }) => {
    setLoading(true)

    ajax({
      url: urlsMap[APP_ENV],
      data: {
        dataType: toolSquare ? 0 : 1,
        searchKey: searchKey
      },
      method: 'get'
    }).then((res) => {
      console.log('测试结果：', res)
      if (res.data.code === '0') {
        setData(
          (res.data?.data || []).map((item) => {
            return {
              ...item
            }
          })
        )
        setLoading(false)
      }
    })
  }

  const onInputSearchChange = (e) => {
    setSearchVal(e.target.value)
  }

  const onNavClick = () => {
    if (APP_ENV === 'auto') {
      // 跳转 创建智能体 页面
      // window.microApp?.dispatch({ type: 'NAVIGATE', target:  'CREATE_AGENT'})
      // window.rawWindow?.open('https://cividev-in.iflytekauto.cn/cloud-web/#/agent/plugin', '_self')
      // window.microApp.getData()?.navigate(`/agent/plugin`, { type: '_self' })
      baseRouter.open(`/agent/plugin`)
    } else if (APP_ENV === 'aiui') {
      window.parent?.postMessage({ type: 'LINK_CREATE_SOURCE' }, '*')
    } else {
      // navigate(`${import.meta.env.VITE_ROUTER_BASE_URL}/workspace/plugin`)
      window.location.href = `${import.meta.env.VITE_ROUTER_BASE_URL}/workspace/plugin`
    }
  }

  return (
    <Modal
      title={null}
      closable={false}
      open={open}
      onCancel={onCancel}
      footer={null}
      destroyOnClose={true}
      width={1200}
      wrapClassName={styles.customModal}
    >
      <div className="bg-[#fff] text-second font-medium text-md flex w-full h-full overflow-hidden rounded-[8px]">
        <div className="w-[240px] bg-[#f5f6fb] p-[20px] flow-tool-modal-left">
          <div className="text-[18px] font-semibold flex items-center gap-2">
            添加{namesMap[APP_ENV]}
          </div>
          <Button
            type="primary"
            className="w-full text-[#fff]  flex items-center gap-2 mt-2"
            onClick={onNavClick}
          >
            新建{namesMap[APP_ENV]}
          </Button>
          <div className="flex flex-col gap-2 mt-6">
            <div
              className={classNames(styles['create-tool-tab-normal'], {
                [styles['create-tool-tab-active']]: toolSquare === false
              })}
              onClick={() => {
                setToolSquare(false)
              }}
            >
              <span>我创建的</span>
            </div>
            <div
              className={classNames(styles['create-tool-tab-normal'], {
                [styles['create-tool-tab-active']]: toolSquare === true
              })}
              onClick={() => {
                setToolSquare(true)
              }}
            >
              <span>官方{namesMap[APP_ENV]}</span>
            </div>
          </div>
        </div>
        <div className="flex-1 h-full  overflow-hidden p-[20px]">
          <Flex justify="flex-end" style={{ marginBottom: 20 }}>
            <Search
              placeholder="输入名称关键字搜索"
              onSearch={onSearch}
              style={{ width: 300 }}
              allowClear
              onChange={onInputSearchChange}
              value={searchVal}
            />
          </Flex>
          <Table
            columns={columns}
            scroll={{ y: 400 }}
            rowKey={(record) => record.id}
            dataSource={data}
            pagination={false}
            loading={loading}
            onChange={handleTableChange}
          />
        </div>
      </div>
    </Modal>
  )
}
export default InsertApiModal
