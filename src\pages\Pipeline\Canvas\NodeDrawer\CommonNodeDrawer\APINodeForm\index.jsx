import {
  But<PERSON>,
  <PERSON>lapse,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Space,
  Switch
} from 'antd'

import React, { memo, useCallback, useEffect, useState } from 'react'
import { CheckOutlined, CloseCircleOutlined, CloseOutlined } from '@ant-design/icons'

import { useReactFlow, useNodesData, useNodeConnections } from '@xyflow/react'

import FormItemRenderer from '@/components/FormItemRender'
import useRefTreeData from '@/hooks/useRefTreeData'
import IconCustomFlow from 'assets/svgs/cog.svg?react'
import { useModelStore } from '@/store/model'
import { useKnowledgeStore } from '@/store/knowledge'
import { DATA_TYPES_MAP, NODETYPES, ENTRY_SOURCE_ID } from '@/utils/constant'
import { getRules, outputRule } from '@/utils/rule'

import InputData from '@/components/InputData/input_data.jsx'
import useStore from '@/store.js'

import useRefUpdate from '@/hooks/useRefUpdate'
import useInputDataValidate from '@/hooks/useInputDataValidate'
import { BlurInput, BlurTextArea } from '@/components/BlurInput'
import NodeDebugDrawer from '../NodeDebugDrawer'
import eventBus from '@/utils/eventBus'
import withDividingLine from '@/components/WithDividingLine'

import IconArrow from 'assets/svgs/drop-arrow.svg?react'

import KeyProp from '@/components/KeyProp'

const genLabelTitle = (title) => {
  return <span className="text-[var(--flow-title-color2)] text-[14px] font-semibold">{title}</span>
}

export default memo((props) => {
  const { node, onClose, visible } = props
  const [form] = Form.useForm()
  const { updateNodeData } = useReactFlow()
  const { debouncedSetDirty } = useStore()
  const nodeData = useNodesData(node.id)
  const { updateRefName } = useRefUpdate(node)

  const hasExpand = (nodeData?.data?.output || [])
    .filter(Boolean)
    .some((val) => (val.schema || []).length > 0)
  console.log(
    '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++nodeData',
    nodeData
  )
  const treeData = useRefTreeData(nodeData, form, visible)
  // console.log('---------------------------------------------------------------treeData', treeData)
  const { validateInputData } = useInputDataValidate(treeData)

  // 官方的模版能力需要禁用某些
  // const isOfficial = !!node.data?.official
  const isOfficial = node.data?.api_type !== 'openapi'

  const ruleObj = getRules(node.type, { isOfficial, api_type: node.data?.api_type })

  const [nodeDebugVisible, setNodeDebugVisible] = useState(false)
  useEffect(() => {
    // 订阅事件
    eventBus.on('DEBUG_NODE', handler)
    // 清理订阅
    return () => {
      eventBus.off('DEBUG_NODE', handler)
    }
  }, [form])

  const onCloseDebug = useCallback(() => {
    setNodeDebugVisible(false)
  }, [])

  const handler = () => {
    form
      .validateFields()
      .then(() => {
        setNodeDebugVisible(true)
        updateNodeData(node.id, { debug: false })
      })
      .catch(() => {
        updateNodeData(node.id, { debug: false })
      })
  }

  const onFinish = (values) => {
    console.log('Success:', values)
  }

  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onValuesChange = (changedValues, allValues) => {
    console.log('\n\n----onValuesChange---', allValues)
    //
    updateNodeData(node.id, allValues)
    debouncedSetDirty()
  }

  const setFields = () => {
    // console.log('useEffect trigged llm drawer', node.type, node?.data)
    form.setFieldsValue({
      domain: nodeData.data?.domain,
      abilityId: nodeData.data?.abilityId,
      abilityName: nodeData.data?.abilityName,
      prompt: nodeData.data?.prompt,
      systemPrompt: nodeData.data?.systemPrompt,
      audit: !!nodeData.data?.audit,
      dist: !!nodeData.data?.dist,
      flow_in: !!nodeData.data?.flow_in,
      flow_out: !!nodeData.data?.flow_out,
      input: nodeData.data?.input,
      output: nodeData.data?.output,

      // 官方token相关信息
      token: nodeData.data?.token,
      work_id: nodeData.data?.work_id,
      work_type: nodeData.data?.work_type ? nodeData.data?.work_type : undefined,

      // 汽车模版
      url: nodeData.data?.url,
      method: nodeData.data?.method,
      auth_type: nodeData.data?.auth_type,
      auth_key: nodeData.data?.auth_key
    })
  }

  const onNameChange = (path, value) => {
    console.log('onNameChange', path, value)
    updateRefName(path, value)
  }

  useEffect(() => {
    setFields()
    //  校验过了，再次打开，需要展示校验结果
    if (node.data?.validated) {
      setTimeout(() => {
        form.validateFields()
      })
    }
  }, [nodeData, visible, form])

  useEffect(() => {
    if (nodeData.data.debug) {
      setTimeout(() => {
        handler()
      })
    }
  }, [nodeData.data.debug, form])

  let inputFormListAdd
  let outputFormListAdd

  const genExtra = (isInput) => {
    // 注意，这里的官方的含义
    // const showAdd = isOfficial ? true : isInput ? true : false
    let showAdd
    if (isInput) {
      showAdd = isOfficial
    } else {
      if (node.data?.api_type === 'auto_template') {
        showAdd = true
      } else {
        showAdd = false
      }
    }
    return (
      <Space size={0} split={<Divider type="vertical" />}>
        {!isInput && (
          <Form.Item name="dist" style={{ marginBottom: 0 }}>
            <Switch
              checkedChildren={
                <span>
                  下发
                  <CheckOutlined />
                </span>
              }
              unCheckedChildren={
                <span>
                  下发
                  <CloseOutlined />
                </span>
              }
            />
          </Form.Item>
        )}
        <Form.Item
          name={isInput ? 'flow_in' : 'flow_out'}
          initialValue={true}
          style={{ marginBottom: 0 }}
        >
          <Switch
            key="flow"
            disabled={isInput}
            checkedChildren={
              <span>
                流式
                <CheckOutlined />
              </span>
            }
            unCheckedChildren={
              <span>
                流式
                <CloseOutlined />
              </span>
            }
          />
        </Form.Item>

        {showAdd && (
          <Button
            type="dashed"
            onClick={() => {
              if (isInput) {
                inputFormListAdd({})
              } else {
                outputFormListAdd()
              }
            }}
            size="small"
          >
            +
          </Button>
        )}
      </Space>
    )
  }

  const items = [
    {
      key: '3',
      label: genLabelTitle('输入'),
      children: (
        <Form.List name="input">
          {(subFields, { add, remove }) => {
            inputFormListAdd = add
            return (
              <div className="relative">
                {subFields.map((subField) => {
                  const key = form.getFieldValue(['input', subField.name, 'key'])

                  console.log('key is', key)
                  console.log('subField', subField)
                  return (
                    <>
                      <Space
                        key={subField.key + 'ext'}
                        style={{ display: 'flex', marginBottom: 0 }}
                        align="middle"
                        size={4}
                      >
                        <Form.Item
                          name={[subField.name, 'key']}
                          style={{ marginBottom: 10, display: isOfficial ? 'block' : 'none' }}
                          rules={
                            isOfficial
                              ? [
                                  ...outputRule.name,
                                  ({ getFieldValue }) => ({
                                    validator(_, value) {
                                      if (value) {
                                        const currentIndex = subField.name

                                        const sameLevelVal = getFieldValue(['input'])
                                        for (let i = 0; i < sameLevelVal.length; i++) {
                                          if (i === currentIndex) {
                                            continue
                                          }
                                          if (sameLevelVal[i]?.key === value) {
                                            return Promise.reject(new Error('变量名不可重复'))
                                          }
                                        }

                                        // 校验该变量是否被 提示词所使用
                                        
                                      }

                                      return Promise.resolve()
                                    }
                                  })
                                ]
                              : []
                          }
                        >
                          <BlurInput placeholder="参数名" showCount maxLength={32} />
                        </Form.Item>

                        {!isOfficial && (
                          <Form.Item name={[subField.name, 'keyProp']} style={{ marginBottom: 10 }}>
                            <KeyProp />
                          </Form.Item>
                        )}
                        <Form.Item
                          name={[subField.name, 'value']}
                          style={{ marginBottom: 0, width: 198 }}
                          rules={
                            node.data?.api_type !== 'openapi' &&
                            node.data?.api_type !== 'auto_template'
                              ? [{ validator: (rule, value) => validateInputData(rule, value) }]
                              : [
                                  ({ getFieldValue }) => ({
                                    validator(_, value) {
                                      const isRequired = getFieldValue([
                                        'input',
                                        subField.name,
                                        'keyProp'
                                      ])?.required
                                      return validateInputData(_, value, !isRequired)
                                    }
                                  })
                                ]
                          }
                        >
                          <InputData options={treeData} placeholder={'请输入或引用参数值'} />
                        </Form.Item>
                        {subFields.length > 1 && (
                          <Form.Item style={{ marginBottom: 0, width: 10 }}>
                            <CloseCircleOutlined
                              onClick={() => remove(subField.name)}
                              className="text-[var(--flow-desc-color)]"
                            />
                          </Form.Item>
                        )}
                      </Space>
                    </>
                  )
                })}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(true)
    },

    node.data?.api_type === 'auto_template'
      ? {
          key: 'basic',
          label: genLabelTitle('API信息'),
          children: (
            <>
              <Form.Item name="url" label="URL" style={{ marginBottom: 10 }} rules={ruleObj['url']}>
                <BlurInput maxLength={1000} placeholder="请填写URL" disabled/>
              </Form.Item>
              <Form.Item
                name="method"
                label="方式"
                style={{ marginBottom: 10 }}
                rules={ruleObj['method']}
              >
                {/* <BlurInput maxLength={100} placeholder="填写API类型" /> */}
                <Select
                  placeholder="请选择方式"
                  disabled
                  options={[
                    {
                      value: 'get',
                      label: 'get'
                    },
                    {
                      value: 'post',
                      label: 'post'
                    }
                  ]}
                />
              </Form.Item>
              <Form.Item
                name="auth_type"
                label="授权类型"
                style={{ marginBottom: 10 }}
                rules={ruleObj['auth_type']}
              >
                <Select
                  placeholder="请选择授权类型"
                  disabled
                  options={[
                    {
                      value: 'bearer',
                      label: 'bearer'
                    }
                  ]}
                />
              </Form.Item>
              <Form.Item
                name="auth_key"
                label="授权key信息"
                style={{ marginBottom: 10 }}
                rules={ruleObj['auth_key']}
              >
                <BlurInput maxLength={500} placeholder="填写授权key信息" disabled/>
              </Form.Item>
            </>
          )
        }
      : isOfficial
      ? {
          key: 'basic',
          label: genLabelTitle('API信息'),
          children: (
            <>
              <Form.Item
                name="work_type"
                label="API类型"
                style={{ marginBottom: 10 }}
                rules={ruleObj['work_type']}
              >
                {/* <BlurInput maxLength={100} placeholder="填写API类型" /> */}
                <Select
                  placeholder="请选择API类型"
                  options={[
                    {
                      value: 'workflow',
                      label: '工作流类型'
                    },
                    {
                      value: 'bot',
                      label: '智能体类型'
                    }
                  ]}
                />
              </Form.Item>
              <Form.Item
                name="work_id"
                label="API ID"
                style={{ marginBottom: 10 }}
                rules={ruleObj['work_id']}
              >
                <BlurInput maxLength={100} placeholder="填写API唯一ID（工作流ID或智能体ID）" />
              </Form.Item>
              <Form.Item
                name="token"
                label="授权token信息"
                style={{ marginBottom: 10 }}
                rules={ruleObj['token']}
              >
                <BlurInput maxLength={500} placeholder="填写授权token信息" />
              </Form.Item>
            </>
          )
        }
      : null,

    {
      key: '11',
      label: genLabelTitle('输出'),
      children: (
        <Form.List name="output">
          {(subFields, { add, remove }) => {
            // console.log('****************subFields**************', subFields)
            outputFormListAdd = add

            return (
              <div className="relative">
                {subFields.map((subField) => (
                  <Space align="start" size={4} key={subField.key}>
                    <FormItemRenderer
                      field={subField}
                      remove={remove}
                      form={form}
                      path={['output', subField.name]}
                      closeable={subFields.length > 1}
                      hasExpand={hasExpand}
                      onNameChange={onNameChange}
                      isTop={node.data?.api_type === 'auto_template' ? false : true}
                      disabled={!isOfficial}
                    />
                    {/* <Form.Item style={{ marginBottom: 8, width: 20 }}>
                      <CloseCircleOutlined
                        onClick={() => remove(subField.name)}
                        className="text-[var(--flow-desc-color)]"
                      />
                    </Form.Item> */}
                  </Space>
                ))}
              </div>
            )
          }}
        </Form.List>
      ),
      extra: genExtra(false)
    }
  ]
    .filter(Boolean)
    .map((item) => {
      return {
        ...item,
        children: withDividingLine(item.children)
      }
    })

  return (
    <>
      <Form
        form={form}
        name="basic"
        onFinish={onFinish}
        onFinishFailed={onFinishFailed}
        autoComplete="off"
        labelWrap
        layout="vertical"
        onValuesChange={onValuesChange}
        requiredMark={false}
        className="flex-1 overflow-auto mr-[-24px] pr-0 "
        preserve={false}
      >
        <Collapse
          items={items}
          defaultActiveKey={[
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8',
            '9',
            '10',
            '11',
            '12',
            'basic'
          ]}
          size="small"
          collapsible={'icon'}
          ghost
          className="pr-[20px]"
          expandIcon={({ isActive }) => (
            <IconArrow
              style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
            ></IconArrow>
          )}
        />
      </Form>

      <NodeDebugDrawer {...props} nodeDebug={nodeDebugVisible} onCloseDebug={onCloseDebug} />
    </>
  )
})
